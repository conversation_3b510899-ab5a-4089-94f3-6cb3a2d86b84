#!/usr/bin/env python3
"""
Complete Actor System Test
Tests the entire Actor system with real TikTok account login and scraping
"""

import os
import sys
import django
import json
import time
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from actor.models import ActorAccount, ActorTask, ActorScrapedData
from actor.services.actor_service import ActorService

class CompleteActorSystemTest:
    def __init__(self):
        self.actor_service = ActorService()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # Test credentials
        self.test_username = "grafisone"
        self.test_password = "Puyol@102410"
        self.search_keyword = "prabowo"
        
        # Get or create test user
        self.user, created = User.objects.get_or_create(
            username='test_actor_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Actor'
            }
        )
        if created:
            self.user.set_password('testpass123')
            self.user.save()
            
        print(f"🎭 Starting Complete Actor System Test")
        print(f"📅 Timestamp: {self.test_results['timestamp']}")
        print(f"👤 Test User: {self.user.username}")
        print(f"🎯 TikTok Account: {self.test_username}")
        print(f"🔍 Search Keyword: {self.search_keyword}")
        print("=" * 60)

    def log_test_result(self, test_name, success, message, data=None):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results['tests'][test_name] = {
            'success': success,
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }

    def test_1_create_actor_account(self):
        """Test 1: Create Actor Account"""
        print("\n🔧 Test 1: Creating Actor Account...")
        
        try:
            # Clean up any existing test accounts
            ActorAccount.objects.filter(
                user=self.user,
                platform='tiktok',
                username=self.test_username
            ).delete()
            
            result = self.actor_service.create_account(
                user=self.user,
                platform='tiktok',
                username=self.test_username,
                password=self.test_password,
                email='<EMAIL>'
            )
            
            if result['success']:
                self.account_id = result['account']['id']
                self.log_test_result(
                    'create_actor_account',
                    True,
                    f"Account created successfully with ID: {self.account_id}",
                    result['account']
                )
                return True
            else:
                self.log_test_result(
                    'create_actor_account',
                    False,
                    f"Failed to create account: {result.get('error', 'Unknown error')}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                'create_actor_account',
                False,
                f"Exception during account creation: {str(e)}"
            )
            return False

    def test_2_authenticate_account(self):
        """Test 2: Authenticate Actor Account"""
        print("\n🔐 Test 2: Authenticating Actor Account...")
        
        try:
            result = self.actor_service.authenticate_account(
                user=self.user,
                account_id=self.account_id
            )
            
            if result['success']:
                self.log_test_result(
                    'authenticate_account',
                    True,
                    "Account authenticated successfully",
                    {
                        'session_valid': result.get('session_valid', False),
                        'message': result.get('message', '')
                    }
                )
                return True
            else:
                self.log_test_result(
                    'authenticate_account',
                    False,
                    f"Authentication failed: {result.get('error', 'Unknown error')}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                'authenticate_account',
                False,
                f"Exception during authentication: {str(e)}"
            )
            return False

    def test_3_create_scraping_task(self):
        """Test 3: Create Scraping Task"""
        print("\n📋 Test 3: Creating Scraping Task...")
        
        try:
            result = self.actor_service.create_task(
                user=self.user,
                account_id=self.account_id,
                task_type='CONTENT_SEARCH',
                task_name=f'Test Scraping - {self.search_keyword}',
                keywords=self.search_keyword,
                max_items=10
            )
            
            if result['success']:
                self.task_id = result['task']['id']
                self.log_test_result(
                    'create_scraping_task',
                    True,
                    f"Task created successfully with ID: {self.task_id}",
                    result['task']
                )
                return True
            else:
                self.log_test_result(
                    'create_scraping_task',
                    False,
                    f"Failed to create task: {result.get('error', 'Unknown error')}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                'create_scraping_task',
                False,
                f"Exception during task creation: {str(e)}"
            )
            return False

    def test_4_execute_scraping_task(self):
        """Test 4: Execute Scraping Task"""
        print("\n🚀 Test 4: Executing Scraping Task...")
        print(f"   Searching for: '{self.search_keyword}'")
        print("   This may take a few minutes...")
        
        try:
            result = self.actor_service.execute_task(self.task_id)
            
            if result['success']:
                items_scraped = result.get('items_scraped', 0)
                self.log_test_result(
                    'execute_scraping_task',
                    True,
                    f"Task executed successfully. Items scraped: {items_scraped}",
                    {
                        'items_scraped': items_scraped,
                        'execution_time': result.get('execution_time', 'Unknown'),
                        'task_status': result.get('status', 'Unknown')
                    }
                )
                return True
            else:
                self.log_test_result(
                    'execute_scraping_task',
                    False,
                    f"Task execution failed: {result.get('error', 'Unknown error')}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                'execute_scraping_task',
                False,
                f"Exception during task execution: {str(e)}"
            )
            return False

    def test_5_verify_scraped_data(self):
        """Test 5: Verify Scraped Data in Database"""
        print("\n📊 Test 5: Verifying Scraped Data...")
        
        try:
            # Get scraped data from database
            scraped_data = ActorScrapedData.objects.filter(
                user=self.user,
                task_id=self.task_id
            )
            
            data_count = scraped_data.count()
            
            if data_count > 0:
                # Get sample data
                sample_data = scraped_data.first()
                
                self.log_test_result(
                    'verify_scraped_data',
                    True,
                    f"Found {data_count} scraped items in database",
                    {
                        'total_items': data_count,
                        'sample_data': {
                            'id': sample_data.id,
                            'data_type': sample_data.data_type,
                            'platform': sample_data.platform,
                            'platform_content_id': sample_data.platform_content_id,
                            'scraped_at': sample_data.scraped_at.isoformat(),
                            'is_complete': sample_data.is_complete,
                            'quality_score': sample_data.quality_score
                        }
                    }
                )
                return True
            else:
                self.log_test_result(
                    'verify_scraped_data',
                    False,
                    "No scraped data found in database"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                'verify_scraped_data',
                False,
                f"Exception during data verification: {str(e)}"
            )
            return False

    def test_6_data_quality_check(self):
        """Test 6: Data Quality Check"""
        print("\n🔍 Test 6: Checking Data Quality...")
        
        try:
            result = self.actor_service.get_data_labeling_stats(self.user)
            
            if result['success']:
                stats = result['stats']
                self.log_test_result(
                    'data_quality_check',
                    True,
                    f"Data quality stats retrieved successfully",
                    stats
                )
                return True
            else:
                self.log_test_result(
                    'data_quality_check',
                    False,
                    f"Failed to get data quality stats: {result.get('error', 'Unknown error')}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                'data_quality_check',
                False,
                f"Exception during data quality check: {str(e)}"
            )
            return False

    def generate_summary(self):
        """Generate test summary"""
        total_tests = len(self.test_results['tests'])
        passed_tests = sum(1 for test in self.test_results['tests'].values() if test['success'])
        failed_tests = total_tests - passed_tests
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%"
        }
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {self.test_results['summary']['success_rate']}")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! Actor System is working correctly!")
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Check the details above.")

    def save_results(self):
        """Save test results to file"""
        results_file = f"tests/actor_system_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n💾 Test results saved to: {results_file}")

    def run_all_tests(self):
        """Run all tests in sequence"""
        print("🚀 Starting Complete Actor System Test Suite...")
        
        # Run tests in sequence
        tests = [
            self.test_1_create_actor_account,
            self.test_2_authenticate_account,
            self.test_3_create_scraping_task,
            self.test_4_execute_scraping_task,
            self.test_5_verify_scraped_data,
            self.test_6_data_quality_check
        ]
        
        for test in tests:
            success = test()
            if not success:
                print(f"\n⚠️  Test failed, but continuing with remaining tests...")
            time.sleep(2)  # Brief pause between tests
        
        self.generate_summary()
        self.save_results()

if __name__ == "__main__":
    test = CompleteActorSystemTest()
    test.run_all_tests()
