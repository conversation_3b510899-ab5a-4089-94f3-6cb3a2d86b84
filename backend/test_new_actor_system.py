#!/usr/bin/env python3
"""
Comprehensive Test for New Actor System

This script tests the new Actor system functionality including:
1. Platform engine system
2. Generic account management
3. Multi-platform support
4. Data labeling system
5. Enhanced search with date filtering
6. Account selection interface
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
django.setup()

from django.contrib.auth.models import User
from actor_tiktok.models import ActorAccount, ActorTask, ActorScrapedData
from actor_tiktok.services.actor_service import ActorService
from actor_tiktok.engines import EngineRegistry

def test_platform_engines():
    """Test platform engine system."""
    print("🔧 Testing Platform Engine System...")
    
    # Test engine registry
    available_platforms = EngineRegistry.get_available_platforms()
    print(f"Available platforms: {available_platforms}")
    
    # Test TikTok engine
    tiktok_engine = EngineRegistry.get_engine('tiktok')
    if tiktok_engine:
        print("✅ TikTok engine loaded successfully")
    else:
        print("❌ Failed to load TikTok engine")
        return False
    
    # Test other platform engines
    for platform in ['instagram', 'facebook', 'twitter', 'youtube']:
        engine = EngineRegistry.get_engine(platform)
        if engine:
            print(f"✅ {platform.title()} engine loaded successfully")
        else:
            print(f"❌ Failed to load {platform} engine")
    
    return True

def test_actor_service():
    """Test Actor service functionality."""
    print("\n🎭 Testing Actor Service...")
    
    service = ActorService()
    
    # Test platform availability
    platforms = service.get_available_platforms()
    print(f"Service reports platforms: {platforms}")
    
    # Create test user
    test_user, created = User.objects.get_or_create(
        username='test_actor_user',
        defaults={'email': '<EMAIL>'}
    )
    
    if created:
        print("✅ Created test user")
    else:
        print("✅ Using existing test user")
    
    # Test account creation
    account_result = service.create_account(
        user=test_user,
        platform='tiktok',
        username='test_tiktok_user',
        password='test_password',
        email='<EMAIL>'
    )
    
    if account_result['success']:
        print("✅ Successfully created actor account")
        account_id = account_result['account_id']
    else:
        print(f"❌ Failed to create account: {account_result['error']}")
        return False
    
    # Test getting user accounts
    user_accounts = service.get_user_accounts(test_user)
    print(f"✅ Retrieved {len(user_accounts)} accounts for user")
    
    # Test task creation
    task_result = service.create_task(
        user=test_user,
        account_id=account_id,
        task_type='CONTENT_SEARCH',
        task_name='Test Search Task',
        keywords='test,actor,system',
        max_items=10
    )
    
    if task_result['success']:
        print("✅ Successfully created actor task")
        task_id = task_result['task_id']
    else:
        print(f"❌ Failed to create task: {task_result['error']}")
        return False
    
    return True

def test_data_labeling():
    """Test data labeling system."""
    print("\n🏷️  Testing Data Labeling System...")
    
    # Get test data
    test_user = User.objects.get(username='test_actor_user')
    scraped_data = ActorScrapedData.objects.filter(task__user=test_user)
    
    print(f"Found {scraped_data.count()} scraped data items")
    
    # Test data labeling
    for data in scraped_data[:3]:  # Test first 3 items
        print(f"Data item {data.id}:")
        print(f"  - Platform: {data.platform}")
        print(f"  - Account: @{data.account_username}")
        print(f"  - Data Type: {data.data_type}")
        print(f"  - Quality Score: {data.quality_score}")
        print(f"  - Complete: {data.is_complete}")
        print(f"  - Platform Content ID: {data.platform_content_id}")
    
    return True

def test_enhanced_search():
    """Test enhanced search functionality."""
    print("\n🔍 Testing Enhanced Search...")
    
    service = ActorService()
    test_user = User.objects.get(username='test_actor_user')
    
    # Get user accounts
    accounts = service.get_user_accounts(test_user, platform='tiktok')
    
    if not accounts:
        print("❌ No TikTok accounts found for testing")
        return False
    
    account_id = accounts[0]['id']
    
    # Test search with date filtering
    search_task = service.create_task(
        user=test_user,
        account_id=account_id,
        task_type='KEYWORD_SEARCH',
        task_name='Enhanced Search Test',
        keywords='technology,AI,innovation',
        max_items=5,
        start_date=(datetime.now() - timedelta(days=30)).date(),
        end_date=datetime.now().date()
    )
    
    if search_task['success']:
        print("✅ Successfully created enhanced search task with date filtering")
    else:
        print(f"❌ Failed to create enhanced search task: {search_task['error']}")
        return False
    
    return True

def test_account_selection():
    """Test account selection functionality."""
    print("\n👤 Testing Account Selection...")
    
    service = ActorService()
    test_user = User.objects.get(username='test_actor_user')
    
    # Get all accounts
    all_accounts = service.get_user_accounts(test_user)
    print(f"Total accounts: {len(all_accounts)}")
    
    # Get platform-specific accounts
    tiktok_accounts = service.get_user_accounts(test_user, platform='tiktok')
    print(f"TikTok accounts: {len(tiktok_accounts)}")
    
    # Test account details
    for account in all_accounts:
        print(f"Account: @{account['username']} ({account['platform']})")
        print(f"  - Active: {account['is_active']}")
        print(f"  - Session Valid: {account['session_valid']}")
        print(f"  - Last Login: {account['last_login']}")
    
    return True

def test_migration_compatibility():
    """Test backward compatibility with existing TikTok system."""
    print("\n🔄 Testing Migration Compatibility...")
    
    service = ActorService()
    
    # Check if there are existing TikTok accounts to migrate
    from actor_tiktok.models import TikTokUserAccount
    
    old_accounts = TikTokUserAccount.objects.all()
    print(f"Found {old_accounts.count()} existing TikTok accounts")
    
    # Test migration for first account if exists
    if old_accounts.exists():
        old_account = old_accounts.first()
        migration_result = service.migrate_tiktok_account(old_account.id)
        
        if migration_result['success']:
            print("✅ Successfully migrated TikTok account to Actor system")
        else:
            print(f"❌ Migration failed: {migration_result['error']}")
    
    return True

def test_frontend_integration():
    """Test frontend integration points."""
    print("\n🌐 Testing Frontend Integration...")
    
    # Test API endpoints would go here
    # For now, just verify the structure is in place
    
    from actor_tiktok.views import (
        get_available_platforms, create_actor_account, authenticate_actor_account,
        get_actor_accounts, create_actor_task, execute_actor_task,
        get_actor_scraped_data, get_data_labeling_stats
    )
    
    print("✅ All new Actor API endpoints are available")
    
    return True

def run_comprehensive_test():
    """Run all tests."""
    print("🚀 Starting Comprehensive Actor System Test")
    print("=" * 60)
    
    tests = [
        ("Platform Engines", test_platform_engines),
        ("Actor Service", test_actor_service),
        ("Data Labeling", test_data_labeling),
        ("Enhanced Search", test_enhanced_search),
        ("Account Selection", test_account_selection),
        ("Migration Compatibility", test_migration_compatibility),
        ("Frontend Integration", test_frontend_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            results[test_name] = False
            print(f"\n❌ ERROR in {test_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! The new Actor system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
