#!/usr/bin/env python3
"""
Test script for updated TikTok login selectors and flow
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.utils.tiktok_auth import TikTokAuthenticator
from actor_tiktok.utils.anti_detection import AntiDetectionManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_selector_detection():
    """
    Test the updated selectors without actually logging in
    """
    print("\n=== Testing Updated TikTok Login Selectors ===")
    
    # Initialize components
    anti_detection = AntiDetectionManager()
    auth = TikTokAuthenticator()
    
    driver = None
    try:
        # Set up driver with anti-detection
        driver = anti_detection.setup_driver()
        
        if not driver:
            print("❌ Failed to set up driver")
            return False
        
        print("✅ Driver setup successful")
        
        # Navigate to TikTok login page
        print("\n📍 Navigating to TikTok login page...")
        driver.get("https://www.tiktok.com/login")
        time.sleep(5)
        
        print(f"Current URL: {driver.current_url}")
        
        # Test 1: Check if login form is already available
        print("\n🔍 Test 1: Checking if login form is immediately available...")
        username_field = auth._find_element_with_dynamic_selectors(driver, 'username_input', timeout=3)
        password_field = auth._find_element_with_dynamic_selectors(driver, 'password_input', timeout=3)
        
        if username_field and password_field:
            print("✅ Login form already available - no method selection needed")
            return True
        else:
            print("ℹ️ Login form not immediately available - method selection needed")
        
        # Test 2: Look for "Use phone / email / username" option
        print("\n🔍 Test 2: Looking for 'Use phone / email / username' option...")
        phone_email_element = auth._find_element_with_dynamic_selectors(driver, 'phone_email_username', timeout=8)
        
        if phone_email_element:
            print("✅ Found 'Use phone / email / username' option")
            print(f"Element tag: {phone_email_element.tag_name}")
            print(f"Element text: {phone_email_element.text[:100]}")
            
            # Try to click it
            if auth._click_element_with_parent_fallback(driver, phone_email_element):
                print("✅ Successfully clicked phone/email/username option")
                time.sleep(3)
                
                # Test 3: Look for email/username tab
                print("\n🔍 Test 3: Looking for 'Email / Username' tab...")
                email_tab_element = auth._find_element_with_dynamic_selectors(driver, 'email_username_tab', timeout=5)
                
                if email_tab_element:
                    print("✅ Found 'Email / Username' tab")
                    print(f"Tab text: {email_tab_element.text[:100]}")
                    
                    if auth._click_element_with_parent_fallback(driver, email_tab_element):
                        print("✅ Successfully clicked email/username tab")
                        time.sleep(3)
                    else:
                        print("⚠️ Failed to click email/username tab")
                else:
                    print("ℹ️ Email/username tab not found - may already be selected")
            else:
                print("❌ Failed to click phone/email/username option")
        else:
            print("❌ 'Use phone / email / username' option not found")
        
        # Test 4: Final check for login form elements
        print("\n🔍 Test 4: Final check for login form elements...")
        username_field = auth._find_element_with_dynamic_selectors(driver, 'username_input', timeout=8)
        password_field = auth._find_element_with_dynamic_selectors(driver, 'password_input', timeout=5)
        login_button = auth._find_element_with_dynamic_selectors(driver, 'login_button', timeout=5)
        
        results = {
            'username_field': username_field is not None,
            'password_field': password_field is not None,
            'login_button': login_button is not None
        }
        
        print(f"Username field found: {'✅' if results['username_field'] else '❌'}")
        print(f"Password field found: {'✅' if results['password_field'] else '❌'}")
        print(f"Login button found: {'✅' if results['login_button'] else '❌'}")
        
        if username_field:
            print(f"Username field placeholder: {username_field.get_attribute('placeholder')}")
        if password_field:
            print(f"Password field placeholder: {password_field.get_attribute('placeholder')}")
        if login_button:
            print(f"Login button text: {login_button.text[:50]}")
        
        # Success if we found at least username and password fields
        success = results['username_field'] and results['password_field']
        
        if success:
            print("\n🎉 SUCCESS: Login flow selectors are working correctly!")
        else:
            print("\n❌ FAILURE: Could not complete login flow with current selectors")
            
        return success
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        return False
        
    finally:
        if driver:
            print("\n🧹 Cleaning up driver...")
            try:
                driver.quit()
            except Exception:
                pass

def test_selector_coverage():
    """
    Test the coverage and variety of our selectors
    """
    print("\n=== Testing Selector Coverage ===")
    
    auth = TikTokAuthenticator()
    
    # Test each selector type
    selector_types = ['phone_email_username', 'email_username_tab', 'username_input', 'password_input', 'login_button']
    
    for selector_type in selector_types:
        selectors = auth._get_dynamic_selectors(selector_type)
        css_count = len(selectors.get('css', []))
        xpath_count = len(selectors.get('xpath', []))
        total_count = css_count + xpath_count
        
        print(f"\n📊 {selector_type}:")
        print(f"  CSS selectors: {css_count}")
        print(f"  XPath selectors: {xpath_count}")
        print(f"  Total selectors: {total_count}")
        
        if total_count < 5:
            print(f"  ⚠️ Warning: Only {total_count} selectors - consider adding more")
        elif total_count >= 10:
            print(f"  ✅ Good coverage with {total_count} selectors")
        else:
            print(f"  ℹ️ Moderate coverage with {total_count} selectors")

if __name__ == "__main__":
    print("TikTok Login Selector Testing")
    print("=============================")
    
    # Test selector coverage first
    test_selector_coverage()
    
    # Test actual selector detection
    success = test_selector_detection()
    
    print("\n" + "="*50)
    if success:
        print("🎉 OVERALL RESULT: SELECTORS WORKING CORRECTLY")
    else:
        print("❌ OVERALL RESULT: SELECTORS NEED IMPROVEMENT")
    print("="*50)