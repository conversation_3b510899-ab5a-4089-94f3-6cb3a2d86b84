{"timestamp": "2025-07-18T11:08:50.731949", "tests": {"basic_navigation": {"success": true, "current_url": "https://www.tiktok.com/explore", "page_title": "Explore - Find your favourite videos on TikTok"}, "login_page_access": {"success": true, "current_url": "https://www.tiktok.com/login", "login_indicators_found": 4}, "dynamic_selectors": {"success": true, "selectors_valid": true, "element_tests": {"phone_email_username": true, "email_username_tab": false, "username_input": true, "password_input": false, "login_button": false}, "total_selectors": 5}, "javascript_error_handling": {"success": false, "error": "Message: no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.158)\nStacktrace:\n0   chromedriver                        0x00000001081f9308 chromedriver + 6148872\n1   chromedriver                        0x00000001081f08ba chromedriver + 6113466\n2   chromedriver                        0x0000000107c81e10 chromedriver + 417296\n3   chromedriver                        0x0000000107c567d0 chromedriver + 239568\n4   chromedriver                        0x0000000107d01e98 chromedriver + 941720\n5   chromedriver                        0x0000000107d20b3c chromedriver + 1067836\n6   chromedriver                        0x0000000107cf9a93 chromedriver + 907923\n7   chromedriver                        0x0000000107cc60f7 chromedriver + 696567\n8   chromedriver                        0x0000000107cc6d61 chromedriver + 699745\n9   chromedriver                        0x00000001081b6250 chromedriver + 5874256\n10  chromedriver                        0x00000001081ba289 chromedriver + 5890697\n11  chromedriver                        0x0000000108192022 chromedriver + 5726242\n12  chromedriver                        0x00000001081babff chromedriver + 5893119\n13  chromedriver                        0x0000000108180d14 chromedriver + 5655828\n14  chromedriver                        0x00000001081ddde8 chromedriver + 6036968\n15  chromedriver                        0x00000001081ddfb0 chromedriver + 6037424\n16  chromedriver                        0x00000001081f0451 chromedriver + 6112337\n17  libsystem_pthread.dylib             0x00007ff8179b31d3 _pthread_start + 125\n18  libsystem_pthread.dylib             0x00007ff8179aebd3 thread_start + 15\n"}, "popup_handling": {"success": false, "error": "Message: no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.158)\nStacktrace:\n0   chromedriver                        0x00000001081f9308 chromedriver + 6148872\n1   chromedriver                        0x00000001081f08ba chromedriver + 6113466\n2   chromedriver                        0x0000000107c81e10 chromedriver + 417296\n3   chromedriver                        0x0000000107c567d0 chromedriver + 239568\n4   chromedriver                        0x0000000107d01e98 chromedriver + 941720\n5   chromedriver                        0x0000000107d20b3c chromedriver + 1067836\n6   chromedriver                        0x0000000107cf9a93 chromedriver + 907923\n7   chromedriver                        0x0000000107cc60f7 chromedriver + 696567\n8   chromedriver                        0x0000000107cc6d61 chromedriver + 699745\n9   chromedriver                        0x00000001081b6250 chromedriver + 5874256\n10  chromedriver                        0x00000001081ba289 chromedriver + 5890697\n11  chromedriver                        0x0000000108192022 chromedriver + 5726242\n12  chromedriver                        0x00000001081babff chromedriver + 5893119\n13  chromedriver                        0x0000000108180d14 chromedriver + 5655828\n14  chromedriver                        0x00000001081ddde8 chromedriver + 6036968\n15  chromedriver                        0x00000001081ddfb0 chromedriver + 6037424\n16  chromedriver                        0x00000001081f0451 chromedriver + 6112337\n17  libsystem_pthread.dylib             0x00007ff8179b31d3 _pthread_start + 125\n18  libsystem_pthread.dylib             0x00007ff8179aebd3 thread_start + 15\n"}, "8_step_login_process": {"success": false, "error": "Message: no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.158)\nStacktrace:\n0   chromedriver                        0x00000001081f9308 chromedriver + 6148872\n1   chromedriver                        0x00000001081f08ba chromedriver + 6113466\n2   chromedriver                        0x0000000107c81e10 chromedriver + 417296\n3   chromedriver                        0x0000000107c567d0 chromedriver + 239568\n4   chromedriver                        0x0000000107d01e98 chromedriver + 941720\n5   chromedriver                        0x0000000107d20b3c chromedriver + 1067836\n6   chromedriver                        0x0000000107cf9a93 chromedriver + 907923\n7   chromedriver                        0x0000000107cc60f7 chromedriver + 696567\n8   chromedriver                        0x0000000107cc6d61 chromedriver + 699745\n9   chromedriver                        0x00000001081b6250 chromedriver + 5874256\n10  chromedriver                        0x00000001081ba289 chromedriver + 5890697\n11  chromedriver                        0x0000000108192022 chromedriver + 5726242\n12  chromedriver                        0x00000001081babff chromedriver + 5893119\n13  chromedriver                        0x0000000108180d14 chromedriver + 5655828\n14  chromedriver                        0x00000001081ddde8 chromedriver + 6036968\n15  chromedriver                        0x00000001081ddfb0 chromedriver + 6037424\n16  chromedriver                        0x00000001081f0451 chromedriver + 6112337\n17  libsystem_pthread.dylib             0x00007ff8179b31d3 _pthread_start + 125\n18  libsystem_pthread.dylib             0x00007ff8179aebd3 thread_start + 15\n"}, "login_method_detection": {"success": false, "error": "Message: no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=138.0.7204.158)\nStacktrace:\n0   chromedriver                        0x00000001081f9308 chromedriver + 6148872\n1   chromedriver                        0x00000001081f08ba chromedriver + 6113466\n2   chromedriver                        0x0000000107c81e10 chromedriver + 417296\n3   chromedriver                        0x0000000107c567d0 chromedriver + 239568\n4   chromedriver                        0x0000000107d01e98 chromedriver + 941720\n5   chromedriver                        0x0000000107d20b3c chromedriver + 1067836\n6   chromedriver                        0x0000000107cf9a93 chromedriver + 907923\n7   chromedriver                        0x0000000107cc60f7 chromedriver + 696567\n8   chromedriver                        0x0000000107cc6d61 chromedriver + 699745\n9   chromedriver                        0x00000001081b6250 chromedriver + 5874256\n10  chromedriver                        0x00000001081ba289 chromedriver + 5890697\n11  chromedriver                        0x0000000108192022 chromedriver + 5726242\n12  chromedriver                        0x00000001081babff chromedriver + 5893119\n13  chromedriver                        0x0000000108180d14 chromedriver + 5655828\n14  chromedriver                        0x00000001081ddde8 chromedriver + 6036968\n15  chromedriver                        0x00000001081ddfb0 chromedriver + 6037424\n16  chromedriver                        0x00000001081f0451 chromedriver + 6112337\n17  libsystem_pthread.dylib             0x00007ff8179b31d3 _pthread_start + 125\n18  libsystem_pthread.dylib             0x00007ff8179aebd3 thread_start + 15\n"}}, "summary": {"total_tests": 7, "passed": 3, "failed": 4}}