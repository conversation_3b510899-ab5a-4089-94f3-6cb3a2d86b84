#!/usr/bin/env python
"""
Integration test for the enhanced bot detection system
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.utils.session_manager import EnhancedSessionManager
from actor_tiktok.config.bot_detection_config import (
    SESSION_ROTATION_THRESHOLDS,
    HEALTH_SCORE_WEIGHTS,
    RISK_LEVELS,
    COOLDOWN_PERIODS,
    BOT_DETECTION_INDICATORS,
    CAPTCHA_INDICATORS,
    RATE_LIMITING_INDICATORS,
    HUMAN_BEHAVIOR_CONFIG
)
from actor_tiktok.models import TikTokUserAccount, TikTokSession
from django.utils import timezone

def test_configuration_integration():
    """Test that all configuration components are properly integrated"""
    print("\n🔧 Testing Configuration Integration")
    print("=" * 50)
    
    # Test session rotation thresholds
    print(f"✅ Session rotation thresholds: {len(SESSION_ROTATION_THRESHOLDS)} configured")
    for key, value in SESSION_ROTATION_THRESHOLDS.items():
        print(f"   - {key}: {value}")
    
    # Test health score weights
    print(f"\n✅ Health score weights: {len(HEALTH_SCORE_WEIGHTS)} configured")
    total_weight = sum(HEALTH_SCORE_WEIGHTS.values())
    print(f"   - Total weight: {total_weight} (should be 1.0)")
    
    # Test risk levels
    print(f"\n✅ Risk levels: {len(RISK_LEVELS)} configured")
    for level, threshold in RISK_LEVELS.items():
        print(f"   - {level}: {threshold}")
    
    # Test cooldown periods
    print(f"\n✅ Cooldown periods: {len(COOLDOWN_PERIODS)} configured")
    for reason, duration in COOLDOWN_PERIODS.items():
        print(f"   - {reason}: {duration}")
    
    # Test detection indicators
    print(f"\n✅ Bot detection indicators: {len(BOT_DETECTION_INDICATORS['keywords'])} keywords, {len(BOT_DETECTION_INDICATORS['css_selectors'])} selectors")
    print(f"✅ CAPTCHA indicators: {len(CAPTCHA_INDICATORS['css_selectors'])} selectors, {CAPTCHA_INDICATORS['timeout_seconds']}s timeout")
    print(f"✅ Rate limiting indicators: {len(RATE_LIMITING_INDICATORS['keywords'])} keywords, {len(RATE_LIMITING_INDICATORS['css_selectors'])} selectors")
    
    # Test human behavior config
    print(f"\n✅ Human behavior config: {len(HUMAN_BEHAVIOR_CONFIG)} categories")
    for category in HUMAN_BEHAVIOR_CONFIG.keys():
        print(f"   - {category}: configured")
    
    return True

def test_session_manager_integration():
    """Test EnhancedSessionManager integration with configuration"""
    print("\n🔄 Testing Session Manager Integration")
    print("=" * 50)
    
    try:
        # Initialize session manager
        session_manager = EnhancedSessionManager()
        print("✅ EnhancedSessionManager initialized successfully")
        
        # Test configuration integration
        assert session_manager.rotation_threshold == SESSION_ROTATION_THRESHOLDS['anti_bot_score']
        print(f"✅ Rotation threshold properly configured: {session_manager.rotation_threshold}")
        
        # Test method availability
        methods = [
            'should_rotate_session',
            'get_account_health_score',
            'select_best_account_for_task',
            'rotate_session_if_needed'
        ]
        
        for method in methods:
            assert hasattr(session_manager, method), f"Missing method: {method}"
            print(f"✅ Method available: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ Session manager integration failed: {str(e)}")
        return False

def test_database_models():
    """Test database model integration"""
    print("\n🗄️  Testing Database Models")
    print("=" * 50)
    
    try:
        # Test TikTokUserAccount model
        account_count = TikTokUserAccount.objects.count()
        print(f"✅ TikTokUserAccount model accessible: {account_count} accounts")
        
        # Test TikTokSession model
        session_count = TikTokSession.objects.count()
        print(f"✅ TikTokSession model accessible: {session_count} sessions")
        
        # Test model fields
        account_fields = [field.name for field in TikTokUserAccount._meta.fields]
        required_account_fields = [
            'tiktok_username', 'is_active', 'is_blocked', 'login_attempts',
            'last_login', 'session_expires_at', 'encrypted_session_data',
            'user', 'tiktok_user_id', 'created_at', 'updated_at'
        ]
        
        for field in required_account_fields:
            assert field in account_fields, f"Missing account field: {field}"
            print(f"✅ Account field available: {field}")
        
        session_fields = [field.name for field in TikTokSession._meta.fields]
        required_session_fields = [
            'tiktok_account', 'session_id', 'user_agent', 'proxy_used'
        ]
        
        for field in required_session_fields:
            assert field in session_fields, f"Missing session field: {field}"
            print(f"✅ Session field available: {field}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database model test failed: {str(e)}")
        return False

def test_task_imports():
    """Test that all tasks can be imported"""
    print("\n📋 Testing Task Imports")
    print("=" * 50)
    
    try:
        from actor_tiktok.tasks import (
            actor_login_task,
            select_best_account_for_task,
            monitor_account_health,
            cleanup_expired_sessions
        )
        
        tasks = [
            ('actor_login_task', actor_login_task),
            ('select_best_account_for_task', select_best_account_for_task),
            ('monitor_account_health', monitor_account_health),
            ('cleanup_expired_sessions', cleanup_expired_sessions)
        ]
        
        for task_name, task_func in tasks:
            assert callable(task_func), f"Task {task_name} is not callable"
            print(f"✅ Task imported successfully: {task_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Task import test failed: {str(e)}")
        return False

def test_management_command():
    """Test management command availability"""
    print("\n⚙️  Testing Management Command")
    print("=" * 50)
    
    try:
        from actor_tiktok.management.commands.test_bot_detection import Command
        
        command = Command()
        assert hasattr(command, 'handle'), "Command missing handle method"
        print("✅ Management command available: test_bot_detection")
        
        return True
        
    except Exception as e:
        print(f"❌ Management command test failed: {str(e)}")
        return False

def run_integration_tests():
    """Run all integration tests"""
    print("🚀 Enhanced Bot Detection System - Integration Tests")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    
    tests = [
        ('Configuration Integration', test_configuration_integration),
        ('Session Manager Integration', test_session_manager_integration),
        ('Database Models', test_database_models),
        ('Task Imports', test_task_imports),
        ('Management Command', test_management_command)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Integration Test Summary")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All integration tests passed! The enhanced bot detection system is fully integrated.")
        print("\n📋 System Features Verified:")
        print("   ✅ Configuration-driven bot detection")
        print("   ✅ Enhanced session management")
        print("   ✅ Account health monitoring")
        print("   ✅ Smart session rotation")
        print("   ✅ Cooldown management")
        print("   ✅ Management commands")
        print("   ✅ Celery task integration")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the output above.")
    
    print(f"\nCompleted at: {datetime.now()}")
    return failed == 0

if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)