#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.models import TikTokUserAccount
from django.contrib.auth.models import User
from django.utils import timezone

def fix_accounts():
    """Fix TikTok accounts by associating them with users"""
    print("Fixing TikTok account user associations...")
    
    # Get or create a test user
    test_user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    if created:
        test_user.set_password('testpassword123')
        test_user.save()
        print(f"Created test user: {test_user.username}")
    else:
        print(f"Using existing test user: {test_user.username}")
    
    # Fix accounts without users
    accounts_without_users = TikTokUserAccount.objects.filter(user__isnull=True)
    
    print(f"Found {accounts_without_users.count()} accounts without users")
    
    for account in accounts_without_users:
        account.user = test_user
        account.save()
        print(f"Associated account '{account.tiktok_username}' with user '{test_user.username}'")
    
    # Show all accounts
    print("\nAll TikTok accounts:")
    for account in TikTokUserAccount.objects.all():
        print(f"  {account.tiktok_username} -> User: {account.user.username if account.user else 'None'}")

if __name__ == '__main__':
    fix_accounts()