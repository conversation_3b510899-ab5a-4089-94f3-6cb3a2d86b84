#!/usr/bin/env python

# Test what data is being extracted from TikTok pages
from crawler_tiktok.utils.data_processor import parse_tiktok_data

# Sample HTML content that might be found on TikTok (simplified for testing)
sample_search_html = '''
<html>
<body>
    <div data-e2e="search-card-desc">This is a test video description</div>
    <div data-e2e="search-card-user-link">@testuser</div>
    <div data-e2e="search-card-video-caption">Test caption</div>
    <strong data-e2e="search-card-like-count">1.2K</strong>
    <strong data-e2e="search-card-comment-count">45</strong>
</body>
</html>
'''

sample_video_html = '''
<html>
<body>
    <div data-e2e="browse-video">
        <div data-e2e="browse-video-desc">Sample video description</div>
        <a data-e2e="browse-video-author">@sampleuser</a>
        <strong data-e2e="like-count">5.6K</strong>
        <strong data-e2e="comment-count">123</strong>
        <strong data-e2e="share-count">89</strong>
    </div>
</body>
</html>
'''

print("=== Testing Data Extraction ===")

print("\n1. Testing Search Data Extraction:")
search_result = parse_tiktok_data(sample_search_html, 'search')
print(f"Search result type: {type(search_result)}")
print(f"Search result: {search_result}")

print("\n2. Testing Video Data Extraction:")
video_result = parse_tiktok_data(sample_video_html, 'video')
print(f"Video result type: {type(video_result)}")
print(f"Video result: {video_result}")

print("\n3. Testing with empty HTML:")
empty_result = parse_tiktok_data('<html><body></body></html>', 'search')
print(f"Empty result: {empty_result}")

print("\n=== Expected Data Structure ===")
print("For search results, we should see:")
print("- id: unique identifier")
print("- desc: video description")
print("- author: username")
print("- stats: {likes, comments, shares}")
print("\nFor video results, we should see:")
print("- desc: video description")
print("- author: username")
print("- stats: {likes, comments, shares}")