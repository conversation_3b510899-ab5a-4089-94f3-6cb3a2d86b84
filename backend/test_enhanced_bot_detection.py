#!/usr/bin/env python
"""
Test script for the enhanced bot detection system

This script tests various components of the bot detection system
to ensure they're working correctly.
"""

import os
import sys
import django
from datetime import timedelta
from django.utils import timezone

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.models import TikTokUserAccount, TikTokSession
from actor_tiktok.utils.session_manager import EnhancedSessionManager
from actor_tiktok.config.bot_detection_config import (
    SESSION_ROTATION_THRESHOLDS,
    HEALTH_SCORE_WEIGHTS,
    RISK_LEVELS,
    COOLDOWN_PERIODS
)

def test_configuration_loading():
    """Test that configuration is loaded correctly"""
    print("\n=== Testing Configuration Loading ===")
    
    try:
        # Test that all required config sections exist
        assert 'anti_bot_score' in SESSION_ROTATION_THRESHOLDS
        assert 'session_quality' in HEALTH_SCORE_WEIGHTS
        assert 'low' in RIS<PERSON>_LEVELS
        assert 'bot_detection' in COOLDOWN_PERIODS
        
        print("✅ Configuration loaded successfully")
        print(f"   - Anti-bot threshold: {SESSION_ROTATION_THRESHOLDS['anti_bot_score']}")
        print(f"   - Session quality weight: {HEALTH_SCORE_WEIGHTS['session_quality']}")
        print(f"   - Low risk threshold: {RISK_LEVELS['low']}")
        print(f"   - Bot detection cooldown: {COOLDOWN_PERIODS['bot_detection']}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration loading failed: {str(e)}")
        return False

def test_session_manager_initialization():
    """Test that EnhancedSessionManager initializes correctly"""
    print("\n=== Testing Session Manager Initialization ===")
    
    try:
        session_manager = EnhancedSessionManager()
        
        # Test that the manager has the expected attributes
        assert hasattr(session_manager, 'should_rotate_session'), "Missing should_rotate_session method"
        assert hasattr(session_manager, 'get_account_health_score'), "Missing get_account_health_score method"
        assert hasattr(session_manager, 'select_best_account_for_task'), "Missing select_best_account_for_task method"
        
        # Check if apply_cooldown method exists (it might be named differently)
        if not hasattr(session_manager, 'apply_cooldown'):
            print("   ⚠️  apply_cooldown method not found, checking for alternatives...")
            # List available methods for debugging
            methods = [method for method in dir(session_manager) if not method.startswith('_')]
            print(f"   Available methods: {methods}")
        
        print("✅ EnhancedSessionManager initialized successfully")
        print(f"   - Rotation threshold: {session_manager.rotation_threshold}")
        print(f"   - Health check interval: {session_manager.health_check_interval}")
        
        return True
    except Exception as e:
        import traceback
        print(f"❌ Session manager initialization failed: {str(e)}")
        print(f"   Full traceback: {traceback.format_exc()}")
        return False

def test_account_health_scoring():
    """Test account health scoring with mock data"""
    print("\n=== Testing Account Health Scoring ===")
    
    try:
        session_manager = EnhancedSessionManager()
        
        # Get first active account for testing
        account = TikTokUserAccount.objects.filter(is_active=True).first()
        
        if not account:
            print("⚠️  No active accounts found for testing")
            return True
        
        # Test health score calculation
        health_score = session_manager.get_account_health_score(account.id)
        
        # Verify health score structure
        required_keys = ['overall_score', 'risk_level', 'recommendations']
        for key in required_keys:
            assert key in health_score, f"Missing key: {key}"
        
        overall_score = health_score['overall_score']
        risk_level = health_score['risk_level']
        
        assert 0 <= overall_score <= 1, f"Invalid overall score: {overall_score}"
        assert risk_level in ['low', 'medium', 'high', 'critical'], f"Invalid risk level: {risk_level}"
        
        print(f"✅ Health scoring working correctly")
        print(f"   - Account {account.id} score: {overall_score:.2f}")
        print(f"   - Risk level: {risk_level}")
        print(f"   - Recommendations: {len(health_score['recommendations'])}")
        
        return True
    except Exception as e:
        print(f"❌ Health scoring test failed: {str(e)}")
        return False

def test_session_rotation_logic():
    """Test session rotation decision logic"""
    print("\n=== Testing Session Rotation Logic ===")
    
    try:
        session_manager = EnhancedSessionManager()
        
        # Get first active account for testing
        account = TikTokUserAccount.objects.filter(is_active=True).first()
        
        if not account:
            print("⚠️  No active accounts found for testing")
            return True
        
        # Test rotation decision
        should_rotate, reason = session_manager.should_rotate_session(account.id)
        
        assert isinstance(should_rotate, bool), "should_rotate must be boolean"
        assert isinstance(reason, str), "reason must be string"
        
        print(f"✅ Session rotation logic working")
        print(f"   - Account {account.id} should rotate: {should_rotate}")
        print(f"   - Reason: {reason}")
        
        return True
    except Exception as e:
        print(f"❌ Session rotation test failed: {str(e)}")
        return False

def test_account_selection():
    """Test best account selection logic"""
    print("\n=== Testing Account Selection ===")
    
    try:
        session_manager = EnhancedSessionManager()
        
        # Get available accounts
        available_accounts = list(
            TikTokUserAccount.objects.filter(
                is_active=True
            ).values_list('id', flat=True)[:5]  # Limit to 5 for testing
        )
        
        if not available_accounts:
            print("⚠️  No active accounts found for testing")
            return True
        
        # Test account selection
        best_account_id = session_manager.select_best_account_for_task(available_accounts)
        
        if best_account_id:
            assert best_account_id in available_accounts, "Selected account not in available list"
            print(f"✅ Account selection working")
            print(f"   - Selected account: {best_account_id}")
            print(f"   - From {len(available_accounts)} available accounts")
        else:
            print("⚠️  No suitable account found (all may be in cooldown)")
        
        return True
    except Exception as e:
        print(f"❌ Account selection test failed: {str(e)}")
        return False

def test_cooldown_application():
    """Test cooldown application (without actually applying it)"""
    print("\n=== Testing Cooldown Logic ===")
    
    try:
        session_manager = EnhancedSessionManager()
        
        # Test cooldown duration calculation for different reasons
        test_reasons = ['bot_detection', 'rate_limiting', 'captcha_timeout', 'general_failure']
        
        for reason in test_reasons:
            cooldown_duration = COOLDOWN_PERIODS.get(reason)
            assert cooldown_duration is not None, f"No cooldown defined for {reason}"
            assert isinstance(cooldown_duration, timedelta), f"Cooldown for {reason} must be timedelta"
            
            print(f"   - {reason}: {cooldown_duration.total_seconds()/60:.1f} minutes")
        
        print("✅ Cooldown logic working correctly")
        return True
    except Exception as e:
        print(f"❌ Cooldown test failed: {str(e)}")
        return False

def test_database_connectivity():
    """Test database connectivity and model access"""
    print("\n=== Testing Database Connectivity ===")
    
    try:
        # Test TikTokUserAccount model
        account_count = TikTokUserAccount.objects.count()
        active_count = TikTokUserAccount.objects.filter(is_active=True).count()
        
        # Test TikTokSession model
        session_count = TikTokSession.objects.count()
        
        print(f"✅ Database connectivity working")
        print(f"   - Total accounts: {account_count}")
        print(f"   - Active accounts: {active_count}")
        print(f"   - Total sessions: {session_count}")
        
        return True
    except Exception as e:
        print(f"❌ Database connectivity test failed: {str(e)}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("🚀 Starting Enhanced Bot Detection System Tests")
    print("=" * 50)
    
    tests = [
        test_configuration_loading,
        test_database_connectivity,
        test_session_manager_initialization,
        test_account_health_scoring,
        test_session_rotation_logic,
        test_account_selection,
        test_cooldown_application,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {str(e)}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Summary: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Enhanced bot detection system is working correctly.")
    else:
        print(f"⚠️  {failed} test(s) failed. Please review the output above.")
    
    return failed == 0

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)