#!/usr/bin/env python3
"""
Test Session Management Fix

Test the complete login flow and session persistence to verify the fixes work correctly.
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000"
FRONTEND_URL = "http://localhost:3000"

def test_session_management_fix():
    """Test the complete session management fix"""
    print("🔧 SESSION MANAGEMENT FIX TEST")
    print("="*70)
    
    # Test 1: Login endpoint accessibility
    print("\n🧪 Test 1: Login Endpoint Accessibility")
    try:
        response = requests.post(f"{BASE_URL}/api/actor/simple-login/", json={
            "username": "grafisone",
            "password": "Puyol@102410"
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Login endpoint accessible")
            print(f"   ✅ Response success: {data.get('success', False)}")
            
            # Check for JWT tokens
            if data.get('access_token'):
                print(f"   ✅ Access token returned: {data['access_token'][:20]}...")
            else:
                print(f"   ❌ No access token in response")
                
            if data.get('refresh_token'):
                print(f"   ✅ Refresh token returned: {data['refresh_token'][:20]}...")
            else:
                print(f"   ❌ No refresh token in response")
                
            if data.get('user'):
                print(f"   ✅ User info returned: {data['user']}")
            else:
                print(f"   ❌ No user info in response")
                
            # Store tokens for next test
            access_token = data.get('access_token')
            
        else:
            print(f"   ❌ Login failed with status: {response.status_code}")
            print(f"   ❌ Response: {response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ Login test failed: {str(e)}")
        return
    
    # Test 2: Authenticated API calls
    print("\n🧪 Test 2: Authenticated API Calls")
    if access_token:
        headers = {
            'Authorization': f'JWT {access_token}',
            'Content-Type': 'application/json'
        }
        
        # Test authenticated endpoints
        auth_endpoints = [
            ("/api/actor/tasks/", "Task Management"),
            ("/api/actor/content-stats/", "Content Statistics"),
            ("/api/actor/scraped-data/", "Scraped Data")
        ]
        
        for endpoint, description in auth_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                if response.status_code == 200:
                    print(f"   ✅ {description}: Authenticated access working")
                elif response.status_code == 401:
                    print(f"   ❌ {description}: Still unauthorized (token issue)")
                else:
                    print(f"   ⚠️ {description}: Status {response.status_code}")
            except Exception as e:
                print(f"   ❌ {description}: {str(e)}")
    else:
        print("   ❌ No access token available for authenticated tests")
    
    # Test 3: Task Creation
    print("\n🧪 Test 3: Task Creation with Authentication")
    if access_token:
        try:
            task_data = {
                "task_name": "Test Content Search",
                "task_type": "CONTENT_SEARCH",
                "keywords": "viral indonesia",
                "max_items": 10
            }
            
            response = requests.post(
                f"{BASE_URL}/api/actor/tasks/", 
                json=task_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Task creation successful")
                print(f"   ✅ Task ID: {data.get('id')}")
                print(f"   ✅ Task name: {data.get('task_name')}")
                print(f"   ✅ Keywords: {data.get('keywords')}")
            else:
                print(f"   ❌ Task creation failed: {response.status_code}")
                print(f"   ❌ Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Task creation test failed: {str(e)}")
    else:
        print("   ❌ No access token available for task creation test")
    
    # Test 4: Frontend Integration
    print("\n🧪 Test 4: Frontend Integration")
    try:
        response = requests.get(f"{FRONTEND_URL}/actor", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ Frontend accessible")
            print(f"   ✅ Page size: {len(response.content)} bytes")
            
            # Check if the page contains the expected elements
            content = response.text
            if "TikTok Actor Dashboard" in content:
                print(f"   ✅ Dashboard title found")
            if "Login Active" in content or "loginSuccess" in content:
                print(f"   ✅ Login state management code present")
            if "Logout" in content:
                print(f"   ✅ Logout functionality present")
                
        else:
            print(f"   ⚠️ Frontend status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Frontend test failed: {str(e)}")
    
    print("\n" + "="*70)
    print("SESSION MANAGEMENT FIX SUMMARY")
    print("="*70)
    
    print("\n✅ FIXES IMPLEMENTED:")
    print("   🔧 Login endpoint made accessible (AllowAny permission)")
    print("   🔧 JWT token generation added to login response")
    print("   🔧 Frontend token storage implemented")
    print("   🔧 Authentication state persistence added")
    print("   🔧 Task creation with authentication enabled")
    print("   🔧 Logout functionality implemented")
    print("   🔧 Cross-component state synchronization")
    
    print("\n🎯 EXPECTED USER EXPERIENCE:")
    print("   1. 🌐 User opens http://localhost:3000/actor")
    print("   2. 🔐 User logs in with grafisone / Puyol@102410")
    print("   3. ✅ Dashboard shows 'Login Active' badge")
    print("   4. 📋 User can create tasks in Tasks tab")
    print("   5. 🔍 User can perform content searches")
    print("   6. 💾 Login state persists across page refreshes")
    print("   7. 🚪 User can logout to clear session")
    
    print("\n🔧 TECHNICAL IMPROVEMENTS:")
    print("   • JWT-based authentication system")
    print("   • Persistent localStorage token storage")
    print("   • Automatic authentication state detection")
    print("   • Cross-component state synchronization")
    print("   • Proper session cleanup on logout")
    
    print(f"\n🎉 Session management fixes are ready for testing!")
    print(f"🌐 Test at: {FRONTEND_URL}/actor")

if __name__ == "__main__":
    test_session_management_fix()
