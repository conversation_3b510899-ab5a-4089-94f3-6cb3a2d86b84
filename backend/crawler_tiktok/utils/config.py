import os
from typing import Dict, List

# Rate limiting settings
RATE_LIMITS = {
    'video': {
        'requests_per_minute': 10,
        'pause_after_requests': 5,  # seconds
        'max_retries': 3
    },
    'user': {
        'requests_per_minute': 8,
        'pause_after_requests': 6,  # seconds
        'max_retries': 3
    },
    'search': {
        'requests_per_minute': 6,
        'pause_after_requests': 8,  # seconds
        'max_retries': 3
    }
}

# Crawler settings
CRAWLER_CONFIG = {
    'scroll_pause_time': 2.0,  # seconds
    'scroll_attempts': 5,
    'page_load_timeout': 30,  # seconds
    'element_timeout': 20,  # seconds
    'min_action_delay': 1.0,  # seconds
    'max_action_delay': 3.0,  # seconds
    'min_request_delay': 2.0,  # seconds
    'max_request_delay': 5.0   # seconds
}

# WebDriver settings
WEBDRIVER_CONFIG = {
    'pool_size': 3,
    'driver_ttl': 1800,  # 30 minutes in seconds
    'browser_options': [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-extensions',
        '--disable-infobars',
        '--disable-notifications',
        '--disable-popup-blocking',
        '--disable-blink-features=AutomationControlled',
        '--headless=new'
    ]
}

# Proxy configuration
PROXY_CONFIG = {
    'enabled': False,
    'proxies': [
        # Add your proxy list here
        # Format: {'http': 'http://proxy:port', 'https': 'https://proxy:port'}
    ],
    'rotation_interval': 300  # 5 minutes in seconds
}

# User agent rotation
USER_AGENT_ROTATION = {
    'enabled': True,
    'rotation_interval': 60  # 1 minute in seconds
}

# Error handling
ERROR_HANDLING = {
    'max_retries': 3,
    'retry_delay': 5,  # seconds
    'exponential_backoff': True,
    'error_types': {
        'captcha': {
            'max_retries': 2,
            'delay': 10
        },
        'rate_limit': {
            'max_retries': 3,
            'delay': 15
        },
        'network': {
            'max_retries': 3,
            'delay': 5
        }
    }
}

# Monitoring settings
MONITORING = {
    'log_level': 'INFO',
    'metrics': {
        'request_count': True,
        'success_rate': True,
        'response_time': True,
        'error_rate': True
    },
    'alerts': {
        'error_threshold': 0.2,  # 20% error rate
        'response_time_threshold': 10  # seconds
    }
}

# File paths
PATHS = {
    'chrome_binary': '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
    'download_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'downloads'),
    'log_dir': os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
}

# Create directories if they don't exist
for directory in [PATHS['download_dir'], PATHS['log_dir']]:
    if not os.path.exists(directory):
        os.makedirs(directory)