from bs4 import BeautifulSoup
import json
import re
from datetime import datetime
import logging
from ..exceptions import TikTokContentChangedException

logger = logging.getLogger(__name__)

def extract_json_data(html_content):
    """Extract embedded JSON data from TikTok's page source."""
    try:
        # Try to find JSON-LD data first
        soup = BeautifulSoup(html_content, 'html.parser')
        json_ld = soup.find('script', type='application/ld+json')
        if json_ld:
            return json.loads(json_ld.string)

        # Look for SIGI_STATE data
        sigi_state_pattern = re.compile(r'window\[\'SIGI_STATE\'\]\s*=\s*(\{.*?\});', re.DOTALL)
        sigi_match = sigi_state_pattern.search(html_content)
        if sigi_match:
            return json.loads(sigi_match.group(1))

        # Look for __UNIVERSAL_DATA_FOR_REHYDRATION__ data
        universal_pattern = re.compile(r'window\[\'__UNIVERSAL_DATA_FOR_REHYDRATION__\'\]\s*=\s*(\{.*?\});', re.DOTALL)
        universal_match = universal_pattern.search(html_content)
        if universal_match:
            return json.loads(universal_match.group(1))

    except Exception as e:
        logger.warning(f"Failed to extract JSON data: {str(e)}")
        return None

def parse_tiktok_data(html_content):
    """Parse TikTok video data from HTML content."""
    try:
        # Try to extract embedded JSON data first
        json_data = extract_json_data(html_content)
        if json_data:
            return process_json_data(json_data)

        # Fallback to HTML parsing
        return parse_html_data(html_content)

    except Exception as e:
        logger.error(f"Error parsing TikTok data: {str(e)}")
        raise TikTokContentChangedException(f"Failed to parse video data: {str(e)}")

def process_json_data(json_data):
    """Process extracted JSON data into a standardized format."""
    processed_data = {
        'video': {},
        'engagement': {},
        'user': {},
        'hashtags': [],
        'music': {}
    }

    try:
        # Extract video information
        if 'video' in json_data:
            video_info = json_data['video']
            processed_data['video'].update({
                'url': video_info.get('url'),
                'description': video_info.get('description'),
                'duration': video_info.get('duration'),
                'created_at': video_info.get('uploadDate'),
                'video_id': video_info.get('id')
            })

        # Extract engagement metrics
        if 'stats' in json_data:
            stats = json_data['stats']
            processed_data['engagement'].update({
                'likes': int(stats.get('diggCount', 0)),
                'comments': int(stats.get('commentCount', 0)),
                'shares': int(stats.get('shareCount', 0)),
                'views': int(stats.get('playCount', 0))
            })

        # Extract user information
        if 'author' in json_data:
            author = json_data['author']
            processed_data['user'].update({
                'username': author.get('uniqueId'),
                'nickname': author.get('nickname'),
                'avatar_url': author.get('avatarLarger'),
                'signature': author.get('signature')
            })

        # Extract hashtags
        if 'challenges' in json_data:
            processed_data['hashtags'] = [
                tag.get('title', '').strip('#')
                for tag in json_data['challenges']
                if tag.get('title')
            ]

        # Extract music information
        if 'music' in json_data:
            music = json_data['music']
            processed_data['music'].update({
                'title': music.get('title'),
                'author': music.get('authorName'),
                'duration': music.get('duration'),
                'url': music.get('playUrl')
            })

        return processed_data

    except Exception as e:
        logger.error(f"Error processing JSON data: {str(e)}")
        return None

def parse_html_data(html_content):
    """Parse TikTok data from HTML when JSON extraction fails."""
    soup = BeautifulSoup(html_content, 'html.parser')
    processed_data = {
        'video': {},
        'engagement': {},
        'user': {},
        'hashtags': [],
        'music': {}
    }

    try:
        # Extract video information from meta tags
        meta_tags = {
            'og:url': 'url',
            'og:description': 'description',
            'og:video:duration': 'duration',
            'og:title': 'title'
        }

        for meta_name, data_key in meta_tags.items():
            meta_tag = soup.find('meta', property=meta_name)
            if meta_tag:
                processed_data['video'][data_key] = meta_tag.get('content')

        # Extract engagement metrics
        engagement_selectors = {
            'likes': '[data-e2e="like-count"]',
            'comments': '[data-e2e="comment-count"]',
            'shares': '[data-e2e="share-count"]'
        }

        for metric, selector in engagement_selectors.items():
            element = soup.select_one(selector)
            if element:
                processed_data['engagement'][metric] = parse_count(element.text)

        # Extract user information
        user_link = soup.select_one('a[data-e2e="video-author-uniqueid"]')
        if user_link:
            processed_data['user'].update({
                'username': user_link.text.strip('@'),
                'profile_url': user_link.get('href')
            })

        # Extract hashtags
        hashtag_links = soup.select('a[href*="/tag/"]')
        processed_data['hashtags'] = [
            link.text.strip('#')
            for link in hashtag_links
            if link.text.startswith('#')
        ]

        # Extract music information
        music_link = soup.select_one('a[data-e2e="video-music"]')
        if music_link:
            processed_data['music'].update({
                'title': music_link.text.strip(),
                'url': music_link.get('href')
            })

        return processed_data

    except Exception as e:
        logger.error(f"Error parsing HTML data: {str(e)}")
        return None

def parse_count(count_str):
    """Parse string representations of counts (e.g., '1.5M', '300K') into integers."""
    try:
        count_str = count_str.strip().upper()
        multipliers = {'K': 1000, 'M': 1000000, 'B': 1000000000}
        
        if not count_str:
            return 0
            
        if count_str[-1] in multipliers:
            number = float(count_str[:-1].replace(',', ''))
            return int(number * multipliers[count_str[-1]])
            
        return int(count_str.replace(',', ''))
    except (ValueError, AttributeError):
        return 0