import time
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains
from ..utils.anti_detection import get_random_user_agent
from ..utils.config import LOAD_TIMEOUT, SCROLL_PAUSE_TIME
from ..exceptions import TikTokScrapingException, TikTokContentChangedException, TikTokLoginRequiredException, TikTokBlockedException
from .driver_setup import create_driver
from ..utils.data_processor import parse_tiktok_data
import logging

logger = logging.getLogger(__name__)

def random_scroll(driver):
    """Perform random scrolling to simulate human behavior."""
    scroll_amount = random.randint(100, 500)
    driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
    time.sleep(random.uniform(0.5, 2.0))

def random_mouse_movement(driver):
    """Simulate random mouse movements."""
    action = ActionChains(driver)
    for _ in range(3):
        x = random.randint(0, 500)
        y = random.randint(0, 500)
        action.move_by_offset(x, y).perform()
        time.sleep(random.uniform(0.1, 0.5))

def handle_popups(driver):
    """Handle common TikTok popups and overlays."""
    try:
        # Handle cookie consent
        cookie_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'Accept All')]")
        if cookie_buttons:
            cookie_buttons[0].click()
            time.sleep(1)

        # Handle login prompts
        login_close_buttons = driver.find_elements(By.XPATH, "//button[@aria-label='Close' or contains(@class, 'close-button')]")
        if login_close_buttons:
            login_close_buttons[0].click()
            time.sleep(1)
    except Exception as e:
        logger.warning(f"Error handling popups: {str(e)}")

def scrape_tiktok_video(video_url, proxy=None):
    driver = None
    try:
        user_agent = get_random_user_agent()
        driver = create_driver(
            headless=True,
            proxy=proxy,
            user_agent=user_agent
        )
        
        # Add random delay before navigation
        time.sleep(random.uniform(1, 3))
        
        # Navigate to video URL
        driver.get(video_url)
        
        # Handle initial popups
        handle_popups(driver)
        
        # Wait for content to load with multiple selectors
        selectors = [
            "div[class*='DivVideoContainer']",
            "div[class*='video-card-container']",
            "div[data-e2e='video-container']"
        ]
        
        content_found = False
        for selector in selectors:
            try:
                WebDriverWait(driver, LOAD_TIMEOUT/len(selectors)).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                content_found = True
                break
            except TimeoutException:
                continue
        
        if not content_found:
            raise TikTokScrapingException("Content failed to load within timeout")
        
        # Simulate human behavior
        random_scroll(driver)
        random_mouse_movement(driver)
        
        # Additional wait for dynamic content
        time.sleep(random.uniform(2, 4))
        
        # Check for blocking conditions
        current_url = driver.current_url.lower()
        page_source = driver.page_source.lower()
        
        if "login" in current_url or "signup" in current_url:
            raise TikTokLoginRequiredException("Login required to access this content")
        
        if any(term in page_source for term in ["captcha", "verify", "security check"]):
            raise TikTokBlockedException("Security verification required")
        
        if "video-not-found" in page_source or "video-removed" in page_source:
            raise TikTokContentChangedException("Video not found or has been removed")
        
        # Extract and parse data
        page_source = driver.page_source
        video_data = parse_tiktok_data(page_source, 'video')
        
        # Validate extracted data
        if not video_data or not isinstance(video_data, dict):
            raise TikTokScrapingException("Failed to extract video data")
        
        return video_data
    
    except Exception as e:
        error_msg = f"Scraping failed: {str(e)}"
        logger.error(error_msg)
        raise TikTokScrapingException(error_msg)
    
    finally:
        if driver:
            driver.quit()