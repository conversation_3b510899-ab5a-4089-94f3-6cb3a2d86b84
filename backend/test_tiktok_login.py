#!/usr/bin/env python3
"""
Test script for TikTok login with enhanced anti-detection measures
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.utils.tiktok_auth import TikTokAuthenticator
from actor_tiktok.utils.anti_detection import AntiDetectionManager
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_anti_detection():
    """Test the anti-detection manager"""
    print("Testing Anti-Detection Manager...")
    
    try:
        anti_detection = AntiDetectionManager()
        driver = anti_detection.setup_driver(headless=False)  # Use visible browser for testing
        
        print("✓ Driver created successfully")
        
        # Test basic navigation
        driver.get("https://www.tiktok.com")
        print("✓ Navigated to TikTok homepage")
        
        # Test human behavior simulation
        anti_detection.simulate_human_behavior(driver)
        print("✓ Human behavior simulation completed")
        
        # Check if webdriver property is hidden
        webdriver_hidden = driver.execute_script("return navigator.webdriver === undefined;")
        print(f"✓ Webdriver property hidden: {webdriver_hidden}")
        
        # Check user agent
        user_agent = driver.execute_script("return navigator.userAgent;")
        print(f"✓ User Agent: {user_agent[:50]}...")
        
        # Keep browser open for manual inspection
        input("Press Enter to close the browser and continue...")
        
        driver.quit()
        print("✓ Anti-detection test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Anti-detection test failed: {str(e)}")
        return False

def test_tiktok_login():
    """Test TikTok login with enhanced anti-detection"""
    print("\nTesting TikTok Login...")
    
    # Test credentials
    username = "grafisone"
    password = "Puyol@102410"
    
    driver = None
    try:
        # Create driver with anti-detection
        anti_detection = AntiDetectionManager()
        driver = anti_detection.setup_driver(headless=False)  # Use visible browser for testing
        
        authenticator = TikTokAuthenticator()
        
        print(f"Attempting to login with username: {username}")
        
        result = authenticator.login(
            driver=driver,
            username=username,
            password=password,
            retry_count=0  # Only try once for testing
        )
        
        if result['success']:
            print("✓ Login successful!")
            print(f"Session data keys: {list(result.get('session_data', {}).keys())}")
            return True
        else:
            print(f"✗ Login failed: {result.get('error', 'Unknown error')}")
            
            # Check for specific error types
            if result.get('bot_detected'):
                print("  → Bot detection triggered")
            if result.get('rate_limited'):
                print("  → Rate limiting detected")
            if result.get('captcha_timeout'):
                print("  → CAPTCHA timeout")
            if result.get('requires_2fa'):
                print("  → 2FA required")
                
            return False
            
    except Exception as e:
        print(f"✗ Login test failed with exception: {str(e)}")
        return False
    finally:
        # Clean up driver
        if driver:
            try:
                driver.quit()
            except Exception as e:
                print(f"Warning: Error closing driver: {str(e)}")

def main():
    """Main test function"""
    print("TikTok Enhanced Anti-Detection Test Suite")
    print("=" * 50)
    
    # Test 1: Anti-detection mechanisms
    anti_detection_success = test_anti_detection()
    
    if not anti_detection_success:
        print("\n❌ Anti-detection test failed. Skipping login test.")
        return
    
    # Test 2: TikTok login
    login_success = test_tiktok_login()
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Anti-Detection: {'✓ PASS' if anti_detection_success else '✗ FAIL'}")
    print(f"TikTok Login: {'✓ PASS' if login_success else '✗ FAIL'}")
    
    if login_success:
        print("\n🎉 All tests passed! The enhanced anti-detection measures are working.")
    else:
        print("\n⚠️  Login test failed. This could be due to:")
        print("   - TikTok's advanced bot detection")
        print("   - Account already locked/blocked")
        print("   - Network/connectivity issues")
        print("   - Need for additional anti-detection measures")
        print("\nRecommendations:")
        print("   - Wait longer between login attempts")
        print("   - Use different IP address/proxy")
        print("   - Try with a fresh TikTok account")
        print("   - Consider using residential proxies")

if __name__ == "__main__":
    main()