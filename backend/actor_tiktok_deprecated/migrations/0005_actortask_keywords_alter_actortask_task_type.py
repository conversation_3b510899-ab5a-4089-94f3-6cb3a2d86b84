# Generated by Django 5.2.4 on 2025-07-18 20:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor_tiktok', '0004_actortask_task_parameters'),
    ]

    operations = [
        migrations.AddField(
            model_name='actortask',
            name='keywords',
            field=models.TextField(blank=True, help_text='Keywords for content search (comma-separated)', null=True),
        ),
        migrations.AlterField(
            model_name='actortask',
            name='task_type',
            field=models.CharField(choices=[('MY_VIDEOS', 'My Videos'), ('MY_FOLLOWERS', 'My Followers'), ('MY_FOLLOWING', 'My Following'), ('MY_LIKES', 'My Liked Videos'), ('FEED_SCRAPE', 'Feed Scraping'), ('TARGETED_USER', 'Targeted User Analysis'), ('HASHTAG_ANALYSIS', 'Hashtag Analysis'), ('COMPETITOR_ANALYSIS', 'Competitor Analysis'), ('CONTENT_SEARCH', 'Content Search')], help_text='Type of actor task', max_length=20),
        ),
    ]
