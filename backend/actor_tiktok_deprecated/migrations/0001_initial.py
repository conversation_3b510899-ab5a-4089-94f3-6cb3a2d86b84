# Generated by Django 5.2.4 on 2025-07-15 17:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TikTokUserAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tiktok_username', models.Char<PERSON>ield(help_text='TikTok username', max_length=255)),
                ('tiktok_user_id', models.CharField(blank=True, help_text='TikTok user ID', max_length=255, null=True)),
                ('encrypted_session_data', models.TextField(help_text='Encrypted TikTok session data')),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Whether this account is active')),
                ('last_login', models.DateTimeField(blank=True, help_text='Last successful login to TikTok', null=True)),
                ('session_expires_at', models.DateTimeField(blank=True, help_text='When the session expires', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('login_attempts', models.IntegerField(default=0, help_text='Number of login attempts')),
                ('last_attempt_at', models.DateTimeField(blank=True, help_text='Last login attempt', null=True)),
                ('is_blocked', models.BooleanField(default=False, help_text='Whether account is temporarily blocked')),
                ('blocked_until', models.DateTimeField(blank=True, help_text='When the block expires', null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tiktok_accounts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'TikTok User Account',
                'verbose_name_plural': 'TikTok User Accounts',
                'unique_together': {('user', 'tiktok_username')},
            },
        ),
        migrations.CreateModel(
            name='TikTokSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=255, unique=True)),
                ('user_agent', models.TextField(help_text='User agent used for this session')),
                ('proxy_used', models.CharField(blank=True, help_text='Proxy used for this session', max_length=255, null=True)),
                ('requests_made', models.IntegerField(default=0, help_text='Number of requests made in this session')),
                ('successful_requests', models.IntegerField(default=0, help_text='Number of successful requests')),
                ('failed_requests', models.IntegerField(default=0, help_text='Number of failed requests')),
                ('captcha_challenges', models.IntegerField(default=0, help_text='Number of CAPTCHA challenges encountered')),
                ('rate_limit_hits', models.IntegerField(default=0, help_text='Number of rate limit hits')),
                ('detection_score', models.FloatField(default=0.0, help_text='Bot detection risk score (0.0 to 1.0)')),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('is_healthy', models.BooleanField(default=True, help_text='Whether session is healthy')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(help_text='When this session expires')),
                ('tiktok_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='actor_tiktok.tiktokuseraccount')),
            ],
            options={
                'verbose_name': 'TikTok Session',
                'verbose_name_plural': 'TikTok Sessions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ActorTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_name', models.CharField(help_text='Name of the scraping task', max_length=255)),
                ('task_type', models.CharField(choices=[('MY_VIDEOS', 'My Videos'), ('MY_FOLLOWERS', 'My Followers'), ('MY_FOLLOWING', 'My Following'), ('MY_LIKES', 'My Liked Videos'), ('FEED_SCRAPE', 'Feed Scraping'), ('TARGETED_USER', 'Targeted User Analysis'), ('HASHTAG_ANALYSIS', 'Hashtag Analysis'), ('COMPETITOR_ANALYSIS', 'Competitor Analysis')], help_text='Type of actor task', max_length=20)),
                ('target_identifier', models.CharField(blank=True, help_text='Target username, hashtag, or URL', max_length=255, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('RUNNING', 'Running'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled'), ('PAUSED', 'Paused')], default='PENDING', max_length=10)),
                ('max_items', models.IntegerField(default=100, help_text='Maximum items to scrape')),
                ('scrape_interval', models.IntegerField(default=60, help_text='Interval between requests (seconds)')),
                ('use_stealth_mode', models.BooleanField(default=True, help_text='Use anti-detection measures')),
                ('randomize_delays', models.BooleanField(default=True, help_text='Randomize request delays')),
                ('start_date', models.DateField(blank=True, help_text='Start date for data collection', null=True)),
                ('end_date', models.DateField(blank=True, help_text='End date for data collection', null=True)),
                ('celery_task_id', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('items_scraped', models.IntegerField(default=0, help_text='Number of items successfully scraped')),
                ('total_items_found', models.IntegerField(default=0, help_text='Total items found')),
                ('progress_percentage', models.FloatField(default=0.0, help_text='Task completion percentage')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='actor_tasks', to=settings.AUTH_USER_MODEL)),
                ('tiktok_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='actor_tiktok.tiktokuseraccount')),
            ],
            options={
                'verbose_name': 'Actor Task',
                'verbose_name_plural': 'Actor Tasks',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ActorScrapedData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_type', models.CharField(choices=[('VIDEO', 'Video Data'), ('USER', 'User Data'), ('FOLLOWER', 'Follower Data'), ('FOLLOWING', 'Following Data'), ('LIKE', 'Liked Video Data'), ('FEED_ITEM', 'Feed Item Data'), ('HASHTAG_DATA', 'Hashtag Data'), ('ANALYTICS', 'Analytics Data')], max_length=20)),
                ('content', models.JSONField(help_text='JSON content of the scraped data')),
                ('tiktok_id', models.CharField(blank=True, help_text='TikTok ID of the content', max_length=255, null=True)),
                ('scraped_at', models.DateTimeField(auto_now_add=True)),
                ('is_complete', models.BooleanField(default=True, help_text='Whether all expected data was scraped')),
                ('quality_score', models.FloatField(default=1.0, help_text='Data quality score (0.0 to 1.0)')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scraped_data', to='actor_tiktok.actortask')),
            ],
            options={
                'verbose_name': 'Actor Scraped Data',
                'verbose_name_plural': 'Actor Scraped Data',
                'ordering': ['-scraped_at'],
                'indexes': [models.Index(fields=['task', 'data_type'], name='actor_tikto_task_id_8a21b3_idx'), models.Index(fields=['tiktok_id'], name='actor_tikto_tiktok__f16582_idx'), models.Index(fields=['scraped_at'], name='actor_tikto_scraped_c6d5ad_idx')],
            },
        ),
    ]
