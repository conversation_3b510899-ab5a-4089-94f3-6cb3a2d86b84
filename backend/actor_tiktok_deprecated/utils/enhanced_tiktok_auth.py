import logging
import time
import random
import json
from typing import Dict, Optional, List, Tuple
from datetime import datetime, timedelta
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from django.utils import timezone

from .anti_detection import AntiDetectionManager
from .proxy_manager import ProxyManager
from .session_manager import EnhancedSessionManager
from .undetected_chrome_auth import UndetectedChromeAuthenticator
from ..models import TikTokUserAccount

logger = logging.getLogger(__name__)

class EnhancedTikTokAuthenticator:
    """
    Enhanced TikTok authenticator with advanced anti-detection, proxy support,
    and comprehensive error handling
    """
    
    def __init__(self, proxy_config: Optional[Dict] = None):
        """
        Initialize enhanced authenticator
        
        Args:
            proxy_config: Proxy configuration dictionary
        """
        self.base_url = "https://www.tiktok.com"
        self.login_url = "https://www.tiktok.com/login/phone-or-email/email"
        self.mobile_login_url = "https://www.tiktok.com/login?enter_from=mobile_main"
        
        # Initialize components
        self.anti_detection = AntiDetectionManager()
        self.session_manager = EnhancedSessionManager()
        self.undetected_auth = UndetectedChromeAuthenticator()
        
        # Initialize proxy manager if config provided
        self.proxy_manager = None
        if proxy_config and proxy_config.get('enabled', False):
            self.proxy_manager = ProxyManager(
                proxies=proxy_config.get('proxies', []),
                max_failures=proxy_config.get('max_failures', 3),
                health_check_interval=proxy_config.get('health_check_interval', 300)
            )
        
        # Login strategies with priority
        self.login_strategies = [
            ('undetected_chrome', self._login_with_undetected_chrome),
            ('stealth_mode', self._login_with_stealth_mode),
            ('mobile_view', self._login_with_mobile_view),
            ('alternative_url', self._login_with_alternative_url)
        ]
        
        # Error recovery strategies
        self.recovery_strategies = {
            'captcha': self._handle_captcha_recovery,
            'rate_limit': self._handle_rate_limit_recovery,
            'suspicious_activity': self._handle_suspicious_activity_recovery,
            'account_locked': self._handle_account_locked_recovery,
            'session_expired': self._handle_session_expired_recovery
        }
    
    def login(self, username: str, password: str, use_2fa: bool = False, 
              two_factor_code: Optional[str] = None, retry_count: int = 0,
              account_id: Optional[int] = None) -> Dict:
        """
        Enhanced login with comprehensive error handling and recovery
        
        Args:
            username: TikTok username or email
            password: TikTok password
            use_2fa: Whether to use 2FA
            two_factor_code: 2FA code if available
            retry_count: Current retry attempt
            account_id: TikTokUserAccount ID for session management
        
        Returns:
            Login result dictionary
        """
        logger.info(f"Starting enhanced TikTok login for {username} (attempt {retry_count + 1})")
        
        # Check if we should use existing session
        if account_id:
            existing_session = self._try_existing_session(account_id)
            if existing_session['success']:
                return existing_session
        
        # Get proxy if available
        proxy = None
        if self.proxy_manager:
            proxy_info = self.proxy_manager.get_best_proxy()
            if proxy_info:
                proxy = proxy_info.proxy_url
                logger.info(f"Using proxy: {proxy_info.host}:{proxy_info.port}")
        
        # Try login strategies in order
        last_error = None
        for strategy_name, strategy_func in self.login_strategies:
            try:
                logger.info(f"Attempting login with strategy: {strategy_name}")
                
                result = strategy_func(
                    username=username,
                    password=password,
                    use_2fa=use_2fa,
                    two_factor_code=two_factor_code,
                    proxy=proxy,
                    retry_count=retry_count
                )
                
                if result['success']:
                    # Save session if account_id provided
                    if account_id and 'session_data' in result:
                        self.session_manager.save_session(account_id, result['session_data'])
                        # Create backup
                        self.session_manager.create_session_backup(account_id)
                    
                    # Mark proxy as successful
                    if self.proxy_manager and proxy:
                        proxy_info = self._get_proxy_info_by_url(proxy)
                        if proxy_info:
                            self.proxy_manager.mark_proxy_success(
                                proxy_info, result.get('response_time', 0.0)
                            )
                    
                    logger.info(f"Login successful with strategy: {strategy_name}")
                    return result
                
                last_error = result.get('error', 'Unknown error')
                
                # Try error recovery if applicable
                if 'error_type' in result:
                    recovery_result = self._attempt_error_recovery(
                        result['error_type'], username, password, proxy
                    )
                    if recovery_result['success']:
                        return recovery_result
                
            except Exception as e:
                logger.error(f"Strategy {strategy_name} failed: {str(e)}")
                last_error = str(e)
                
                # Mark proxy as failed if applicable
                if self.proxy_manager and proxy:
                    proxy_info = self._get_proxy_info_by_url(proxy)
                    if proxy_info:
                        self.proxy_manager.mark_proxy_failure(proxy_info)
                
                continue
        
        # All strategies failed
        return {
            'success': False,
            'error': f'All login strategies failed. Last error: {last_error}',
            'strategies_attempted': [s[0] for s in self.login_strategies],
            'retry_count': retry_count
        }
    
    def _try_existing_session(self, account_id: int) -> Dict:
        """
        Try to use existing session
        
        Args:
            account_id: TikTokUserAccount ID
        
        Returns:
            Result dictionary
        """
        try:
            # Check if session should be rotated
            should_rotate, reason = self.session_manager.should_rotate_session(account_id)
            
            if should_rotate:
                logger.info(f"Session rotation needed: {reason}")
                return {'success': False, 'error': 'Session rotation needed'}
            
            # Validate session health
            health_status = self.session_manager.validate_session_health(account_id)
            
            if health_status.get('is_valid', False):
                session_data = self.session_manager.get_session(account_id)
                if session_data:
                    logger.info("Using existing valid session")
                    return {
                        'success': True,
                        'session_data': session_data,
                        'message': 'Using existing session'
                    }
            
            return {'success': False, 'error': 'No valid existing session'}
            
        except Exception as e:
            logger.error(f"Error checking existing session: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _login_with_undetected_chrome(self, username: str, password: str, 
                                    use_2fa: bool = False, two_factor_code: Optional[str] = None,
                                    proxy: Optional[str] = None, retry_count: int = 0) -> Dict:
        """
        Login using undetected Chrome driver
        """
        driver = None
        try:
            # Setup undetected Chrome driver
            driver = self.anti_detection.setup_driver(
                headless=False, proxy=proxy, use_undetected=True
            )

            # Prepare browser session to avoid bot detection
            self._prepare_for_login(driver)

            # Navigate to login page
            driver.get(self.login_url)
            self.anti_detection.random_delay(5, 8)  # Longer delay

            # Handle popups and consent
            self._handle_initial_popups(driver)
            
            # Fill login form
            login_result = self._fill_login_form(driver, username, password)
            if not login_result['success']:
                return login_result
            
            # Handle 2FA if needed
            if use_2fa or self._is_2fa_required(driver):
                if not two_factor_code:
                    return {
                        'success': False,
                        'error': '2FA code required but not provided',
                        'requires_2fa': True
                    }
                
                tfa_result = self._handle_2fa(driver, two_factor_code)
                if not tfa_result['success']:
                    return tfa_result
            
            # Verify login success
            verification_result = self._verify_login_success(driver)
            if not verification_result['success']:
                return verification_result
            
            # Extract session data
            session_data = self._extract_session_data(driver)
            
            return {
                'success': True,
                'session_data': session_data,
                'strategy': 'undetected_chrome',
                'message': 'Login successful with undetected Chrome'
            }
            
        except Exception as e:
            logger.error(f"Undetected Chrome login failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': self._classify_error(str(e))
            }
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def _login_with_stealth_mode(self, username: str, password: str, 
                               use_2fa: bool = False, two_factor_code: Optional[str] = None,
                               proxy: Optional[str] = None, retry_count: int = 0) -> Dict:
        """
        Login using maximum stealth techniques
        """
        driver = None
        try:
            # Setup driver with maximum stealth
            driver = self.anti_detection.setup_driver(
                headless=True, proxy=proxy, use_undetected=False
            )
            
            # Apply additional stealth measures
            self.anti_detection.simulate_human_behavior(driver)
            
            # Navigate with referrer simulation
            self._navigate_with_referrer(driver, self.login_url)
            
            # Extended wait and behavior simulation
            self.anti_detection.random_delay(5, 8)
            self.anti_detection.simulate_mouse_movement(driver)
            
            # Handle popups
            self._handle_initial_popups(driver)
            
            # Fill form with human-like behavior
            login_result = self._fill_login_form_human_like(driver, username, password)
            if not login_result['success']:
                return login_result
            
            # Handle additional verification steps
            verification_result = self._handle_verification_steps(driver, use_2fa, two_factor_code)
            if not verification_result['success']:
                return verification_result
            
            # Extract session data
            session_data = self._extract_session_data(driver)
            
            return {
                'success': True,
                'session_data': session_data,
                'strategy': 'stealth_mode',
                'message': 'Login successful with stealth mode'
            }
            
        except Exception as e:
            logger.error(f"Stealth mode login failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': self._classify_error(str(e))
            }
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    def _classify_error(self, error_message: str) -> str:
        """
        Classify error type for recovery strategy selection

        Args:
            error_message: Error message to classify

        Returns:
            Error type string
        """
        error_message_lower = error_message.lower()

        if any(keyword in error_message_lower for keyword in ['captcha', 'verification', 'puzzle']):
            return 'captcha'
        elif any(keyword in error_message_lower for keyword in ['rate limit', 'too many', 'slow down', 'maximum number of attempts', 'try again later']):
            return 'rate_limit'
        elif any(keyword in error_message_lower for keyword in ['suspicious', 'unusual', 'blocked']):
            return 'suspicious_activity'
        elif any(keyword in error_message_lower for keyword in ['locked', 'disabled', 'suspended']):
            return 'account_locked'
        elif any(keyword in error_message_lower for keyword in ['session', 'expired', 'invalid']):
            return 'session_expired'
        else:
            return 'unknown'

    def _attempt_error_recovery(self, error_type: str, username: str,
                              password: str, proxy: Optional[str] = None) -> Dict:
        """
        Attempt to recover from specific error types

        Args:
            error_type: Type of error to recover from
            username: Username for recovery
            password: Password for recovery
            proxy: Proxy to use for recovery

        Returns:
            Recovery result dictionary
        """
        if error_type in self.recovery_strategies:
            try:
                recovery_func = self.recovery_strategies[error_type]
                return recovery_func(username, password, proxy)
            except Exception as e:
                logger.error(f"Error recovery failed for {error_type}: {str(e)}")
                return {'success': False, 'error': f'Recovery failed: {str(e)}'}

        return {'success': False, 'error': f'No recovery strategy for {error_type}'}

    def _handle_captcha_recovery(self, username: str, password: str,
                               proxy: Optional[str] = None) -> Dict:
        """Handle captcha-related errors"""
        logger.info("Attempting captcha recovery")

        # Wait before retry
        wait_time = random.uniform(30, 60)
        logger.info(f"Waiting {wait_time:.1f} seconds before captcha recovery")
        time.sleep(wait_time)

        # Try with different strategy
        return {'success': False, 'error': 'Captcha recovery not implemented yet'}

    def _handle_rate_limit_recovery(self, username: str, password: str,
                                  proxy: Optional[str] = None) -> Dict:
        """Handle rate limiting errors including 'Maximum number of attempts'"""
        logger.info("Attempting rate limit recovery for 'Maximum number of attempts' error")

        # For "Maximum number of attempts" error, we need much longer wait times
        base_wait = 1800  # 30 minutes base for this specific error
        max_wait = 7200   # 2 hours max
        jitter = random.uniform(0.9, 1.1)
        wait_time = min(base_wait * jitter, max_wait)

        logger.info(f"Waiting {wait_time:.1f} seconds ({wait_time/60:.1f} minutes) for rate limit recovery")
        logger.info("This extended wait is necessary to avoid TikTok's bot detection system")

        # Clear all browser data and sessions
        if hasattr(self, 'session_manager'):
            self.session_manager.clear_session_data(username)
            logger.info("Cleared session data")

        # Switch proxy if available
        if self.proxy_manager:
            new_proxy_info = self.proxy_manager.get_random_proxy()
            if new_proxy_info:
                logger.info(f"Switching to new proxy: {new_proxy_info.host}:{new_proxy_info.port}")

        # Clear browser cache and cookies
        try:
            logger.info("Clearing browser cache and user data")
            # This will be handled by creating a fresh driver instance
        except Exception as e:
            logger.debug(f"Could not clear browser data: {str(e)}")

        # Wait for the calculated time
        time.sleep(wait_time)

        return {'success': False, 'error': 'Rate limit recovery completed, retry needed'}

    def _handle_suspicious_activity_recovery(self, username: str, password: str,
                                           proxy: Optional[str] = None) -> Dict:
        """Handle suspicious activity warnings"""
        logger.info("Attempting suspicious activity recovery")

        # Longer wait for suspicious activity
        wait_time = random.uniform(1800, 3600)  # 30-60 minutes
        logger.info(f"Waiting {wait_time:.1f} seconds for suspicious activity recovery")
        time.sleep(wait_time)

        return {'success': False, 'error': 'Suspicious activity recovery completed, retry needed'}

    def _handle_account_locked_recovery(self, username: str, password: str,
                                      proxy: Optional[str] = None) -> Dict:
        """Handle account locked errors"""
        logger.warning("Account appears to be locked")
        return {'success': False, 'error': 'Account locked, manual intervention required'}

    def _handle_session_expired_recovery(self, username: str, password: str,
                                       proxy: Optional[str] = None) -> Dict:
        """Handle session expired errors"""
        logger.info("Attempting session expired recovery")
        # This would trigger a fresh login attempt
        return {'success': False, 'error': 'Session expired, fresh login needed'}

    def _get_proxy_info_by_url(self, proxy_url: str) -> Optional:
        """Get proxy info object by URL"""
        if not self.proxy_manager:
            return None

        for proxy in self.proxy_manager.proxies:
            if proxy.proxy_url == proxy_url:
                return proxy

        return None

    def _extract_session_data(self, driver) -> Dict:
        """
        Extract session data from browser

        Args:
            driver: WebDriver instance

        Returns:
            Session data dictionary
        """
        try:
            session_data = {
                'cookies': driver.get_cookies(),
                'local_storage': {},
                'session_storage': {},
                'user_agent': driver.execute_script("return navigator.userAgent;"),
                'timestamp': timezone.now().isoformat(),
                'url': driver.current_url
            }

            # Extract local storage
            try:
                local_storage_script = """
                var items = {};
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    items[key] = localStorage.getItem(key);
                }
                return items;
                """
                session_data['local_storage'] = driver.execute_script(local_storage_script)
            except Exception as e:
                logger.warning(f"Could not extract local storage: {str(e)}")

            # Extract session storage
            try:
                session_storage_script = """
                var items = {};
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    items[key] = sessionStorage.getItem(key);
                }
                return items;
                """
                session_data['session_storage'] = driver.execute_script(session_storage_script)
            except Exception as e:
                logger.warning(f"Could not extract session storage: {str(e)}")

            return session_data

        except Exception as e:
            logger.error(f"Error extracting session data: {str(e)}")
            return {}

    def _login_with_mobile_view(self, username: str, password: str,
                              use_2fa: bool = False, two_factor_code: Optional[str] = None,
                              proxy: Optional[str] = None, retry_count: int = 0) -> Dict:
        """
        Login using mobile view simulation
        """
        driver = None
        try:
            # Setup driver with mobile viewport
            driver = self.anti_detection.setup_driver(headless=False, proxy=proxy, use_undetected=False)

            # Set mobile viewport
            driver.set_window_size(375, 667)  # iPhone 6/7/8 size

            # Navigate to mobile login
            driver.get(self.mobile_login_url)
            self.anti_detection.random_delay(3, 5)

            # Handle popups
            self._handle_initial_popups(driver)

            # Fill login form
            login_result = self._fill_login_form(driver, username, password)
            if not login_result['success']:
                return login_result

            # Handle 2FA if needed
            if use_2fa or self._is_2fa_required(driver):
                if not two_factor_code:
                    return {
                        'success': False,
                        'error': '2FA code required but not provided',
                        'requires_2fa': True
                    }

                tfa_result = self._handle_2fa(driver, two_factor_code)
                if not tfa_result['success']:
                    return tfa_result

            # Verify login success
            verification_result = self._verify_login_success(driver)
            if not verification_result['success']:
                return verification_result

            # Extract session data
            session_data = self._extract_session_data(driver)

            return {
                'success': True,
                'session_data': session_data,
                'strategy': 'mobile_view',
                'message': 'Login successful with mobile view'
            }

        except Exception as e:
            logger.error(f"Mobile view login failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': self._classify_error(str(e))
            }
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    def _login_with_alternative_url(self, username: str, password: str,
                                  use_2fa: bool = False, two_factor_code: Optional[str] = None,
                                  proxy: Optional[str] = None, retry_count: int = 0) -> Dict:
        """
        Login using alternative URL approach
        """
        driver = None
        try:
            driver = self.anti_detection.setup_driver(headless=False, proxy=proxy, use_undetected=False)

            # Try alternative login URLs
            alternative_urls = [
                "https://www.tiktok.com/login/phone-or-email",
                "https://www.tiktok.com/login?lang=en",
                "https://www.tiktok.com/passport/web/login/"
            ]

            for url in alternative_urls:
                try:
                    driver.get(url)
                    self.anti_detection.random_delay(3, 5)

                    # Handle popups
                    self._handle_initial_popups(driver)

                    # Try login on this page
                    login_result = self._fill_login_form(driver, username, password)
                    if not login_result['success']:
                        continue

                    # Handle 2FA if needed
                    if use_2fa or self._is_2fa_required(driver):
                        if not two_factor_code:
                            return {
                                'success': False,
                                'error': '2FA code required but not provided',
                                'requires_2fa': True
                            }

                        tfa_result = self._handle_2fa(driver, two_factor_code)
                        if not tfa_result['success']:
                            continue

                    # Verify login success
                    verification_result = self._verify_login_success(driver)
                    if verification_result['success']:
                        session_data = self._extract_session_data(driver)
                        return {
                            'success': True,
                            'session_data': session_data,
                            'strategy': 'alternative_url',
                            'url_used': url,
                            'message': f'Login successful with alternative URL: {url}'
                        }

                except Exception as e:
                    logger.warning(f"Alternative URL {url} failed: {str(e)}")
                    continue

            return {
                'success': False,
                'error': 'All alternative URLs failed',
                'error_type': 'url_access_failed'
            }

        except Exception as e:
            logger.error(f"Alternative URL login failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'error_type': self._classify_error(str(e))
            }
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    def _fill_login_form(self, driver, username: str, password: str) -> Dict:
        """Fill login form with advanced human-like behavior"""
        try:
            # Wait longer for login form with multiple attempts
            login_form_found = False
            for attempt in range(3):
                try:
                    WebDriverWait(driver, 15).until(
                        EC.any_of(
                            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="text"]')),
                            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="username"]')),
                            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="Email"]')),
                            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="Username"]'))
                        )
                    )
                    login_form_found = True
                    break
                except TimeoutException:
                    logger.warning(f"Login form not found, attempt {attempt + 1}/3")
                    if attempt < 2:
                        # Refresh page and try again
                        driver.refresh()
                        time.sleep(random.uniform(3, 5))
                        self._handle_initial_popups(driver)

            if not login_form_found:
                return {'success': False, 'error': 'Login form not found after multiple attempts'}

            # Simulate human-like page interaction before filling form
            self._simulate_human_page_interaction(driver)

            # Find username field with more selectors
            username_selectors = [
                'input[name="username"]',
                'input[type="text"]',
                'input[placeholder*="Email"]',
                'input[placeholder*="Username"]',
                'input[placeholder*="email"]',
                'input[placeholder*="username"]',
                'div[data-testid="email-or-username"] input',
                'div[data-e2e="email-or-username"] input'
            ]

            username_field = None
            for selector in username_selectors:
                try:
                    username_field = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"Found username field with selector: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not username_field:
                return {'success': False, 'error': 'Username field not found'}

            # Simulate human-like focus and typing
            self._human_like_focus_and_type(driver, username_field, username)

            # Wait between fields like a human
            time.sleep(random.uniform(2, 4))

            # Find password field
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'div[data-testid="password"] input',
                'div[data-e2e="password"] input'
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"Found password field with selector: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not password_field:
                return {'success': False, 'error': 'Password field not found'}

            # Simulate human-like focus and typing for password
            self._human_like_focus_and_type(driver, password_field, password)

            # Wait before submitting like a human would
            time.sleep(random.uniform(3, 6))

            # Find and click submit button with human-like behavior
            submit_result = self._human_like_submit(driver)
            if not submit_result['success']:
                return submit_result

            # Wait for response
            time.sleep(random.uniform(5, 8))
            return {'success': True}

        except Exception as e:
            logger.error(f"Error filling login form: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _simulate_human_page_interaction(self, driver):
        """Simulate human-like page interaction before login"""
        try:
            # Scroll slightly to simulate reading
            driver.execute_script("window.scrollBy(0, 100);")
            time.sleep(random.uniform(1, 2))
            driver.execute_script("window.scrollBy(0, -50);")
            time.sleep(random.uniform(0.5, 1))

            # Move mouse around randomly
            if hasattr(self, 'anti_detection') and self.anti_detection:
                self.anti_detection.simulate_mouse_movement(driver)

            # Wait like human reading the page
            time.sleep(random.uniform(2, 4))

        except Exception as e:
            logger.debug(f"Error in human page interaction: {str(e)}")

    def _human_like_focus_and_type(self, driver, element, text):
        """Focus on element and type text in human-like manner"""
        try:
            # Scroll element into view
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(random.uniform(0.5, 1))

            # Click to focus (human-like)
            if hasattr(self, 'anti_detection') and self.anti_detection:
                self.anti_detection.human_like_click(driver, element)
            else:
                element.click()

            time.sleep(random.uniform(0.5, 1))

            # Clear field
            element.clear()
            time.sleep(random.uniform(0.2, 0.5))

            # Type with human-like delays and occasional pauses
            for i, char in enumerate(text):
                element.send_keys(char)

                # Vary typing speed
                if i % 3 == 0:  # Occasional longer pause
                    time.sleep(random.uniform(0.2, 0.4))
                else:
                    time.sleep(random.uniform(0.05, 0.15))

                # Simulate thinking pauses
                if i == len(text) // 2:  # Pause in middle
                    time.sleep(random.uniform(0.5, 1))

            # Final pause after typing
            time.sleep(random.uniform(0.5, 1))

        except Exception as e:
            logger.warning(f"Error in human-like typing: {str(e)}")
            # Fallback to simple typing
            element.clear()
            element.send_keys(text)

    def _human_like_submit(self, driver):
        """Submit form in human-like manner"""
        try:
            # Find submit button with comprehensive selectors
            submit_selectors = [
                'button[type="submit"]',
                'button[data-testid="login-submit"]',
                'button[data-e2e="login-button"]',
                'button[data-e2e="submit-button"]',
                '.login-button',
                'input[type="submit"]'
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"Found submit button with selector: {selector}")
                    break
                except NoSuchElementException:
                    continue

            # Try XPath for text-based selection if CSS selectors fail
            if not submit_button:
                xpath_selectors = [
                    "//button[contains(text(), 'Log in')]",
                    "//button[contains(text(), 'Sign in')]",
                    "//div[@role='button' and contains(text(), 'Log in')]",
                    "//button[contains(@class, 'login')]",
                    "//input[@value='Log in']"
                ]

                for xpath in xpath_selectors:
                    try:
                        submit_button = driver.find_element(By.XPATH, xpath)
                        logger.info(f"Found submit button with XPath: {xpath}")
                        break
                    except NoSuchElementException:
                        continue

            if not submit_button:
                # Try pressing Enter on password field as fallback
                try:
                    password_field = driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
                    password_field.send_keys(Keys.RETURN)
                    logger.info("Submitted form using Enter key")
                    return {'success': True}
                except:
                    return {'success': False, 'error': 'Submit button not found and Enter key failed'}

            # Scroll submit button into view
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", submit_button)
            time.sleep(random.uniform(1, 2))

            # Human-like click
            if hasattr(self, 'anti_detection') and self.anti_detection:
                self.anti_detection.human_like_click(driver, submit_button)
            else:
                submit_button.click()

            logger.info("Login form submitted successfully")
            return {'success': True}

        except Exception as e:
            logger.error(f"Error submitting form: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _prepare_for_login(self, driver):
        """Prepare browser session to avoid bot detection"""
        try:
            logger.info("Preparing browser session to avoid bot detection...")

            # First, visit TikTok homepage to establish session
            logger.info("Visiting TikTok homepage to establish session...")
            driver.get("https://www.tiktok.com")
            time.sleep(random.uniform(3, 6))

            # Simulate human browsing behavior
            self._simulate_human_browsing(driver)

            # Handle any initial popups/cookies
            self._handle_initial_popups(driver)

            # Wait and simulate more human behavior
            time.sleep(random.uniform(2, 4))

            # Scroll around like a human
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(200, 500)
                driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(1, 2))

            # Scroll back to top
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(1, 2))

            logger.info("Browser session preparation completed")

        except Exception as e:
            logger.warning(f"Error in login preparation: {str(e)}")

    def _simulate_human_browsing(self, driver):
        """Simulate human browsing patterns"""
        try:
            # Move mouse randomly
            if hasattr(self, 'anti_detection') and self.anti_detection:
                self.anti_detection.simulate_mouse_movement(driver)

            # Random page interactions
            actions = [
                lambda: driver.execute_script("window.scrollBy(0, 100);"),
                lambda: driver.execute_script("window.scrollBy(0, -50);"),
                lambda: time.sleep(random.uniform(0.5, 1.5)),
            ]

            for _ in range(random.randint(2, 4)):
                action = random.choice(actions)
                action()
                time.sleep(random.uniform(0.5, 1))

        except Exception as e:
            logger.debug(f"Error in human browsing simulation: {str(e)}")

    def _is_2fa_required(self, driver) -> bool:
        """Check if 2FA is required"""
        try:
            # Look for 2FA indicators
            tfa_selectors = [
                'input[placeholder*="verification"]',
                'input[placeholder*="code"]',
                '[data-testid="verification-code"]',
                '.verification-code-input'
            ]

            for selector in tfa_selectors:
                try:
                    driver.find_element(By.CSS_SELECTOR, selector)
                    return True
                except NoSuchElementException:
                    continue

            return False

        except Exception:
            return False

    def _handle_2fa(self, driver, two_factor_code: str) -> Dict:
        """Handle 2FA verification"""
        try:
            # Find 2FA input field
            tfa_selectors = [
                'input[placeholder*="verification"]',
                'input[placeholder*="code"]',
                '[data-testid="verification-code"]',
                '.verification-code-input'
            ]

            tfa_field = None
            for selector in tfa_selectors:
                try:
                    tfa_field = driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue

            if not tfa_field:
                return {'success': False, 'error': '2FA field not found'}

            # Enter 2FA code
            tfa_field.clear()
            for char in two_factor_code:
                tfa_field.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            time.sleep(random.uniform(1, 2))

            # Submit 2FA
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            submit_button.click()
            time.sleep(random.uniform(3, 5))

            return {'success': True}

        except Exception as e:
            logger.error(f"Error handling 2FA: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _verify_login_success(self, driver) -> Dict:
        """Verify if login was successful"""
        try:
            # Check for login success indicators
            success_indicators = [
                '[data-testid="user-avatar"]',
                '.avatar',
                '[href*="/profile"]',
                '.user-info'
            ]

            for indicator in success_indicators:
                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, indicator))
                    )
                    return {'success': True}
                except TimeoutException:
                    continue

            # Check for error messages
            error_selectors = [
                '.error-message',
                '[data-testid="error"]',
                '.login-error'
            ]

            for selector in error_selectors:
                try:
                    error_element = driver.find_element(By.CSS_SELECTOR, selector)
                    error_text = error_element.text
                    if error_text:
                        return {'success': False, 'error': f'Login error: {error_text}'}
                except NoSuchElementException:
                    continue

            return {'success': False, 'error': 'Login verification failed - no success indicators found'}

        except Exception as e:
            logger.error(f"Error verifying login: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _handle_initial_popups(self, driver):
        """Handle initial popups and consent dialogs"""
        try:
            # Cookie consent
            consent_selectors = [
                '[data-testid="cookie-consent-accept"]',
                '.cookie-consent-accept',
                'button[aria-label="Accept"]',
                'button[title="Accept"]'
            ]

            for selector in consent_selectors:
                try:
                    element = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()
                    time.sleep(random.uniform(1, 2))
                    break
                except TimeoutException:
                    continue

            # App download popup
            app_popup_selectors = [
                '[data-testid="modal-close-inner-button"]',
                '.modal-close',
                'button[aria-label="Close"]',
                '.close-button'
            ]

            for selector in app_popup_selectors:
                try:
                    element = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()
                    time.sleep(random.uniform(1, 2))
                    break
                except TimeoutException:
                    continue

        except Exception as e:
            logger.warning(f"Error handling initial popups: {str(e)}")

    def _navigate_with_referrer(self, driver, url):
        """Navigate to URL with referrer simulation"""
        try:
            # First navigate to a referrer page
            referrer_urls = [
                "https://www.google.com",
                "https://www.tiktok.com",
                "https://www.tiktok.com/foryou"
            ]

            referrer = random.choice(referrer_urls)
            driver.get(referrer)
            time.sleep(random.uniform(2, 4))

            # Then navigate to target URL
            driver.get(url)
            time.sleep(random.uniform(3, 5))

        except Exception as e:
            logger.warning(f"Error navigating with referrer: {str(e)}")
            # Fallback to direct navigation
            driver.get(url)
            time.sleep(random.uniform(3, 5))
