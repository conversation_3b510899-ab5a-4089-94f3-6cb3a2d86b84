import time
import json
import logging
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from django.utils import timezone
from .anti_detection import AntiDetectionManager
from ..config.bot_detection_config import (
    BOT_DETECTION_INDICATORS,
    RATE_LIMITING_INDICATORS,
    CAPTCHA_INDICATORS,
    HUMAN_BEHAVIOR_CONFIG,
    RETRY_CONFIG,
    COOLDOWN_PERIODS
)

logger = logging.getLogger(__name__)

class TikTokAuthenticator:
    """
    Handles TikTok authentication with anti-detection measures
    """
    
    def __init__(self):
        self.base_url = "https://www.tiktok.com"
        self.login_url = "https://www.tiktok.com/login/phone-or-email/email"
        self.anti_detection = AntiDetectionManager()
    
    def login(self, driver, username, password, use_2fa=False, two_factor_code=None, retry_count=0):
        """
        Perform TikTok login using undetected-chromedriver to bypass bot detection
        
        Args:
            driver: Selenium WebDriver instance (will be replaced with undetected-chromedriver)
            username: TikTok username or email
            password: TikTok password
            use_2fa: Whether to use 2FA
            two_factor_code: 2FA code if available
            retry_count: Current retry attempt number
        
        Returns:
            dict: Login result with success status and session data
        """
        try:
            from actor_tiktok.utils.undetected_chrome_auth import UndetectedChromeAuthenticator
            
            logger.info(f"Starting TikTok login with undetected-chromedriver for username: {username} (attempt {retry_count + 1})")
            
            # Check if account is temporarily blocked
            if hasattr(self, 'account') and self.account.is_blocked:
                if self.account.blocked_until and self.account.blocked_until > time.time():
                    remaining_time = (self.account.blocked_until - time.time()) / 60
                    return {
                        'success': False,
                        'error': f'Account temporarily blocked. Try again in {remaining_time:.0f} minutes.',
                        'blocked': True
                    }
            
            # Create undetected Chrome authenticator
            undetected_auth = UndetectedChromeAuthenticator()
            
            # Close the existing driver if provided
            if driver:
                try:
                    driver.quit()
                except Exception as e:
                    logger.warning(f"Error closing existing driver: {str(e)}")
            
            # Create a new undetected Chrome driver
            try:
                # Get proxy configuration if available
                proxy = None
                if hasattr(self, 'proxy') and self.proxy:
                    proxy = self.proxy
                
                # Get headless mode configuration if available
                headless = False
                if hasattr(self, 'headless'):
                    headless = self.headless
                
                # Setup undetected Chrome driver
                undetected_driver = undetected_auth.setup_driver(headless=headless, proxy=proxy)
                logger.info("Successfully created undetected Chrome driver")
                
                # Perform login with undetected Chrome driver
                result = undetected_auth.login(
                    undetected_driver, 
                    username, 
                    password, 
                    use_2fa, 
                    two_factor_code, 
                    retry_count
                )
                
                # Add metadata to result
                result['undetected_chrome'] = True
                
                # Return the driver in the result for further use
                result['driver'] = undetected_driver
                
                return result
                
            except Exception as e:
                logger.error(f"Error with undetected Chrome driver: {str(e)}")
                
                # Fallback to legacy login strategies if undetected Chrome fails
                logger.warning("Falling back to legacy login strategies")
                
                # Try multiple login strategies (legacy approach)
                login_strategies = [
                    ('direct', self._login_strategy_direct),
                    ('alternative_url', self._login_strategy_alternative_url),
                    ('mobile_view', self._login_strategy_mobile_view)
                ]
                
                # Create a new regular driver
                driver = self.anti_detection.setup_driver(headless=headless, proxy=proxy)
                
                last_error = None
                strategies_attempted = ['undetected_chrome_failed']
                strategy_results = [{
                    'strategy': 'undetected_chrome',
                    'success': False,
                    'error': str(e)
                }]
                
                for strategy_index, (strategy_name, strategy_func) in enumerate(login_strategies):
                    try:
                        logger.info(f"Attempting legacy login strategy {strategy_index + 1}/{len(login_strategies)}: {strategy_name}")
                        strategies_attempted.append(strategy_name)
                        
                        result = strategy_func(driver, username, password, use_2fa, two_factor_code, retry_count)
                        strategy_results.append({
                            'strategy': strategy_name,
                            'success': result['success'],
                            'error': result.get('error', 'No error')
                        })
                        
                        if result['success']:
                            logger.info(f"Login successful using legacy strategy {strategy_index + 1}: {strategy_name}")
                            result['strategies_attempted'] = strategies_attempted
                            result['strategy_results'] = strategy_results
                            result['driver'] = driver
                            return result
                        else:
                            last_error = result.get('error', 'Unknown error')
                            logger.warning(f"Legacy strategy {strategy_index + 1} ({strategy_name}) failed: {last_error}")
                            
                            # If we get specific errors that indicate account issues, don't try other strategies
                            critical_errors = ['blocked', '2fa required', 'invalid credentials', 'account locked']
                            if any(error_type in last_error.lower() for error_type in critical_errors):
                                result['strategies_attempted'] = strategies_attempted
                                result['strategy_results'] = strategy_results
                                result['driver'] = driver
                                return result
                            
                            # For bot detection or rate limiting, try other strategies but with shorter delays
                            if any(error_type in last_error.lower() for error_type in ['bot', 'captcha', 'rate limit']):
                                if strategy_index < len(login_strategies) - 1:
                                    wait_time = random.uniform(15, 30)  # Shorter wait for these errors
                                    logger.info(f"Waiting {wait_time:.1f} seconds before trying next strategy")
                                    time.sleep(wait_time)
                            else:
                                # For other errors, use normal wait time
                                if strategy_index < len(login_strategies) - 1:
                                    wait_time = random.uniform(30, 60)
                                    logger.info(f"Waiting {wait_time:.1f} seconds before trying next strategy")
                                    time.sleep(wait_time)
                                
                    except Exception as e:
                        last_error = str(e)
                        logger.error(f"Legacy strategy {strategy_index + 1} ({strategy_name}) threw exception: {last_error}")
                        strategy_results.append({
                            'strategy': strategy_name,
                            'success': False,
                            'error': last_error
                        })
                        continue
                
                # All strategies failed
                return {
                    'success': False,
                    'error': f'All login strategies failed. Last error: {last_error}',
                    'strategy_exhausted': True,
                    'strategies_attempted': strategies_attempted,
                    'strategy_results': strategy_results,
                    'driver': driver
                }
        
        except Exception as e:
            logger.error(f"Error during TikTok login: {str(e)}")
            return {
                'success': False,
                'error': f'Login failed: {str(e)}'
            }
    
    def _wait_for_page_load(self, driver, timeout=10):
        """Wait for page to load completely with JavaScript error detection"""
        try:
            WebDriverWait(driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # Check for JavaScript errors that might affect login
            js_errors = self._check_javascript_errors(driver)
            if js_errors:
                logger.info(f"JavaScript errors detected: {js_errors}")
                
                # Check for atob-related errors specifically
                atob_errors = [error for error in js_errors 
                              if 'atob' in error.get('message', '').lower() or 
                                 'invalidcharactererror' in error.get('message', '').lower()]
                
                if atob_errors:
                    logger.warning(f"atob/base64 errors detected, injecting polyfill: {atob_errors}")
                    self._handle_atob_error(driver)
                
                # Try to clear errors and reload if critical
                if self._is_critical_js_error(js_errors):
                    logger.info("Critical JavaScript error detected, attempting page refresh")
                    driver.refresh()
                    time.sleep(random.uniform(3, 5))
                    WebDriverWait(driver, timeout).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )
                    # Re-inject polyfill after refresh
                    self._handle_atob_error(driver)
            
            # Proactively inject atob polyfill on TikTok pages
            try:
                current_url = driver.current_url
                if 'tiktok.com' in current_url:
                    self._handle_atob_error(driver)
            except Exception as e:
                logger.debug(f"Failed to inject proactive atob polyfill: {str(e)}")
            
            # Additional wait for dynamic content
            time.sleep(random.uniform(2, 4))
        except TimeoutException:
            logger.warning("Page load timeout, continuing anyway")
    
    def _handle_cookie_consent(self, driver):
        """Handle cookie consent popup if present"""
        try:
            # Common cookie consent selectors
            consent_selectors = [
                '[data-testid="cookie-consent-accept"]',
                '.cookie-consent-accept',
                '[aria-label*="Accept"]',
                'button[contains(text(), "Accept")]'
            ]
            
            for selector in consent_selectors:
                try:
                    consent_button = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    self.anti_detection.human_like_click(driver, consent_button)
                    logger.info("Accepted cookie consent")
                    time.sleep(random.uniform(1, 2))
                    return True
                except TimeoutException:
                    continue
            
            return False
        except Exception as e:
            logger.warning(f"Error handling cookie consent: {str(e)}")
            return False
    
    def _handle_app_popup(self, driver):
        """Handle 'Use app' popup/modal that sometimes appears on TikTok login page"""
        try:
            logger.info("Checking for app popup/modal...")
            
            # Common app popup selectors for "Not now" or "Continue in browser" buttons
            app_popup_selectors = [
                # Specific "Not now" button selector from TikTok login popup
                'button[data-e2e="bottom-cta-cancel-btn"]',
                # Other "Not now" button selectors
                'button[data-testid="not-now-button"]',
                'button[data-e2e="not-now-button"]',
                '[data-testid*="not-now"]',
                '[data-e2e*="not-now"]',
                'button[aria-label*="Not now"]',
                'button[aria-label*="not now"]',
                # "Continue in browser" selectors
                'button[data-testid="continue-browser"]',
                'button[data-e2e="continue-browser"]',
                '[data-testid*="continue-browser"]',
                '[data-e2e*="continue-browser"]',
                'button[aria-label*="Continue in browser"]',
                'button[aria-label*="continue in browser"]',
                # Generic close/dismiss selectors
                'button[data-testid="close-modal"]',
                'button[data-e2e="close-modal"]',
                '[data-testid*="close"]',
                '[data-e2e*="close"]',
                'button[aria-label*="Close"]',
                'button[aria-label*="close"]',
                # Class-based selectors
                '.modal-close-button',
                '.popup-close',
                '.app-banner-close',
                'button[class*="close"]',
                'button[class*="dismiss"]',
                # Generic modal overlay close
                '.modal-overlay button',
                '.popup-overlay button'
            ]
            
            # XPath selectors for text-based matching
            app_popup_xpath_selectors = [
                # "Not now" text matches (case-insensitive)
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "Not now")]',
                '//button[contains(translate(normalize-space(text()), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "Not now")]',
                '//div[@role="button" and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "Not now")]',
                # "Continue in browser" text matches
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "continue in browser")]',
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "continue browser")]',
                '//a[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "continue in browser")]',
                # "Skip" or "Later" text matches
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "skip")]',
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "later")]',
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "maybe later")]',
                # Close button with X or × symbol
                '//button[contains(text(), "×") or contains(text(), "✕") or contains(text(), "X")]',
                # Generic close patterns
                '//*[@role="button" and (contains(@aria-label, "close") or contains(@aria-label, "Close"))]',
                '//*[@role="button" and (contains(@title, "close") or contains(@title, "Close"))]'
            ]
            
            # Try CSS selectors first
            for selector in app_popup_selectors:
                try:
                    popup_button = WebDriverWait(driver, 2).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    if popup_button.is_displayed():
                        logger.info(f"Found app popup button with selector: {selector}")
                        self.anti_detection.human_like_click(driver, popup_button)
                        logger.info("Successfully dismissed app popup")
                        time.sleep(random.uniform(1, 2))
                        return True
                except (TimeoutException, Exception):
                    continue
            
            # Try XPath selectors
            for xpath_selector in app_popup_xpath_selectors:
                try:
                    popup_button = WebDriverWait(driver, 2).until(
                        EC.element_to_be_clickable((By.XPATH, xpath_selector))
                    )
                    if popup_button.is_displayed():
                        logger.info(f"Found app popup button with XPath: {xpath_selector}")
                        self.anti_detection.human_like_click(driver, popup_button)
                        logger.info("Successfully dismissed app popup")
                        time.sleep(random.uniform(1, 2))
                        return True
                except (TimeoutException, Exception):
                    continue
            
            # Check for modal overlay and try to click outside to dismiss
            try:
                modal_overlay = driver.find_element(By.CSS_SELECTOR, '.modal-overlay, .popup-overlay, [class*="modal"][class*="overlay"]')
                if modal_overlay.is_displayed():
                    logger.info("Found modal overlay, trying to click outside to dismiss")
                    # Click on the overlay (outside the modal content)
                    self.anti_detection.human_like_click(driver, modal_overlay)
                    time.sleep(random.uniform(1, 2))
                    return True
            except Exception:
                pass
            
            logger.info("No app popup found or already dismissed")
            return False
            
        except Exception as e:
            logger.warning(f"Error handling app popup: {str(e)}")
            return False
    
    def _handle_get_full_app_modal(self, driver):
        """Handle 'Get full app experience' modal that sometimes appears on TikTok login page"""
        try:
            logger.info("Checking for 'Get full app experience' modal...")
            
            # Look for the modal and "Not now" button
            modal_selectors = [
                # Modal container selectors
                '[data-testid="app-download-modal"]',
                '[data-e2e="app-download-modal"]',
                '.app-download-modal',
                '.get-app-modal',
                '[class*="modal"][class*="app"]',
                # Generic modal selectors
                '.modal-overlay',
                '.popup-overlay',
                '[role="dialog"]',
                '[aria-modal="true"]'
            ]
            
            # "Not now" button selectors
            not_now_selectors = [
                # Specific "Not now" button selectors
                'button[data-e2e="bottom-cta-cancel-btn"]',
                'button[data-testid="not-now-button"]',
                'button[data-e2e="not-now-button"]',
                '[data-testid*="not-now"]',
                '[data-e2e*="not-now"]',
                'button[aria-label*="Not now"]',
                'button[aria-label*="not now"]',
                # Text-based selectors
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "not now")]',
                '//button[contains(translate(normalize-space(text()), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "not now")]',
                '//div[@role="button" and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "not now")]'
            ]
            
            # First check if modal is present
            modal_found = False
            for selector in modal_selectors:
                try:
                    modal = WebDriverWait(driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if modal.is_displayed():
                        logger.info(f"Found 'Get full app experience' modal with selector: {selector}")
                        modal_found = True
                        break
                except (TimeoutException, Exception):
                    continue
            
            if not modal_found:
                logger.info("No 'Get full app experience' modal found")
                return True
            
            # Try to find and click "Not now" button
            for selector in not_now_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        not_now_button = WebDriverWait(driver, 2).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        # CSS selector
                        not_now_button = WebDriverWait(driver, 2).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    if not_now_button.is_displayed():
                        logger.info(f"Found 'Not now' button with selector: {selector}")
                        self.anti_detection.human_like_click(driver, not_now_button)
                        logger.info("Successfully clicked 'Not now' button")
                        time.sleep(random.uniform(1, 2))
                        return True
                except (TimeoutException, Exception):
                    continue
            
            # If no "Not now" button found, try to click outside modal to dismiss
            try:
                modal_overlay = driver.find_element(By.CSS_SELECTOR, '.modal-overlay, .popup-overlay, [class*="modal"][class*="overlay"]')
                if modal_overlay.is_displayed():
                    logger.info("Trying to dismiss modal by clicking outside")
                    self.anti_detection.human_like_click(driver, modal_overlay)
                    time.sleep(random.uniform(1, 2))
                    return True
            except Exception:
                pass
            
            logger.warning("Could not dismiss 'Get full app experience' modal")
            return False
            
        except Exception as e:
            logger.warning(f"Error handling 'Get full app experience' modal: {str(e)}")
            return False
    
    def _handle_email_username_selection(self, driver):
        """Handle email/username tab selection or direct link clicking (Step 4)"""
        try:
            logger.info("Handling email/username selection...")
            
            # Step 4a: Check if tab selection is needed
            email_tab_element = self._find_element_with_dynamic_selectors(driver, 'email_username_tab', timeout=5)
            
            if email_tab_element:
                logger.info("Found email/username tab, clicking it")
                if self._click_element_with_parent_fallback(driver, email_tab_element):
                    logger.info("Successfully clicked email/username tab")
                    time.sleep(random.uniform(2, 4))
                    return True
                else:
                    logger.warning("Failed to click email/username tab")
            else:
                logger.info("Email/username tab not found, checking for direct email/username link")
                
                # Step 4b: Try to find and click direct email/username link
                direct_email_selectors = [
                    # Specific selector mentioned by user
                    'a[href="/login/phone-or-email/email"][class="ep888o80 tiktok-1mgli76-ALink-StyledLink epl6mg0"]',
                    # More general selectors
                    'a[href*="phone-or-email/email"]',
                    'a[href*="/login/phone-or-email/email"]',
                    'a[href*="email"][class*="ALink"]',
                    'a[href*="email"][class*="StyledLink"]',
                    # Text-based selectors
                    '//a[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log in with email")]',
                    '//a[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email or username")]',
                    # Generic email login selectors
                    '[data-testid="login-email"]',
                    '[data-testid="email-login"]',
                    'button[data-e2e="email-login"]'
                ]
                
                for selector in direct_email_selectors:
                    try:
                        if selector.startswith('//'):
                            # XPath selector
                            element = WebDriverWait(driver, 2).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                        else:
                            # CSS selector
                            element = WebDriverWait(driver, 2).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                            )
                        
                        if element.is_displayed():
                            logger.info(f"Found direct email/username link with selector: {selector}")
                            self.anti_detection.human_like_click(driver, element)
                            logger.info("Successfully clicked direct email/username link")
                            time.sleep(random.uniform(2, 4))
                            return True
                    except (TimeoutException, Exception):
                        continue
                
                logger.info("No email/username tab or direct link found, assuming form is ready")
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling email/username selection: {str(e)}")
            return False
    
    def _get_dynamic_selectors(self, element_type):
        """Get dynamic selectors for different element types with XPath and CSS combinations"""
        selectors = {
            'phone_email_username': {
                'css': [
                    # Updated TikTok specific selectors for 2024
                    'div[data-testid="login-method-phone-email-username"]',
                    'div[data-e2e="login-method-phone-email-username"]',
                    'div.tiktok-x4fjdp-DivTextContainer.e1cgu1qo3',
                    '.tiktok-x4fjdp-DivTextContainer.e1cgu1qo3',
                    'div[class*="tiktok-x4fjdp-DivTextContainer"]',
                    'div[class*="e1cgu1qo3"]',
                    # Data attribute selectors
                    '[data-testid*="phone-email"]',
                    '[data-testid*="email-phone"]',
                    '[data-e2e*="phone-email"]',
                    '[data-e2e*="login-method"]',
                    '[data-testid*="login-method"]',
                    # Href-based selectors
                    'a[href*="phone-or-email"]',
                    'a[href*="email-phone"]',
                    'a[href*="login/phone"]',
                    # Generic button/link selectors
                    'button[class*="login"]',
                    'div[role="button"][class*="login"]',
                    # More specific class patterns
                    'div[class*="LoginMethodCard"]',
                    'div[class*="login-method"]',
                    '.login-method-card'
                ],
                'xpath': [
                    # Exact text matches (case-insensitive) - Updated for current TikTok
                    '//div[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "use phone") and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email") and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "username")]',
                    '//div[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "phone / email / username")]',
                    '//div[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "phone/email/username")]',
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "phone") and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email")]',
                    '//a[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "phone") and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email")]',
                    # Partial text matches
                    '//div[contains(text(), "Use phone") or contains(text(), "phone / email") or contains(text(), "email / username")]',
                    '//div[contains(text(), "Phone / Email / Username")]',
                    '//button[contains(text(), "phone") and contains(text(), "email")]',
                    # Role-based selectors
                    '//div[@role="button" and (contains(text(), "phone") or contains(text(), "email"))]',
                    '//a[@role="button" and (contains(text(), "phone") or contains(text(), "email"))]',
                    # More flexible text matching
                    '//*[contains(translate(normalize-space(text()), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "use phone")]',
                    '//*[contains(translate(normalize-space(text()), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "phone/email/username")]'
                ]
            },
            'email_username_tab': {
                'css': [
                    # Updated TikTok specific tab selectors for 2024
                    'div[data-e2e="email-tab"]',
                    '[data-e2e="email-tab"]',
                    'div[data-testid="email-username-tab"]',
                    '[data-testid="email-username-tab"]',
                    'div.tiktok-10zo0qw-DivTab.e1ntpldy1',
                    '.tiktok-10zo0qw-DivTab.e1ntpldy1',
                    'div[class*="tiktok-10zo0qw-DivTab"]',
                    'div[class*="e1ntpldy1"]',
                    # Generic tab selectors
                    '[data-testid*="email-tab"]',
                    '[data-testid*="username-tab"]',
                    '[data-testid*="email-username"]',
                    '[role="tab"][data-testid*="email"]',
                    '[role="tab"][data-testid*="username"]',
                    'div[role="tab"]',
                    'button[role="tab"]',
                    '.tab[data-value*="email"]',
                    # More specific selectors
                    'div[class*="Tab"][class*="email"]',
                    'div[class*="tab"][class*="email"]',
                    '.login-tab',
                    '.email-tab',
                    '.username-tab'
                ],
                'xpath': [
                    # Text-based tab selectors - Updated for current TikTok
                    '//div[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email") and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "username")][@role="tab" or @role="button"]',
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email")]',
                    '//div[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "username")][@role="tab" or @role="button"]',
                    # Exact matches
                    '//div[normalize-space(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"))="email / username"]',
                    '//div[normalize-space(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"))="email/username"]',
                    '//div[contains(text(), "Email") and contains(text(), "Username")]',
                    '//div[contains(text(), "Email / Username")]',
                    '//div[contains(text(), "Email/Username")]',
                    # Tab role selectors
                    '//div[@role="tab" and (contains(text(), "email") or contains(text(), "Email"))]',
                    '//button[@role="tab" and (contains(text(), "email") or contains(text(), "Email"))]',
                    # More flexible matching
                    '//*[@role="tab" and contains(translate(normalize-space(text()), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email")]',
                    '//*[contains(translate(normalize-space(text()), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email") and (@role="tab" or @role="button" or @tabindex)]'
                ]
            },
            'username_input': {
                'css': [
                    # Primary input selectors - Updated for 2024
                    'input[placeholder="Email or username"]',
                    'input[placeholder="Email address"]',
                    'input[placeholder="Phone number, username, or email"]',
                    'input[name="username"]',
                    'input[name="email"]',
                    'input[type="email"]',
                    'input[type="text"]',
                    # Partial placeholder matches
                    'input[placeholder*="Email or username"]',
                    'input[placeholder*="email"]',
                    'input[placeholder*="username"]',
                    'input[placeholder*="Email"]',
                    'input[placeholder*="Username"]',
                    'input[placeholder*="phone"]',
                    'input[placeholder*="Phone"]',
                    # Data attributes - Updated
                    '[data-testid="email-input"]',
                    '[data-testid="username-input"]',
                    '[data-testid="login-email"]',
                    '[data-testid="login-username"]',
                    '[data-e2e="email-input"]',
                    '[data-e2e="username-input"]',
                    '[data-e2e="login-email"]',
                    '[data-e2e="login-username"]',
                    # Autocomplete attributes
                    'input[autocomplete="email"]',
                    'input[autocomplete="username"]',
                    'input[autocomplete="tel"]',
                    # ID and class based
                    'input[id*="email"]',
                    'input[id*="username"]',
                    'input[id*="login"]',
                    'input[class*="email"]',
                    'input[class*="username"]',
                    'input[class*="login"]',
                    # Aria attributes
                    'input[aria-label*="email"]',
                    'input[aria-label*="username"]',
                    'input[aria-label*="Email"]',
                    'input[aria-label*="Username"]',
                    # Generic fallbacks
                    'form input[type="text"]:first-of-type',
                    '.login-form input[type="text"]',
                    '.email-input',
                    '.username-input'
                ],
                'xpath': [
                    # Placeholder-based XPath selectors
                    '//input[contains(translate(@placeholder, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email")]',
                    '//input[contains(translate(@placeholder, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "username")]',
                    '//input[contains(translate(@placeholder, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "phone")]',
                    '//input[contains(translate(@placeholder, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email or username")]',
                    # Type-based selectors
                    '//input[@type="text" or @type="email" or @type="tel"]',
                    # Name and ID based selectors
                    '//input[contains(@name, "username") or contains(@name, "email")]',
                    '//input[contains(@id, "username") or contains(@id, "email") or contains(@id, "login")]',
                    # Aria-label based selectors
                    '//input[contains(translate(@aria-label, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "email")]',
                    '//input[contains(translate(@aria-label, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "username")]',
                    # Form context selectors
                    '//form//input[@type="text"][1]',
                    '//div[contains(@class, "login")]//input[@type="text"]',
                    # More flexible matching
                    '//input[(@type="text" or @type="email") and (contains(@placeholder, "@") or contains(@name, "email") or contains(@name, "username"))]'
                ]
            },
            'password_input': {
                'css': [
                    # Primary password selectors - Updated for 2024
                    'input[type="password"]',
                    'input[name="password"]',
                    'input[name="pwd"]',
                    # Placeholder-based
                    'input[placeholder*="Password"]',
                    'input[placeholder*="password"]',
                    'input[placeholder="Password"]',
                    'input[placeholder="Enter password"]',
                    'input[placeholder="Enter your password"]',
                    # Data attributes - Updated
                    '[data-testid="password-input"]',
                    '[data-testid="login-password"]',
                    '[data-e2e="password-input"]',
                    '[data-e2e="login-password"]',
                    # Autocomplete
                    'input[autocomplete="current-password"]',
                    'input[autocomplete="password"]',
                    # ID and class based
                    'input[id*="password"]',
                    'input[id*="pwd"]',
                    'input[id*="login"][type="password"]',
                    'input[class*="password"]',
                    'input[class*="pwd"]',
                    # Aria attributes
                    'input[aria-label*="password"]',
                    'input[aria-label*="Password"]',
                    # Generic fallbacks
                    '.password-input',
                    '.login-password',
                    'form input[type="password"]'
                ],
                'xpath': [
                    # Type-based selectors
                    '//input[@type="password"]',
                    # Placeholder-based XPath selectors
                    '//input[contains(translate(@placeholder, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "password")]',
                    # Name and ID based selectors
                    '//input[contains(@name, "password") or contains(@name, "pwd")]',
                    '//input[contains(@id, "password") or contains(@id, "pwd")]',
                    # Aria-label based selectors
                    '//input[contains(translate(@aria-label, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "password")]',
                    # Form context selectors
                    '//form//input[@type="password"]',
                    '//div[contains(@class, "login")]//input[@type="password"]'
                ]
            },
            'login_button': {
                'css': [
                    # Primary button selectors - Updated for 2024
                    'button[type="submit"]',
                    'button[data-testid="login-submit"]',
                    'button[data-testid="submit"]',
                    'button[data-testid="login-button"]',
                    'button[data-e2e="login-submit"]',
                    'button[data-e2e="submit-button"]',
                    'button[data-e2e="login-button"]',
                    '.login-submit',
                    '.submit-btn',
                    '.login-btn',
                    '.login-button',
                    '[data-e2e="submit-button"]',
                    'form button[type="button"]',
                    'form button:not([type="button"])',
                    'div[role="button"][tabindex="0"]',
                    # ID and class based
                    'button[id*="login"]',
                    'button[id*="submit"]',
                    'button[class*="login"]',
                    'button[class*="submit"]',
                    # Aria attributes
                    'button[aria-label*="log in"]',
                    'button[aria-label*="login"]',
                    'button[aria-label*="sign in"]',
                    'button[aria-label*="submit"]',
                    # Generic fallbacks
                    'form button:last-of-type',
                    '.form-submit',
                    '.btn-primary',
                    '.primary-button'
                ],
                'xpath': [
                    # Text-based button selectors - Updated
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log in")]',
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "sign in")]',
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "login")]',
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "continue")]',
                    '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "submit")]',
                    '//button[normalize-space(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"))="log in"]',
                    '//button[normalize-space(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"))="login"]',
                    '//div[@role="button" and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log in")]',
                    '//div[@role="button" and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "login")]',
                    # Aria-label based selectors
                    '//button[contains(translate(@aria-label, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log in")]',
                    '//button[contains(translate(@aria-label, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "login")]',
                    '//button[contains(translate(@aria-label, "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "sign in")]',
                    # Form context selectors
                    '//form//button[@type="submit"]',
                    '//form//button[last()]',
                    '//div[contains(@class, "login")]//button',
                    # More flexible matching
                    '//*[@role="button" and (contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log") or contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "sign"))]'
                ]
            }
        }
        return selectors.get(element_type, {'css': [], 'xpath': []})
    
    def _find_element_with_dynamic_selectors(self, driver, element_type, timeout=5):
        """Find element using dynamic selectors with CSS and XPath fallbacks"""
        selectors = self._get_dynamic_selectors(element_type)
        
        # Try CSS selectors first
        for selector in selectors.get('css', []):
            try:
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                if element.is_displayed():
                    logger.info(f"Found {element_type} using CSS selector: {selector}")
                    return element
            except (TimeoutException, Exception) as e:
                logger.debug(f"CSS selector {selector} failed: {str(e)}")
                continue
        
        # Try XPath selectors as fallback
        for selector in selectors.get('xpath', []):
            try:
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                if element.is_displayed():
                    logger.info(f"Found {element_type} using XPath selector: {selector}")
                    return element
            except (TimeoutException, Exception) as e:
                logger.debug(f"XPath selector {selector} failed: {str(e)}")
                continue
        
        return None
    
    def _click_element_with_parent_fallback(self, driver, element):
        """Click element with parent fallback for non-clickable elements"""
        try:
            # Try clicking the element directly
            WebDriverWait(driver, 2).until(EC.element_to_be_clickable(element))
            self.anti_detection.human_like_click(driver, element)
            return True
        except TimeoutException:
            # Try parent elements if direct click fails
            try:
                parent = element.find_element(By.XPATH, '..')
                WebDriverWait(driver, 1).until(EC.element_to_be_clickable(parent))
                self.anti_detection.human_like_click(driver, parent)
                return True
            except (TimeoutException, Exception):
                # Try grandparent
                try:
                    grandparent = parent.find_element(By.XPATH, '..')
                    WebDriverWait(driver, 1).until(EC.element_to_be_clickable(grandparent))
                    self.anti_detection.human_like_click(driver, grandparent)
                    return True
                except (TimeoutException, Exception):
                    return False
    
    def _select_login_method(self, driver):
        """Enhanced login method selection following the 8-step process"""
        try:
            # Step 1: Access login page (already done by caller)
            current_url = driver.current_url
            logger.info(f"Starting 8-step login method selection on URL: {current_url}")
            
            # Wait for page to fully load and inject polyfills
            time.sleep(random.uniform(3, 5))
            self._handle_atob_error(driver)  # Proactive error prevention
            
            # Take screenshot for debugging
            try:
                driver.save_screenshot('/tmp/tiktok_login_debug.png')
                logger.info("Debug screenshot saved")
            except Exception:
                pass
            
            # Step 2: Handle "Get full app experience" modal if present
            self._handle_get_full_app_modal(driver)
            
            # Step 3: Select method login "Use phone / email / username"
            # Try user-provided specific selector first
            specific_selectors = [
                # User-provided specific selector
                'div[data-e2e="channel-item"][tabindex="0"][role="link"][class="tiktok-17hparj-DivBoxContainer e1cgu1qo0"]',
                # More general variations of the user selector
                'div[data-e2e="channel-item"][role="link"]',
                'div[class*="tiktok-17hparj-DivBoxContainer"]',
                'div[class*="e1cgu1qo0"]',
                # Text-based selector for the specific content
                '//div[contains(text(), "Use phone / email / username")]',
                '//div[contains(text(), "Use phone") and contains(text(), "email") and contains(text(), "username")]'
            ]
            
            phone_email_element = None
            
            # First try the specific selectors
            for selector in specific_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        element = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        # CSS selector
                        element = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    if element.is_displayed():
                        logger.info(f"Found 'Use phone / email / username' option with specific selector: {selector}")
                        phone_email_element = element
                        break
                except (TimeoutException, Exception):
                    continue
            
            # If specific selectors didn't work, fall back to dynamic selectors
            if not phone_email_element:
                phone_email_element = self._find_element_with_dynamic_selectors(driver, 'phone_email_username', timeout=8)
            
            if phone_email_element:
                if self._click_element_with_parent_fallback(driver, phone_email_element):
                    logger.info("Successfully clicked phone/email/username option")
                    time.sleep(random.uniform(2, 4))
                else:
                    logger.warning("Failed to click phone/email/username option")
                    return False
            else:
                logger.info("Phone/email/username option not found, checking if form is already available")
                # Check if login form is already available (skip method selection)
                username_field = self._find_element_with_dynamic_selectors(driver, 'username_input', timeout=3)
                password_field = self._find_element_with_dynamic_selectors(driver, 'password_input', timeout=3)
                
                if username_field and password_field:
                    logger.info("Login form already available, skipping method selection")
                    return True
                else:
                    logger.error("Cannot find phone/email/username option and form not available")
                    return False
            
            # Step 4: Handle tab selection or direct email/username link
            return self._handle_email_username_selection(driver)
                    
            # Final verification: Check if login form is now available
            username_field = self._find_element_with_dynamic_selectors(driver, 'username_input', timeout=8)
            password_field = self._find_element_with_dynamic_selectors(driver, 'password_input', timeout=5)
            
            if username_field and password_field:
                logger.info("Login form successfully detected and ready")
                return True
            else:
                logger.error("Login form not found after all method selection attempts")
                # Try one more time with a longer wait
                time.sleep(3)
                username_field = self._find_element_with_dynamic_selectors(driver, 'username_input', timeout=5)
                password_field = self._find_element_with_dynamic_selectors(driver, 'password_input', timeout=3)
                
                if username_field and password_field:
                    logger.info("Login form found after extended wait")
                    return True
                else:
                    return False

        
        except Exception as e:
            logger.error(f"Error selecting login method: {str(e)}")
            return False
    
    def _enter_credentials(self, driver, username, password):
        """Enter username and password using specific selectors (Steps 6-7)"""
        try:
            # Wait for form to be ready
            time.sleep(random.uniform(2, 4))
            
            # Step 6: Find username/email field using specific selector
            username_selectors = [
                # User-provided specific selector
                'input[type="text"][placeholder="Email or username"][autocomplete="webauthn"][name="username"][class="tiktok-11to27l-InputContainer etcs7ny1"]',
                # More general variations
                'input[placeholder="Email or username"][name="username"]',
                'input[placeholder="Email or username"]',
                'input[name="username"][type="text"]',
                'input[autocomplete="webauthn"][name="username"]',
                'input[class*="InputContainer"][name="username"]',
                # Fallback to dynamic selectors
                *[selector for selector in self._get_dynamic_selectors('username_input')]
            ]
            
            username_field = None
            for selector in username_selectors:
                try:
                    username_field = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if username_field.is_displayed():
                        logger.info(f"Found username field with selector: {selector}")
                        break
                except (TimeoutException, Exception):
                    continue
            
            if not username_field:
                logger.error("Username field not found with any selector")
                return {'success': False, 'error': 'Could not find username field'}
            
            # Step 7: Find password field using specific selector
            password_selectors = [
                # User-provided specific selector
                'input[type="password"][placeholder="Password"][autocomplete="new-password"][class="tiktok-wv3bkt-InputContainer etcs7ny1"]',
                # More general variations
                'input[placeholder="Password"][type="password"]',
                'input[type="password"][autocomplete="new-password"]',
                'input[type="password"][class*="InputContainer"]',
                'input[type="password"]',
                # Fallback to dynamic selectors
                *[selector for selector in self._get_dynamic_selectors('password_input')]
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if password_field.is_displayed():
                        logger.info(f"Found password field with selector: {selector}")
                        break
                except (TimeoutException, Exception):
                    continue
            
            if not password_field:
                logger.error("Password field not found with any selector")
                return {'success': False, 'error': 'Could not find password field'}
            
            # Clear and enter username with enhanced interaction
            try:
                # Scroll to username field if needed
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", username_field)
                time.sleep(random.uniform(0.5, 1.0))
                
                # Click and focus on username field
                self.anti_detection.human_like_click(driver, username_field)
                time.sleep(random.uniform(0.5, 1.0))
                
                # Clear field using multiple methods for reliability
                username_field.clear()
                username_field.send_keys(Keys.CONTROL + "a")
                username_field.send_keys(Keys.DELETE)
                time.sleep(random.uniform(0.3, 0.7))
                
                # Use human-like typing if available, otherwise manual typing
                if hasattr(self.anti_detection, 'human_like_type'):
                    self.anti_detection.human_like_type(username_field, username)
                else:
                    # Fallback to manual character-by-character typing
                    for char in username:
                        username_field.send_keys(char)
                        time.sleep(random.uniform(0.05, 0.15))
                
                logger.info("Username entered successfully")
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                logger.error(f"Error entering username: {str(e)}")
                return {'success': False, 'error': f'Failed to enter username: {str(e)}'}
            
            # Clear and enter password with enhanced interaction
            try:
                # Scroll to password field if needed
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", password_field)
                time.sleep(random.uniform(0.5, 1.0))
                
                # Click and focus on password field
                self.anti_detection.human_like_click(driver, password_field)
                time.sleep(random.uniform(0.5, 1.0))
                
                # Clear field using multiple methods for reliability
                password_field.clear()
                password_field.send_keys(Keys.CONTROL + "a")
                password_field.send_keys(Keys.DELETE)
                time.sleep(random.uniform(0.3, 0.7))
                
                # Use human-like typing if available, otherwise manual typing
                if hasattr(self.anti_detection, 'human_like_type'):
                    self.anti_detection.human_like_type(password_field, password)
                else:
                    # Fallback to manual character-by-character typing
                    for char in password:
                        password_field.send_keys(char)
                        time.sleep(random.uniform(0.05, 0.15))
                
                logger.info("Password entered successfully")
                time.sleep(random.uniform(1, 2))
                
            except Exception as e:
                logger.error(f"Error entering password: {str(e)}")
                return {'success': False, 'error': f'Failed to enter password: {str(e)}'}
            
            # Verify fields are filled
            try:
                username_value = username_field.get_attribute('value')
                password_value = password_field.get_attribute('value')
                
                if len(username_value) > 0 and len(password_value) > 0:
                    logger.info("Credentials verification successful")
                    return {'success': True}
                else:
                    logger.warning(f"Credential verification failed - username: {len(username_value)} chars, password: {len(password_value)} chars")
                    return {'success': False, 'error': 'Credentials not properly filled'}
                    
            except Exception as e:
                logger.warning(f"Could not verify credentials: {str(e)}, assuming success")
                return {'success': True}
            
        except Exception as e:
            logger.error(f"Error in credential entry process: {str(e)}")
            return {'success': False, 'error': f'Failed to enter credentials: {str(e)}'}
    
    def _handle_captcha(self, driver):
        """Handle captcha if present"""
        try:
            # Check for common captcha indicators
            captcha_selectors = [
                '.captcha',
                '[data-testid="captcha"]',
                'iframe[src*="captcha"]',
                '.recaptcha',
                '.hcaptcha'
            ]
            
            for selector in captcha_selectors:
                try:
                    captcha_element = driver.find_element(By.CSS_SELECTOR, selector)
                    if captcha_element.is_displayed():
                        logger.warning("Captcha detected - manual intervention may be required")
                        
                        # Wait for user to solve captcha (in a real implementation,
                        # you might integrate with a captcha solving service)
                        time.sleep(30)
                        
                        # Check if captcha is still present
                        if captcha_element.is_displayed():
                            return {
                                'success': False,
                                'error': 'Captcha not solved within timeout period'
                            }
                except NoSuchElementException:
                    continue
            
            return {'success': True}
        
        except Exception as e:
            logger.error(f"Error handling captcha: {str(e)}")
            return {'success': False, 'error': f'Captcha handling failed: {str(e)}'}
    
    def _submit_login_form(self, driver):
        """Submit the login form using specific login button selector (Step 8)"""
        try:
            # Wait for form to be ready for submission
            time.sleep(random.uniform(1, 2))
            
            # Step 8: Find login button using specific selector
            login_button_selectors = [
                # User-provided specific selector
                'button[type="submit"][data-e2e="login-button"][class="e1w6iovg0 tiktok-11sviba-Button-StyledButton ehk74z00"]',
                # More general variations
                'button[data-e2e="login-button"][type="submit"]',
                'button[data-e2e="login-button"]',
                'button[type="submit"][class*="Button-StyledButton"]',
                'button[type="submit"]',
                # Text-based selectors
                '//button[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log in")]',
                '//button[@type="submit" and contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "log in")]',
                # Fallback to dynamic selectors
                *[selector for selector in self._get_dynamic_selectors('login_button')]
            ]
            
            submit_button = None
            for selector in login_button_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        submit_button = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        # CSS selector
                        submit_button = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    if submit_button.is_displayed():
                        logger.info(f"Found login button with selector: {selector}")
                        break
                except (TimeoutException, Exception):
                    continue
            
            if submit_button:
                try:
                    # Scroll to button if needed
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", submit_button)
                    time.sleep(random.uniform(0.5, 1.0))
                    
                    # Click the submit button
                    if self._click_element_with_parent_fallback(driver, submit_button):
                        logger.info("Successfully clicked login button")
                    else:
                        logger.warning("Failed to click login button, trying fallback methods")
                        raise Exception("Login button click failed")
                        
                except Exception as e:
                    logger.warning(f"Login button click failed: {str(e)}, trying fallback methods")
                    # Fallback to Enter key on password field
                    try:
                        password_field = self._find_element_with_dynamic_selectors(driver, 'password_input', timeout=3)
                        if password_field:
                            password_field.send_keys(Keys.RETURN)
                            logger.info("Submitted form using Enter key on password field")
                        else:
                            return {'success': False, 'error': 'Could not find login button or password field for fallback'}
                    except Exception as fallback_error:
                        logger.error(f"Fallback submission failed: {str(fallback_error)}")
                        return {'success': False, 'error': 'All submission methods failed'}
            else:
                # Try pressing Enter on password field as primary fallback
                logger.info("Login button not found, trying Enter key on password field")
                try:
                    password_field = self._find_element_with_dynamic_selectors(driver, 'password_input', timeout=5)
                    if password_field:
                        password_field.send_keys(Keys.RETURN)
                        logger.info("Submitted form using Enter key (no login button found)")
                    else:
                        return {'success': False, 'error': 'Could not find login button or password field'}
                except Exception as e:
                    logger.error(f"Enter key submission failed: {str(e)}")
                    return {'success': False, 'error': 'Could not submit form with any method'}
            
            # Wait for form submission to process
            time.sleep(random.uniform(3, 5))
            
            # Check for immediate errors or redirects
            try:
                current_url = driver.current_url
                logger.info(f"Form submitted, current URL: {current_url}")
                
                # Check for common error indicators
                error_selectors = [
                    '.error-message',
                    '[data-testid="error"]',
                    '.login-error',
                    '.alert-error'
                ]
                
                for selector in error_selectors:
                    try:
                        error_element = driver.find_element(By.CSS_SELECTOR, selector)
                        if error_element.is_displayed():
                            error_text = error_element.text
                            logger.warning(f"Login error detected: {error_text}")
                            return {'success': False, 'error': f'Login error: {error_text}'}
                    except NoSuchElementException:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not check for errors after submission: {str(e)}")
            
            return {'success': True}
        
        except Exception as e:
            logger.error(f"Error submitting login form: {str(e)}")
            return {'success': False, 'error': f'Failed to submit form: {str(e)}'}
    
    def _is_2fa_required(self, driver):
        """Check if 2FA is required"""
        try:
            tfa_selectors = [
                '[data-testid="2fa-input"]',
                'input[placeholder*="verification"]',
                'input[placeholder*="code"]',
                '.two-factor',
                '.verification-code'
            ]
            
            for selector in tfa_selectors:
                try:
                    element = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if element.is_displayed():
                        return True
                except TimeoutException:
                    continue
            
            return False
        
        except Exception as e:
            logger.error(f"Error checking 2FA requirement: {str(e)}")
            return False
    
    def _handle_2fa(self, driver, two_factor_code):
        """Handle 2FA verification"""
        try:
            # Find 2FA input field
            tfa_selectors = [
                '[data-testid="2fa-input"]',
                'input[placeholder*="verification"]',
                'input[placeholder*="code"]',
                '.two-factor input',
                '.verification-code input'
            ]
            
            tfa_field = None
            for selector in tfa_selectors:
                try:
                    tfa_field = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not tfa_field:
                return {'success': False, 'error': 'Could not find 2FA input field'}
            
            # Enter 2FA code
            self.anti_detection.human_like_type(tfa_field, two_factor_code)
            time.sleep(random.uniform(1, 2))
            
            # Submit 2FA form
            tfa_field.send_keys(Keys.RETURN)
            
            # Wait for 2FA verification
            time.sleep(random.uniform(3, 5))
            
            logger.info("Successfully handled 2FA")
            return {'success': True}
        
        except Exception as e:
            logger.error(f"Error handling 2FA: {str(e)}")
            return {'success': False, 'error': f'2FA handling failed: {str(e)}'}
    
    def _get_success_indicators(self):
        """Get comprehensive success indicators for login verification"""
        return {
            'css_selectors': [
                # Avatar or profile elements
                '[data-testid="avatar"]',
                '.avatar',
                '.profile-avatar',
                '[data-e2e="nav-profile"]',
                # Navigation elements that appear when logged in
                '[data-testid="nav-profile"]',
                '.nav-profile',
                '[data-testid="header-profile"]',
                '.header-profile',
                # Dashboard or home elements
                '[data-testid="home-feed"]',
                '.home-feed',
                '[data-testid="for-you-feed"]',
                '.for-you-feed',
                # User menu or dropdown
                '.user-menu',
                '[data-testid="user-menu"]',
                '[data-e2e="user-menu"]',
                # TikTok specific logged-in elements
                '[data-testid="upload-button"]',
                '.upload-button',
                '[data-e2e="upload-button"]',
                # Profile or settings links
                'a[href*="/profile"]',
                'a[href*="/@"]',
                # Notification or message elements
                '[data-testid="notification"]',
                '[data-testid="inbox"]'
            ],
            'xpath_selectors': [
                # Text-based success indicators
                '//div[contains(text(), "For You")]',
                '//div[contains(text(), "Following")]',
                '//a[contains(text(), "Profile")]',
                '//button[contains(text(), "Upload")]',
                # Navigation links that appear when logged in
                '//nav//a[contains(@href, "profile")]',
                '//nav//a[contains(@href, "@")]',
                # User-specific elements
                '//div[@data-testid and contains(@data-testid, "user")]',
                '//div[@data-e2e and contains(@data-e2e, "profile")]'
            ],
            'url_patterns': [
                '/foryou',
                '/following',
                '/home',
                '/dashboard',
                '/@',  # User profile URLs
                '/upload'
            ]
        }
    
    def _verify_login_success(self, driver):
        """Enhanced login success verification with comprehensive indicators"""
        try:
            # Wait for potential redirect and page load
            time.sleep(random.uniform(3, 5))
            
            current_url = driver.current_url
            logger.info(f"Verifying login success on URL: {current_url}")
            
            success_indicators = self._get_success_indicators()
            
            # Check URL patterns first (most reliable)
            for pattern in success_indicators['url_patterns']:
                if pattern in current_url:
                    logger.info(f"Login success detected via URL pattern: {pattern}")
                    return {'success': True, 'method': 'url_pattern', 'indicator': pattern}
            
            # Check if we're no longer on login page
            if '/login' not in current_url and 'phone-or-email' not in current_url:
                logger.info("Login success detected - redirected away from login page")
                return {'success': True, 'method': 'url_redirect'}
            
            # Check for success elements using CSS selectors
            for selector in success_indicators['css_selectors']:
                try:
                    element = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if element.is_displayed():
                        logger.info(f"Login success detected via CSS selector: {selector}")
                        return {'success': True, 'method': 'css_element', 'indicator': selector}
                except (TimeoutException, Exception):
                    continue
            
            # Check for success elements using XPath selectors
            for selector in success_indicators['xpath_selectors']:
                try:
                    element = WebDriverWait(driver, 2).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    if element.is_displayed():
                        logger.info(f"Login success detected via XPath selector: {selector}")
                        return {'success': True, 'method': 'xpath_element', 'indicator': selector}
                except (TimeoutException, Exception):
                    continue
            
            # Check for error indicators
            error_indicators = [
                '.error-message',
                '[data-testid="error"]',
                '.login-error',
                '.alert-error',
                '.captcha',
                '[data-testid="captcha"]',
                '.verification-required',
                '[data-testid="verification"]'
            ]
            
            for selector in error_indicators:
                try:
                    error_element = driver.find_element(By.CSS_SELECTOR, selector)
                    if error_element.is_displayed():
                        error_text = error_element.text
                        logger.error(f"Login error detected: {error_text}")
                        return {
                            'success': False,
                            'error': f'Login failed: {error_text}',
                            'method': 'error_detection',
                            'indicator': selector
                        }
                except NoSuchElementException:
                    continue
            
            # Check if still on login page
            login_page_indicators = ['/login', '/signin', '/auth', 'phone-or-email']
            for indicator in login_page_indicators:
                if indicator in current_url:
                    logger.warning(f"Still on login page: {current_url}")
                    return {
                        'success': False,
                        'error': 'Still on login page after submission',
                        'method': 'url_check',
                        'indicator': indicator
                    }
            
            # Additional check: Look for login form elements (indicates failure)
            login_form_elements = [
                'input[type="password"]',
                'input[placeholder*="password"]',
                'button[type="submit"]'
            ]
            
            for selector in login_form_elements:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        logger.warning(f"Login form still present: {selector}")
                        return {
                            'success': False,
                            'error': 'Login form still visible after submission',
                            'method': 'form_check',
                            'indicator': selector
                        }
                except Exception:
                    continue
            
            # If no clear indicators, check page title and content
            try:
                page_title = driver.title.lower()
                if any(word in page_title for word in ['login', 'sign in', 'auth']):
                    logger.warning(f"Login-related page title detected: {page_title}")
                    return {
                        'success': False,
                        'error': f'Login-related page title: {page_title}',
                        'method': 'title_check'
                    }
            except Exception:
                pass
            
            # Default to success if no clear failure indicators
            logger.info("No clear failure indicators found, assuming login success")
            return {'success': True, 'method': 'assumption'}
        
        except Exception as e:
            logger.error(f"Error verifying login success: {str(e)}")
            return {
                'success': False,
                'error': f'Verification failed: {str(e)}',
                'method': 'exception'
            }
    
    def _simulate_pre_login_behavior(self, driver):
        """Simulate human behavior before login"""
        try:
            # Visit TikTok homepage first
            driver.get('https://www.tiktok.com')
            wait_time = random.uniform(*HUMAN_BEHAVIOR_CONFIG['page_interactions']['pre_login_wait_range'])
            time.sleep(wait_time)
            
            # Simulate browsing behavior
            self.anti_detection.simulate_human_behavior(driver)
            
            # Random scrolling if enabled
            if HUMAN_BEHAVIOR_CONFIG['scrolling']['enabled']:
                scroll_count = random.randint(*HUMAN_BEHAVIOR_CONFIG['scrolling']['scroll_count_range'])
                for _ in range(scroll_count):
                    driver.execute_script(f"window.scrollBy(0, {random.randint(200, 800)});")
                    scroll_delay = random.uniform(*HUMAN_BEHAVIOR_CONFIG['scrolling']['scroll_delay_range'])
                    time.sleep(scroll_delay)
            
        except Exception as e:
            logger.warning(f"Pre-login behavior simulation failed: {str(e)}")
    
    def _navigate_to_login_with_referrer(self, driver):
        """Navigate to login page with proper referrer"""
        try:
            # Set referrer header by navigating from homepage
            driver.execute_script("window.location.href = '/login';")
            time.sleep(random.uniform(2, 4))
        except Exception as e:
            logger.warning(f"Referrer navigation failed, using direct navigation: {str(e)}")
            driver.get('https://www.tiktok.com/login')
    
    def _wait_with_human_behavior(self, driver, duration):
        """Wait with human-like behavior"""
        end_time = time.time() + duration
        while time.time() < end_time:
            # Random micro-movements
            if random.random() < 0.3:
                driver.execute_script(
                    f"document.dispatchEvent(new MouseEvent('mousemove', {{"
                    f"clientX: {random.randint(100, 800)}, "
                    f"clientY: {random.randint(100, 600)}"
                    f"}}))"
                )
                time.sleep(random.uniform(0.1, 0.5))
    
    def _check_javascript_errors(self, driver):
        """Check for JavaScript errors in the browser console"""
        try:
            # Get browser logs
            logs = driver.get_log('browser')
            js_errors = []
            
            for log in logs:
                if log['level'] in ['SEVERE', 'ERROR']:
                    message = log.get('message', '')
                    # Filter for relevant JavaScript errors
                    if any(keyword in message.lower() for keyword in [
                        'invalidcharactererror', 'atob', 'btoa', 'syntaxerror',
                        'referenceerror', 'typeerror', 'uncaught', 'failed to execute'
                    ]):
                        js_errors.append({
                            'level': log['level'],
                            'message': message,
                            'timestamp': log['timestamp']
                        })
            
            return js_errors
        except Exception as e:
            logger.debug(f"Could not retrieve browser logs: {str(e)}")
            return []
    
    def _is_critical_js_error(self, js_errors):
        """Determine if JavaScript errors are critical enough to warrant a page refresh"""
        if not js_errors:
            return False
        
        critical_patterns = [
            'invalidcharactererror',  # The specific error from user's report
            'failed to execute \'atob\'',
            'syntaxerror',
            'referenceerror',
            'uncaught typeerror'
        ]
        
        for error in js_errors:
            message = error.get('message', '').lower()
            if any(pattern in message for pattern in critical_patterns):
                return True
        
        return False
    
    def _handle_atob_error(self, driver):
        """Handle specific atob() JavaScript errors by injecting a polyfill"""
        try:
            # Inject atob/btoa polyfill to handle base64 decoding errors
            polyfill_script = """
            // Polyfill for atob/btoa functions to handle encoding issues
            (function() {
                if (typeof window.atob !== 'function') {
                    window.atob = function(str) {
                        try {
                            return decodeURIComponent(escape(window.atob(str)));
                        } catch (e) {
                            console.warn('atob polyfill: Invalid base64 string', str);
                            return '';
                        }
                    };
                }
                
                // Override existing atob to handle errors gracefully
                const originalAtob = window.atob;
                window.atob = function(str) {
                    try {
                        if (!str || typeof str !== 'string') {
                            return '';
                        }
                        // Clean the string of invalid characters
                        const cleanStr = str.replace(/[^A-Za-z0-9+/=]/g, '');
                        return originalAtob(cleanStr);
                    } catch (e) {
                        console.warn('atob error handled:', e.message);
                        return '';
                    }
                };
                
                // Similar handling for btoa
                const originalBtoa = window.btoa;
                window.btoa = function(str) {
                    try {
                        if (!str) {
                            return '';
                        }
                        return originalBtoa(str);
                    } catch (e) {
                        console.warn('btoa error handled:', e.message);
                        return '';
                    }
                };
            })();
            """
            
            driver.execute_script(polyfill_script)
            logger.info("Injected atob/btoa error handling polyfill")
            return True
        except Exception as e:
            logger.warning(f"Failed to inject atob polyfill: {str(e)}")
            return False
    
    def _login_strategy_direct(self, driver, username, password, use_2fa=False, two_factor_code=None, retry_count=0):
        """
        Direct login strategy using the standard TikTok login flow
        """
        try:
            # Apply enhanced anti-detection measures
            self.anti_detection.randomize_viewport(driver)
            self.anti_detection.simulate_human_behavior(driver)
            
            # Pre-login behavior simulation
            self._simulate_pre_login_behavior(driver)
            
            # Navigate to login page with referrer simulation
            self._navigate_to_login_with_referrer(driver)
            self._wait_for_page_load(driver)
            
            # Handle cookie consent if present
            self._handle_cookie_consent(driver)
            
            # Handle app popup/modal if present
            self._handle_app_popup(driver)
            
            # Check for bot detection indicators
            bot_detection_result = self._check_bot_detection(driver)
            if not bot_detection_result['success']:
                return bot_detection_result
            
            # Find and click login method (email/username)
            login_method_result = self._select_login_method(driver)
            if not login_method_result:
                return {'success': False, 'error': 'Could not find login method selector'}
            
            # Enter credentials with enhanced human simulation
            credentials_result = self._enter_credentials_enhanced(driver, username, password)
            if not credentials_result['success']:
                return credentials_result
            
            # Handle captcha if present
            captcha_result = self._handle_captcha_enhanced(driver)
            if not captcha_result['success']:
                return captcha_result
            
            # Submit login form
            submit_result = self._submit_login_form(driver)
            if not submit_result['success']:
                return submit_result
            
            # Check for rate limiting or suspicious activity warnings
            rate_limit_result = self._handle_rate_limiting_warnings(driver)
            if not rate_limit_result['success']:
                return rate_limit_result
            
            # Handle 2FA if required
            if use_2fa or self._is_2fa_required(driver):
                if not two_factor_code:
                    return {
                        'success': False,
                        'error': '2FA code required but not provided',
                        'requires_2fa': True
                    }
                
                tfa_result = self._handle_2fa(driver, two_factor_code)
                if not tfa_result['success']:
                    return tfa_result
            
            # Verify successful login
            login_verification = self._verify_login_success(driver)
            if not login_verification['success']:
                return login_verification
            
            # Post-login behavior simulation
            self._simulate_post_login_behavior(driver)
            
            # Extract session data
            session_data = self._extract_session_data(driver)
            
            return {
                'success': True,
                'session_data': session_data,
                'session_metadata': {
                    'login_timestamp': time.time(),
                    'user_agent': driver.execute_script("return navigator.userAgent;"),
                    'viewport_size': driver.get_window_size(),
                    'retry_count': retry_count,
                    'strategy': 'direct'
                }
            }
            
        except Exception as e:
            logger.error(f"Direct login strategy failed: {str(e)}")
            return {
                'success': False,
                'error': f'Direct strategy failed: {str(e)}'
            }
    
    def _login_strategy_alternative_url(self, driver, username, password, use_2fa=False, two_factor_code=None, retry_count=0):
        """
        Alternative login strategy using different TikTok login URLs
        """
        try:
            # Try alternative login URLs
            alternative_urls = [
                "https://www.tiktok.com/login/phone-or-email/email",
                "https://www.tiktok.com/passport/web/login/",
                "https://www.tiktok.com/auth/login/"
            ]
            
            for url in alternative_urls:
                try:
                    logger.info(f"Trying alternative URL: {url}")
                    
                    # Navigate directly to alternative URL
                    driver.get(url)
                    self._wait_for_page_load(driver)
                    
                    # Check if we're on a login page
                    if self._is_on_login_page(driver):
                        # Apply anti-detection measures
                        self.anti_detection.simulate_human_behavior(driver)
                        
                        # Handle cookie consent
                        self._handle_cookie_consent(driver)
                        
                        # Handle app popup/modal if present
                        self._handle_app_popup(driver)
                        
                        # Check for bot detection
                        bot_detection_result = self._check_bot_detection(driver)
                        if not bot_detection_result['success']:
                            continue
                        
                        # Try to enter credentials directly (skip method selection)
                        credentials_result = self._enter_credentials_enhanced(driver, username, password)
                        if not credentials_result['success']:
                            continue
                        
                        # Handle captcha
                        captcha_result = self._handle_captcha_enhanced(driver)
                        if not captcha_result['success']:
                            continue
                        
                        # Submit form
                        submit_result = self._submit_login_form(driver)
                        if not submit_result['success']:
                            continue
                        
                        # Handle 2FA if needed
                        if use_2fa or self._is_2fa_required(driver):
                            if not two_factor_code:
                                return {
                                    'success': False,
                                    'error': '2FA code required but not provided',
                                    'requires_2fa': True
                                }
                            
                            tfa_result = self._handle_2fa(driver, two_factor_code)
                            if not tfa_result['success']:
                                continue
                        
                        # Verify login success
                        login_verification = self._verify_login_success(driver)
                        if login_verification['success']:
                            # Extract session data
                            session_data = self._extract_session_data(driver)
                            
                            return {
                                'success': True,
                                'session_data': session_data,
                                'session_metadata': {
                                    'login_timestamp': time.time(),
                                    'user_agent': driver.execute_script("return navigator.userAgent;"),
                                    'viewport_size': driver.get_window_size(),
                                    'retry_count': retry_count,
                                    'strategy': 'alternative_url',
                                    'successful_url': url
                                }
                            }
                        
                except Exception as e:
                    logger.warning(f"Alternative URL {url} failed: {str(e)}")
                    continue
            
            return {
                'success': False,
                'error': 'All alternative URLs failed'
            }
            
        except Exception as e:
            logger.error(f"Alternative URL strategy failed: {str(e)}")
            return {
                'success': False,
                'error': f'Alternative URL strategy failed: {str(e)}'
            }
    
    def _login_strategy_mobile_view(self, driver, username, password, use_2fa=False, two_factor_code=None, retry_count=0):
        """
        Mobile view login strategy to bypass desktop-specific bot detection
        """
        try:
            # Set mobile user agent
            original_user_agent = driver.execute_script("return navigator.userAgent;")
            mobile_user_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
            
            # Set mobile viewport
            driver.set_window_size(375, 812)  # iPhone X dimensions
            
            # Navigate to mobile TikTok
            mobile_url = "https://m.tiktok.com/login"
            driver.get(mobile_url)
            self._wait_for_page_load(driver)
            
            # Apply anti-detection measures
            self.anti_detection.simulate_human_behavior(driver)
            
            # Handle cookie consent
            self._handle_cookie_consent(driver)
            
            # Handle app popup/modal if present
            self._handle_app_popup(driver)
            
            # Check for bot detection
            bot_detection_result = self._check_bot_detection(driver)
            if not bot_detection_result['success']:
                return bot_detection_result
            
            # Mobile-specific login method selection
            mobile_login_result = self._select_mobile_login_method(driver)
            if not mobile_login_result:
                return {'success': False, 'error': 'Could not find mobile login method'}
            
            # Enter credentials
            credentials_result = self._enter_credentials_enhanced(driver, username, password)
            if not credentials_result['success']:
                return credentials_result
            
            # Handle captcha
            captcha_result = self._handle_captcha_enhanced(driver)
            if not captcha_result['success']:
                return captcha_result
            
            # Submit form
            submit_result = self._submit_login_form(driver)
            if not submit_result['success']:
                return submit_result
            
            # Handle 2FA if needed
            if use_2fa or self._is_2fa_required(driver):
                if not two_factor_code:
                    return {
                        'success': False,
                        'error': '2FA code required but not provided',
                        'requires_2fa': True
                    }
                
                tfa_result = self._handle_2fa(driver, two_factor_code)
                if not tfa_result['success']:
                    return tfa_result
            
            # Verify login success
            login_verification = self._verify_login_success(driver)
            if not login_verification['success']:
                return login_verification
            
            # Extract session data
            session_data = self._extract_session_data(driver)
            
            return {
                'success': True,
                'session_data': session_data,
                'session_metadata': {
                    'login_timestamp': time.time(),
                    'user_agent': mobile_user_agent,
                    'viewport_size': driver.get_window_size(),
                    'retry_count': retry_count,
                    'strategy': 'mobile_view'
                }
            }
            
        except Exception as e:
            logger.error(f"Mobile view strategy failed: {str(e)}")
            return {
                'success': False,
                'error': f'Mobile view strategy failed: {str(e)}'
            }
    
    def _is_on_login_page(self, driver):
        """Check if we're currently on a login page"""
        try:
            # Check URL
            current_url = driver.current_url.lower()
            if 'login' in current_url or 'auth' in current_url:
                return True
            
            # Check for login form elements
            login_indicators = [
                'input[type="password"]',
                'input[placeholder*="password"]',
                'input[name="username"]',
                'input[type="email"]'
            ]
            
            for selector in login_indicators:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.warning(f"Error checking if on login page: {str(e)}")
            return False
    
    def _select_mobile_login_method(self, driver):
        """Select login method for mobile view"""
        try:
            # Mobile-specific selectors
            mobile_selectors = [
                'a[href*="email"]',
                'button[data-testid*="email"]',
                '.login-method-email',
                '//a[contains(text(), "Email")]',
                '//button[contains(text(), "Email")]'
            ]
            
            for selector in mobile_selectors:
                try:
                    if selector.startswith('//'):
                        element = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        element = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    
                    if element.is_displayed():
                        self.anti_detection.human_like_click(driver, element)
                        logger.info(f"Selected mobile login method with: {selector}")
                        time.sleep(random.uniform(2, 4))
                        return True
                        
                except (TimeoutException, Exception):
                    continue
            
            # If no specific mobile method found, check if form is already available
            form_selectors = [
                'input[type="password"]',
                'input[name="username"]'
            ]
            
            for selector in form_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        logger.info("Mobile login form already available")
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error selecting mobile login method: {str(e)}")
            return False
    
    def _handle_rate_limiting_warnings(self, driver):
        """
        Handle rate limiting or suspicious activity warnings
        """
        try:
            # Check for rate limiting indicators
            rate_limit_indicators = [
                'too many attempts',
                'suspicious activity',
                'please try again later',
                'rate limit',
                'temporarily blocked',
                'unusual activity'
            ]
            
            page_source = driver.page_source.lower()
            
            for indicator in rate_limit_indicators:
                if indicator in page_source:
                    logger.warning(f"Rate limiting detected: {indicator}")
                    return {
                        'success': False,
                        'error': f'Rate limiting detected: {indicator}',
                        'rate_limited': True
                    }
            
            # Check for specific rate limiting elements
            rate_limit_selectors = [
                '.rate-limit-message',
                '.error-message[data-testid*="rate"]',
                '.warning-message',
                '//div[contains(text(), "too many")]',
                '//div[contains(text(), "suspicious")]'
            ]
            
            for selector in rate_limit_selectors:
                try:
                    if selector.startswith('//'):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            message = element.text.lower()
                            if any(indicator in message for indicator in rate_limit_indicators):
                                logger.warning(f"Rate limiting element found: {message}")
                                return {
                                    'success': False,
                                    'error': f'Rate limiting detected: {message}',
                                    'rate_limited': True
                                }
                except:
                    continue
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error checking rate limiting: {str(e)}")
            return {'success': True}  # Continue if check fails
    
    def _check_bot_detection(self, driver):
        """Check for bot detection indicators"""
        try:
            # Check for common bot detection messages
            bot_indicators = BOT_DETECTION_INDICATORS['keywords']
            
            page_text = driver.page_source.lower()
            for indicator in bot_indicators:
                if indicator in page_text:
                    logger.warning(f"Bot detection indicator found: {indicator}")
                    return {
                        'success': False,
                        'error': f'Bot detection triggered: {indicator}',
                        'bot_detected': True
                    }
            
            # Check for specific bot detection elements
            bot_selectors = BOT_DETECTION_INDICATORS['css_selectors']
            
            for selector in bot_selectors:
                try:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        return {
                            'success': False,
                            'error': 'Bot detection challenge detected',
                            'bot_detected': True
                        }
                except NoSuchElementException:
                    continue
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error checking bot detection: {str(e)}")
            return {'success': True}  # Continue if check fails
    
    def _enter_credentials_enhanced(self, driver, username, password):
        """Enter credentials with enhanced human simulation"""
        try:
            # Find username field with multiple selectors
            username_selectors = [
                'input[name="username"]',
                'input[placeholder*="email"]',
                'input[placeholder*="phone"]',
                'input[placeholder*="username"]',
                'input[type="text"]',
                'input[type="email"]',
                'input[type="tel"]',
                '[data-testid="username"]',
                '[data-testid="email"]'
            ]
            
            username_field = None
            for selector in username_selectors:
                try:
                    username_field = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not username_field:
                return {'success': False, 'error': 'Could not find username field'}
            
            # Simulate human-like focus and typing
            self.anti_detection.human_like_click(driver, username_field)
            time.sleep(random.uniform(0.5, 1.5))
            
            # Clear field first
            username_field.clear()
            time.sleep(random.uniform(0.2, 0.5))
            
            # Type with realistic pauses and corrections
            self._type_with_realistic_behavior(username_field, username)
            
            # Tab to password field or click it
            time.sleep(random.uniform(0.5, 1.0))
            
            # Find password field
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                '[data-testid="password"]'
            ]
            
            password_field = None
            for selector in password_selectors:
                try:
                    password_field = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not password_field:
                return {'success': False, 'error': 'Could not find password field'}
            
            # Click password field
            self.anti_detection.human_like_click(driver, password_field)
            time.sleep(random.uniform(0.5, 1.5))
            
            # Clear and type password
            password_field.clear()
            time.sleep(random.uniform(0.2, 0.5))
            self._type_with_realistic_behavior(password_field, password)
            
            # Random pause before submission
            time.sleep(random.uniform(1, 3))
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error entering credentials: {str(e)}")
            return {'success': False, 'error': f'Failed to enter credentials: {str(e)}'}
    
    def _type_with_realistic_behavior(self, element, text):
        """Type text with realistic human behavior including mistakes"""
        try:
            # Simulate occasional typos and corrections
            if len(text) > 5 and random.random() < 0.1:  # 10% chance of typo
                # Type part of the text
                partial_text = text[:random.randint(2, len(text)//2)]
                self.anti_detection.human_like_type(element, partial_text)
                
                # Add a wrong character
                wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                element.send_keys(wrong_char)
                typing_config = HUMAN_BEHAVIOR_CONFIG['typing_delays']
                time.sleep(random.uniform(typing_config['word_pause_min'], typing_config['word_pause_max']))
                
                # Backspace to correct
                element.send_keys(Keys.BACKSPACE)
                time.sleep(random.uniform(typing_config['min_char_delay'], typing_config['max_char_delay']))
                
                # Continue with rest of text
                remaining_text = text[len(partial_text):]
                self.anti_detection.human_like_type(element, remaining_text)
            else:
                # Normal typing
                self.anti_detection.human_like_type(element, text)
                
        except Exception as e:
            logger.warning(f"Realistic typing failed, using fallback: {str(e)}")
            element.send_keys(text)
    
    def _handle_captcha_enhanced(self, driver):
        """Enhanced CAPTCHA handling with better detection"""
        try:
            # Extended CAPTCHA selectors
            captcha_selectors = CAPTCHA_INDICATORS['css_selectors']
            
            captcha_found = False
            for selector in captcha_selectors:
                try:
                    captcha_element = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if captcha_element.is_displayed():
                        captcha_found = True
                        logger.info(f"CAPTCHA detected with selector: {selector}")
                        break
                except TimeoutException:
                    continue
            
            if captcha_found:
                logger.info("CAPTCHA challenge detected, waiting for manual resolution...")
                
                # Wait longer for manual CAPTCHA solving
                max_wait_time = CAPTCHA_INDICATORS['timeout_seconds']
                wait_interval = 5
                waited_time = 0
                
                while waited_time < max_wait_time:
                    time.sleep(wait_interval)
                    waited_time += wait_interval
                    
                    # Check if CAPTCHA is still present
                    captcha_still_present = False
                    for selector in captcha_selectors:
                        try:
                            element = driver.find_element(By.CSS_SELECTOR, selector)
                            if element.is_displayed():
                                captcha_still_present = True
                                break
                        except NoSuchElementException:
                            continue
                    
                    if not captcha_still_present:
                        logger.info("CAPTCHA resolved successfully")
                        return {'success': True}
                    
                    logger.info(f"Still waiting for CAPTCHA resolution... ({waited_time}s/{max_wait_time}s)")
                
                return {
                    'success': False,
                    'error': 'CAPTCHA not resolved within time limit',
                    'captcha_timeout': True
                }
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error handling captcha: {str(e)}")
            return {'success': False, 'error': f'Captcha handling failed: {str(e)}'}
    
    def _handle_rate_limiting_warnings(self, driver):
        """Handle rate limiting and suspicious activity warnings"""
        try:
            # Check for rate limiting messages
            rate_limit_indicators = RATE_LIMITING_INDICATORS['keywords']
            
            page_text = driver.page_source.lower()
            for indicator in rate_limit_indicators:
                if indicator in page_text:
                    logger.warning(f"Rate limiting detected: {indicator}")
                    return {
                        'success': False,
                        'error': f'Rate limiting detected: {indicator}',
                        'rate_limited': True
                    }
            
            # Check for specific warning elements
            warning_selectors = RATE_LIMITING_INDICATORS['css_selectors']
            
            for selector in warning_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            warning_text = element.text.lower()
                            for indicator in rate_limit_indicators:
                                if indicator in warning_text:
                                    return {
                                        'success': False,
                                        'error': f'Rate limiting warning: {warning_text}',
                                        'rate_limited': True
                                    }
                except NoSuchElementException:
                    continue
            
            return {'success': True}
            
        except Exception as e:
            logger.error(f"Error checking rate limiting: {str(e)}")
            return {'success': True}  # Continue if check fails
    
    def _simulate_post_login_behavior(self, driver):
        """Simulate human behavior after successful login"""
        try:
            # Wait and simulate reading the page
            wait_time = random.uniform(*HUMAN_BEHAVIOR_CONFIG['page_interactions']['post_login_wait_range'])
            time.sleep(wait_time)
            
            # Random scrolling if enabled
            if HUMAN_BEHAVIOR_CONFIG['scrolling']['enabled']:
                scroll_count = random.randint(*HUMAN_BEHAVIOR_CONFIG['scrolling']['scroll_count_range'])
                for _ in range(scroll_count):
                    driver.execute_script(f"window.scrollBy(0, {random.randint(100, 300)});")
                    scroll_delay = random.uniform(*HUMAN_BEHAVIOR_CONFIG['scrolling']['scroll_delay_range'])
                    time.sleep(scroll_delay)
            
            # Simulate mouse movement
            self.anti_detection.simulate_human_behavior(driver)
            
        except Exception as e:
            logger.warning(f"Post-login behavior simulation failed: {str(e)}")
    
    def _clear_browser_traces(self, driver):
        """Clear browser traces before closing"""
        try:
            # Clear some storage (but keep session data)
            driver.execute_script("""
                // Clear some traces but keep essential session data
                if (window.performance && window.performance.clearResourceTimings) {
                    window.performance.clearResourceTimings();
                }
                // Clear some debugging traces
                if (window.console && window.console.clear) {
                    window.console.clear();
                }
            """)
        except Exception as e:
            logger.warning(f"Failed to clear browser traces: {str(e)}")
    
    def _extract_session_data(self, driver):
        """Extract session data from the browser"""
        try:
            session_data = {
                'cookies': [],
                'local_storage': {},
                'session_storage': {},
                'current_url': driver.current_url,
                'user_agent': driver.execute_script("return navigator.userAgent;")
            }
            
            # Extract cookies
            try:
                cookies = driver.get_cookies()
                session_data['cookies'] = cookies
                logger.info(f"Extracted {len(cookies)} cookies")
            except Exception as e:
                logger.warning(f"Could not extract cookies: {str(e)}")
            
            # Extract local storage
            try:
                local_storage = driver.execute_script(
                    "return Object.keys(localStorage).reduce((obj, key) => { obj[key] = localStorage.getItem(key); return obj; }, {});"
                )
                session_data['local_storage'] = local_storage
                logger.info(f"Extracted {len(local_storage)} local storage items")
            except Exception as e:
                logger.warning(f"Could not extract local storage: {str(e)}")
            
            # Extract session storage
            try:
                session_storage = driver.execute_script(
                    "return Object.keys(sessionStorage).reduce((obj, key) => { obj[key] = sessionStorage.getItem(key); return obj; }, {});"
                )
                session_data['session_storage'] = session_storage
                logger.info(f"Extracted {len(session_storage)} session storage items")
            except Exception as e:
                logger.warning(f"Could not extract session storage: {str(e)}")
            
            return session_data
        
        except Exception as e:
            logger.error(f"Error extracting session data: {str(e)}")
            return {
                'cookies': [],
                'local_storage': {},
                'session_storage': {},
                'current_url': driver.current_url if driver else '',
                'user_agent': ''
            }