import logging
import time
import random
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from django.utils import timezone

from .enhanced_tiktok_auth import EnhancedTikTokAuthenticator
from .anti_detection import AntiDetectionManager
from .session_manager import EnhancedSessionManager
from .proxy_manager import ProxyManager

logger = logging.getLogger(__name__)

class EnhancedTikTokScraper:
    """
    Enhanced TikTok scraper with advanced anti-detection and data extraction
    """
    
    def __init__(self, proxy_config: Optional[Dict] = None, rate_limit_config: Optional[Dict] = None):
        """
        Initialize enhanced scraper
        
        Args:
            proxy_config: Proxy configuration
            rate_limit_config: Rate limiting configuration
        """
        self.base_url = "https://www.tiktok.com"
        self.profile_url = "https://www.tiktok.com/@{username}"
        self.video_url = "https://www.tiktok.com/@{username}/video/{video_id}"
        
        # Initialize components
        self.authenticator = EnhancedTikTokAuthenticator(proxy_config)
        self.anti_detection = AntiDetectionManager()
        self.session_manager = EnhancedSessionManager()
        
        # Rate limiting configuration
        self.rate_limits = rate_limit_config or {
            'requests_per_minute': 30,
            'requests_per_hour': 500,
            'delay_between_requests': (2, 5),
            'delay_between_profiles': (10, 20)
        }
        
        # Selectors for different content types
        self.selectors = {
            'video_container': '[data-e2e="user-post-item"]',
            'video_link': 'a[href*="/video/"]',
            'video_description': '[data-e2e="video-desc"]',
            'video_stats': '[data-e2e="video-views"]',
            'like_count': '[data-e2e="like-count"]',
            'comment_count': '[data-e2e="comment-count"]',
            'share_count': '[data-e2e="share-count"]',
            'profile_info': '[data-e2e="user-page"]',
            'follower_count': '[data-e2e="followers-count"]',
            'following_count': '[data-e2e="following-count"]',
            'likes_count': '[data-e2e="likes-count"]'
        }
        
        # Current session info
        self.current_driver = None
        self.current_account_id = None
        self.last_request_time = None
        self.request_count = {'minute': 0, 'hour': 0}
        self.last_reset_time = {'minute': time.time(), 'hour': time.time()}
    
    def login_and_prepare(self, username: str, password: str, account_id: int,
                         use_2fa: bool = False, two_factor_code: Optional[str] = None) -> Dict:
        """
        Login and prepare scraper for data extraction
        
        Args:
            username: TikTok username
            password: TikTok password
            account_id: Account ID for session management
            use_2fa: Whether to use 2FA
            two_factor_code: 2FA code if needed
        
        Returns:
            Login result dictionary
        """
        try:
            logger.info(f"Preparing scraper for account: {username}")
            
            # Attempt login
            login_result = self.authenticator.login(
                username=username,
                password=password,
                use_2fa=use_2fa,
                two_factor_code=two_factor_code,
                account_id=account_id
            )
            
            if not login_result['success']:
                return login_result
            
            # Setup driver with session
            self.current_account_id = account_id
            session_data = login_result.get('session_data', {})
            
            # Create driver and load session
            self.current_driver = self.anti_detection.setup_driver(use_undetected=True)
            self._load_session_data(self.current_driver, session_data)
            
            # Verify login status
            if not self._verify_login_status(self.current_driver):
                return {'success': False, 'error': 'Login verification failed'}
            
            logger.info("Scraper prepared successfully")
            return {'success': True, 'message': 'Scraper ready for data extraction'}
            
        except Exception as e:
            logger.error(f"Error preparing scraper: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def scrape_user_profile(self, username: str, include_videos: bool = True, 
                          video_limit: int = 50) -> Dict:
        """
        Scrape comprehensive user profile data
        
        Args:
            username: TikTok username to scrape
            include_videos: Whether to include video data
            video_limit: Maximum number of videos to scrape
        
        Returns:
            Profile data dictionary
        """
        try:
            if not self._check_rate_limit():
                return {'success': False, 'error': 'Rate limit exceeded'}
            
            logger.info(f"Scraping profile: {username}")
            
            # Navigate to profile
            profile_url = self.profile_url.format(username=username)
            self.current_driver.get(profile_url)
            self._apply_rate_limiting()
            
            # Handle popups and wait for content
            self._handle_page_popups(self.current_driver)
            self._wait_for_profile_load(self.current_driver)
            
            # Extract profile information
            profile_data = self._extract_profile_info(self.current_driver, username)
            
            # Extract videos if requested
            if include_videos:
                videos_data = self._extract_profile_videos(
                    self.current_driver, username, video_limit
                )
                profile_data['videos'] = videos_data
            
            # Update session after successful scraping
            self._update_session_after_scraping()
            
            logger.info(f"Successfully scraped profile: {username}")
            return {
                'success': True,
                'data': profile_data,
                'scraped_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error scraping profile {username}: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def scrape_video_details(self, username: str, video_id: str) -> Dict:
        """
        Scrape detailed information about a specific video
        
        Args:
            username: Video owner's username
            video_id: TikTok video ID
        
        Returns:
            Video data dictionary
        """
        try:
            if not self._check_rate_limit():
                return {'success': False, 'error': 'Rate limit exceeded'}
            
            logger.info(f"Scraping video: {video_id}")
            
            # Navigate to video
            video_url = self.video_url.format(username=username, video_id=video_id)
            self.current_driver.get(video_url)
            self._apply_rate_limiting()
            
            # Wait for video to load
            self._wait_for_video_load(self.current_driver)
            
            # Extract video data
            video_data = self._extract_video_details(self.current_driver, video_id)
            
            # Extract comments if available
            comments_data = self._extract_video_comments(self.current_driver, limit=100)
            video_data['comments'] = comments_data
            
            logger.info(f"Successfully scraped video: {video_id}")
            return {
                'success': True,
                'data': video_data,
                'scraped_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error scraping video {video_id}: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def scrape_trending_videos(self, limit: int = 100, category: Optional[str] = None) -> Dict:
        """
        Scrape trending videos from TikTok
        
        Args:
            limit: Maximum number of videos to scrape
            category: Optional category filter
        
        Returns:
            Trending videos data
        """
        try:
            if not self._check_rate_limit():
                return {'success': False, 'error': 'Rate limit exceeded'}
            
            logger.info(f"Scraping trending videos (limit: {limit})")
            
            # Navigate to trending/discover page
            trending_url = f"{self.base_url}/discover"
            if category:
                trending_url += f"?category={category}"
            
            self.current_driver.get(trending_url)
            self._apply_rate_limiting()
            
            # Wait for content to load
            self._wait_for_trending_load(self.current_driver)
            
            # Extract trending videos
            trending_data = self._extract_trending_videos(self.current_driver, limit)
            
            logger.info(f"Successfully scraped {len(trending_data)} trending videos")
            return {
                'success': True,
                'data': trending_data,
                'scraped_at': timezone.now().isoformat(),
                'category': category
            }
            
        except Exception as e:
            logger.error(f"Error scraping trending videos: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def search_content(self, query: str, content_type: str = 'videos', 
                      limit: int = 50) -> Dict:
        """
        Search for content on TikTok
        
        Args:
            query: Search query
            content_type: Type of content ('videos', 'users', 'sounds')
            limit: Maximum results to return
        
        Returns:
            Search results data
        """
        try:
            if not self._check_rate_limit():
                return {'success': False, 'error': 'Rate limit exceeded'}
            
            logger.info(f"Searching for: {query} (type: {content_type})")
            
            # Navigate to search page
            search_url = f"{self.base_url}/search?q={query}&t={content_type}"
            self.current_driver.get(search_url)
            self._apply_rate_limiting()
            
            # Wait for search results
            self._wait_for_search_results(self.current_driver)
            
            # Extract search results based on content type
            if content_type == 'videos':
                results = self._extract_search_videos(self.current_driver, limit)
            elif content_type == 'users':
                results = self._extract_search_users(self.current_driver, limit)
            elif content_type == 'sounds':
                results = self._extract_search_sounds(self.current_driver, limit)
            else:
                return {'success': False, 'error': f'Unsupported content type: {content_type}'}
            
            logger.info(f"Successfully found {len(results)} results for: {query}")
            return {
                'success': True,
                'data': results,
                'query': query,
                'content_type': content_type,
                'scraped_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error searching for {query}: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _check_rate_limit(self) -> bool:
        """
        Check if request is within rate limits
        
        Returns:
            True if request is allowed, False otherwise
        """
        current_time = time.time()
        
        # Reset counters if needed
        if current_time - self.last_reset_time['minute'] >= 60:
            self.request_count['minute'] = 0
            self.last_reset_time['minute'] = current_time
        
        if current_time - self.last_reset_time['hour'] >= 3600:
            self.request_count['hour'] = 0
            self.last_reset_time['hour'] = current_time
        
        # Check limits
        if self.request_count['minute'] >= self.rate_limits['requests_per_minute']:
            logger.warning("Per-minute rate limit exceeded")
            return False
        
        if self.request_count['hour'] >= self.rate_limits['requests_per_hour']:
            logger.warning("Per-hour rate limit exceeded")
            return False
        
        return True
    
    def _apply_rate_limiting(self):
        """Apply rate limiting delays"""
        # Increment counters
        self.request_count['minute'] += 1
        self.request_count['hour'] += 1
        
        # Apply delay
        delay_range = self.rate_limits['delay_between_requests']
        delay = random.uniform(delay_range[0], delay_range[1])
        
        logger.debug(f"Applying rate limit delay: {delay:.2f} seconds")
        time.sleep(delay)
        
        self.last_request_time = time.time()
    
    def _load_session_data(self, driver, session_data: Dict):
        """Load session data into driver"""
        try:
            if not session_data:
                return
            
            # Navigate to TikTok first
            driver.get(self.base_url)
            time.sleep(2)
            
            # Load cookies
            if 'cookies' in session_data:
                for cookie in session_data['cookies']:
                    try:
                        driver.add_cookie(cookie)
                    except Exception as e:
                        logger.warning(f"Failed to add cookie: {str(e)}")
            
            # Load local storage
            if 'local_storage' in session_data:
                for key, value in session_data['local_storage'].items():
                    try:
                        driver.execute_script(
                            f"localStorage.setItem('{key}', '{value}');"
                        )
                    except Exception as e:
                        logger.warning(f"Failed to set localStorage: {str(e)}")
            
            # Refresh page to apply session
            driver.refresh()
            time.sleep(3)
            
        except Exception as e:
            logger.error(f"Error loading session data: {str(e)}")
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.current_driver:
                self.current_driver.quit()
                self.current_driver = None
            
            logger.info("Scraper cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()

    def _handle_page_popups(self, driver):
        """Handle popups on TikTok pages"""
        try:
            # Similar to the authenticator popup handling
            popup_selectors = [
                '[data-testid="modal-close-inner-button"]',
                '.modal-close',
                'button[aria-label="Close"]',
                '.close-button'
            ]

            for selector in popup_selectors:
                try:
                    element = WebDriverWait(driver, 2).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    element.click()
                    time.sleep(1)
                    break
                except TimeoutException:
                    continue

        except Exception as e:
            logger.debug(f"No popups to handle: {str(e)}")

    def _wait_for_profile_load(self, driver):
        """Wait for profile page to load"""
        try:
            WebDriverWait(driver, 15).until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="user-page"]')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.user-info')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="user-info"]'))
                )
            )
        except TimeoutException:
            logger.warning("Profile page load timeout")

    def _wait_for_video_load(self, driver):
        """Wait for video page to load"""
        try:
            WebDriverWait(driver, 15).until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'video')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="video-player"]')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.video-container'))
                )
            )
        except TimeoutException:
            logger.warning("Video page load timeout")

    def _wait_for_trending_load(self, driver):
        """Wait for trending page to load"""
        try:
            WebDriverWait(driver, 15).until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="user-post-item"]')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.video-feed-item')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.discover-item'))
                )
            )
        except TimeoutException:
            logger.warning("Trending page load timeout")

    def _wait_for_search_results(self, driver):
        """Wait for search results to load"""
        try:
            WebDriverWait(driver, 15).until(
                EC.any_of(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="search-result"]')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.search-item')),
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[data-e2e="user-post-item"]'))
                )
            )
        except TimeoutException:
            logger.warning("Search results load timeout")

    def _extract_profile_info(self, driver, username):
        """Extract profile information"""
        try:
            profile_data = {
                'username': username,
                'display_name': '',
                'bio': '',
                'follower_count': 0,
                'following_count': 0,
                'likes_count': 0,
                'verified': False
            }

            # Try to extract basic profile info
            try:
                display_name_element = driver.find_element(By.CSS_SELECTOR, '[data-e2e="user-title"]')
                profile_data['display_name'] = display_name_element.text
            except NoSuchElementException:
                pass

            try:
                bio_element = driver.find_element(By.CSS_SELECTOR, '[data-e2e="user-bio"]')
                profile_data['bio'] = bio_element.text
            except NoSuchElementException:
                pass

            return profile_data

        except Exception as e:
            logger.error(f"Error extracting profile info: {str(e)}")
            return {'username': username, 'error': str(e)}

    def _extract_profile_videos(self, driver, username, limit):
        """Extract videos from profile"""
        try:
            videos = []
            video_elements = driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"]')

            for i, element in enumerate(video_elements[:limit]):
                try:
                    video_data = {
                        'index': i,
                        'username': username,
                        'title': 'Video title not available',
                        'views': 'Views not available'
                    }
                    videos.append(video_data)
                except Exception as e:
                    logger.debug(f"Error extracting video {i}: {str(e)}")
                    continue

            return videos

        except Exception as e:
            logger.error(f"Error extracting profile videos: {str(e)}")
            return []

    def _extract_video_details(self, driver, video_id):
        """Extract detailed video information"""
        try:
            video_data = {
                'video_id': video_id,
                'title': 'Title not available',
                'stats': {},
                'author': 'Author not available'
            }

            # Try to extract video details
            try:
                title_element = driver.find_element(By.CSS_SELECTOR, '[data-e2e="video-desc"]')
                video_data['title'] = title_element.text
            except NoSuchElementException:
                pass

            return video_data

        except Exception as e:
            logger.error(f"Error extracting video details: {str(e)}")
            return {'video_id': video_id, 'error': str(e)}

    def _extract_video_comments(self, driver, limit):
        """Extract video comments"""
        try:
            comments = []
            # This is a placeholder - actual comment extraction would be more complex
            logger.info(f"Comment extraction placeholder - would extract up to {limit} comments")
            return comments

        except Exception as e:
            logger.error(f"Error extracting comments: {str(e)}")
            return []

    def _extract_trending_videos(self, driver, limit):
        """Extract trending videos"""
        try:
            videos = []
            video_elements = driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"]')

            for i, element in enumerate(video_elements[:limit]):
                try:
                    video_data = {
                        'index': i,
                        'title': f'Trending video {i+1}',
                        'type': 'trending'
                    }
                    videos.append(video_data)
                except Exception as e:
                    logger.debug(f"Error extracting trending video {i}: {str(e)}")
                    continue

            return videos

        except Exception as e:
            logger.error(f"Error extracting trending videos: {str(e)}")
            return []

    def _extract_search_videos(self, driver, limit):
        """Extract search result videos"""
        try:
            videos = []
            video_elements = driver.find_elements(By.CSS_SELECTOR, '[data-e2e="user-post-item"]')

            for i, element in enumerate(video_elements[:limit]):
                try:
                    video_data = {
                        'index': i,
                        'title': f'Search result video {i+1}',
                        'type': 'search_result'
                    }
                    videos.append(video_data)
                except Exception as e:
                    logger.debug(f"Error extracting search video {i}: {str(e)}")
                    continue

            return videos

        except Exception as e:
            logger.error(f"Error extracting search videos: {str(e)}")
            return []

    def _extract_search_users(self, driver, limit):
        """Extract search result users"""
        try:
            users = []
            # Placeholder implementation
            logger.info(f"User search extraction placeholder - would extract up to {limit} users")
            return users

        except Exception as e:
            logger.error(f"Error extracting search users: {str(e)}")
            return []

    def _extract_search_sounds(self, driver, limit):
        """Extract search result sounds"""
        try:
            sounds = []
            # Placeholder implementation
            logger.info(f"Sound search extraction placeholder - would extract up to {limit} sounds")
            return sounds

        except Exception as e:
            logger.error(f"Error extracting search sounds: {str(e)}")
            return []

    def _verify_login_status(self, driver):
        """Verify if user is logged in"""
        try:
            # Check for login indicators
            login_indicators = [
                '[data-testid="user-avatar"]',
                '.avatar',
                '[href*="/profile"]',
                '.user-info'
            ]

            for indicator in login_indicators:
                try:
                    driver.find_element(By.CSS_SELECTOR, indicator)
                    return True
                except NoSuchElementException:
                    continue

            return False

        except Exception as e:
            logger.error(f"Error verifying login status: {str(e)}")
            return False

    def _update_session_after_scraping(self):
        """Update session data after successful scraping"""
        try:
            if self.current_account_id and self.current_driver:
                # Extract current session data
                session_data = {
                    'cookies': self.current_driver.get_cookies(),
                    'timestamp': timezone.now().isoformat(),
                    'last_activity': 'scraping'
                }

                # Update session in manager
                self.session_manager.save_session(self.current_account_id, session_data)
                logger.debug("Session updated after scraping")

        except Exception as e:
            logger.warning(f"Error updating session after scraping: {str(e)}")
