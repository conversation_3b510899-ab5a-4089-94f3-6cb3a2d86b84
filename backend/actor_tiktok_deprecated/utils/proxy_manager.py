import random
import time
import logging
import requests
from typing import List, Dict, <PERSON>tional, <PERSON>ple
from dataclasses import dataclass
from datetime import datetime, timedelta
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

@dataclass
class ProxyInfo:
    """Data class for proxy information"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = 'http'
    country: Optional[str] = None
    city: Optional[str] = None
    is_working: bool = True
    last_checked: Optional[datetime] = None
    response_time: float = 0.0
    failure_count: int = 0
    success_count: int = 0
    
    @property
    def proxy_url(self) -> str:
        """Get formatted proxy URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}"
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def proxy_dict(self) -> Dict[str, str]:
        """Get proxy dictionary for requests"""
        return {
            'http': self.proxy_url,
            'https': self.proxy_url
        }
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        total = self.success_count + self.failure_count
        return (self.success_count / total) if total > 0 else 0.0

class ProxyManager:
    """
    Advanced proxy management system with health monitoring and rotation
    """
    
    def __init__(self, proxies: List[Dict] = None, max_failures: int = 3, 
                 health_check_interval: int = 300, timeout: int = 10):
        """
        Initialize proxy manager
        
        Args:
            proxies: List of proxy configurations
            max_failures: Maximum failures before marking proxy as bad
            health_check_interval: Interval between health checks in seconds
            timeout: Timeout for proxy tests in seconds
        """
        self.proxies: List[ProxyInfo] = []
        self.max_failures = max_failures
        self.health_check_interval = health_check_interval
        self.timeout = timeout
        self.current_index = 0
        self.lock = threading.Lock()
        self.last_health_check = datetime.now()
        
        # Test URLs for proxy validation
        self.test_urls = [
            'https://httpbin.org/ip',
            'https://api.ipify.org?format=json',
            'https://ipinfo.io/json'
        ]
        
        if proxies:
            self.load_proxies(proxies)
    
    def load_proxies(self, proxy_configs: List[Dict]):
        """
        Load proxies from configuration
        
        Args:
            proxy_configs: List of proxy configuration dictionaries
        """
        self.proxies = []
        for config in proxy_configs:
            try:
                proxy = ProxyInfo(
                    host=config['host'],
                    port=config['port'],
                    username=config.get('username'),
                    password=config.get('password'),
                    protocol=config.get('protocol', 'http'),
                    country=config.get('country'),
                    city=config.get('city')
                )
                self.proxies.append(proxy)
            except KeyError as e:
                logger.error(f"Invalid proxy configuration: {config}, missing key: {e}")
        
        logger.info(f"Loaded {len(self.proxies)} proxies")
    
    def get_working_proxies(self) -> List[ProxyInfo]:
        """Get list of working proxies"""
        return [proxy for proxy in self.proxies if proxy.is_working]
    
    def get_next_proxy(self) -> Optional[ProxyInfo]:
        """
        Get next working proxy in rotation
        
        Returns:
            ProxyInfo object or None if no working proxies
        """
        with self.lock:
            working_proxies = self.get_working_proxies()
            
            if not working_proxies:
                logger.warning("No working proxies available")
                return None
            
            # Check if health check is needed
            if (datetime.now() - self.last_health_check).seconds > self.health_check_interval:
                self._schedule_health_check()
            
            # Get next proxy in rotation
            proxy = working_proxies[self.current_index % len(working_proxies)]
            self.current_index = (self.current_index + 1) % len(working_proxies)
            
            return proxy
    
    def get_random_proxy(self) -> Optional[ProxyInfo]:
        """
        Get random working proxy
        
        Returns:
            ProxyInfo object or None if no working proxies
        """
        working_proxies = self.get_working_proxies()
        
        if not working_proxies:
            logger.warning("No working proxies available")
            return None
        
        return random.choice(working_proxies)
    
    def get_best_proxy(self) -> Optional[ProxyInfo]:
        """
        Get proxy with best performance metrics
        
        Returns:
            ProxyInfo object or None if no working proxies
        """
        working_proxies = self.get_working_proxies()
        
        if not working_proxies:
            return None
        
        # Sort by success rate and response time
        best_proxy = max(working_proxies, 
                        key=lambda p: (p.success_rate, -p.response_time))
        
        return best_proxy
    
    def test_proxy(self, proxy: ProxyInfo) -> Tuple[bool, float]:
        """
        Test if proxy is working
        
        Args:
            proxy: ProxyInfo object to test
        
        Returns:
            Tuple of (is_working, response_time)
        """
        for test_url in self.test_urls:
            try:
                start_time = time.time()
                response = requests.get(
                    test_url,
                    proxies=proxy.proxy_dict,
                    timeout=self.timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    return True, response_time
                    
            except Exception as e:
                logger.debug(f"Proxy test failed for {proxy.host}:{proxy.port} - {str(e)}")
                continue
        
        return False, 0.0
    
    def mark_proxy_failure(self, proxy: ProxyInfo):
        """
        Mark proxy as failed and update statistics
        
        Args:
            proxy: ProxyInfo object that failed
        """
        with self.lock:
            proxy.failure_count += 1
            proxy.last_checked = datetime.now()
            
            if proxy.failure_count >= self.max_failures:
                proxy.is_working = False
                logger.warning(f"Proxy {proxy.host}:{proxy.port} marked as not working after {proxy.failure_count} failures")
    
    def mark_proxy_success(self, proxy: ProxyInfo, response_time: float = 0.0):
        """
        Mark proxy as successful and update statistics
        
        Args:
            proxy: ProxyInfo object that succeeded
            response_time: Response time for the request
        """
        with self.lock:
            proxy.success_count += 1
            proxy.response_time = response_time
            proxy.last_checked = datetime.now()
            proxy.is_working = True
    
    def health_check(self):
        """
        Perform health check on all proxies
        """
        logger.info("Starting proxy health check")
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_proxy = {
                executor.submit(self.test_proxy, proxy): proxy 
                for proxy in self.proxies
            }
            
            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    is_working, response_time = future.result()
                    
                    with self.lock:
                        proxy.is_working = is_working
                        proxy.last_checked = datetime.now()
                        
                        if is_working:
                            proxy.response_time = response_time
                            proxy.failure_count = 0  # Reset failure count on success
                        else:
                            proxy.failure_count += 1
                            
                except Exception as e:
                    logger.error(f"Health check failed for proxy {proxy.host}:{proxy.port}: {str(e)}")
                    self.mark_proxy_failure(proxy)
        
        working_count = len(self.get_working_proxies())
        logger.info(f"Health check complete. {working_count}/{len(self.proxies)} proxies working")
        
        self.last_health_check = datetime.now()
    
    def _schedule_health_check(self):
        """Schedule health check in background thread"""
        def run_health_check():
            self.health_check()
        
        thread = threading.Thread(target=run_health_check, daemon=True)
        thread.start()
    
    def get_proxy_stats(self) -> Dict:
        """
        Get proxy statistics
        
        Returns:
            Dictionary with proxy statistics
        """
        working_proxies = self.get_working_proxies()
        
        stats = {
            'total_proxies': len(self.proxies),
            'working_proxies': len(working_proxies),
            'average_response_time': 0.0,
            'average_success_rate': 0.0,
            'countries': set(),
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None
        }
        
        if working_proxies:
            stats['average_response_time'] = sum(p.response_time for p in working_proxies) / len(working_proxies)
            stats['average_success_rate'] = sum(p.success_rate for p in working_proxies) / len(working_proxies)
            stats['countries'] = {p.country for p in working_proxies if p.country}
        
        return stats
