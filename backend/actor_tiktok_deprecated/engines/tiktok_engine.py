"""
TikTok Engine for Actor Platform System

This module implements the TikTok-specific engine that handles authentication,
scraping, and data processing for TikTok platform.
"""

from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta
from django.utils import timezone

from .base_engine import BaseActorEngine, EngineRegistry
from ..utils.simple_tiktok_auth import SimpleTikTokAuthenticator
from ..utils.production_tiktok_scraper import ProductionTikTokScraper
from ..utils.actor_scraper import ActorTikTokScraper
from ..models import ActorAccount, TikTokUserAccount

logger = logging.getLogger(__name__)


class TikTokEngine(BaseActorEngine):
    """
    TikTok implementation of the Actor engine.
    
    This engine handles all TikTok-specific operations including authentication,
    content scraping, and data normalization.
    """
    
    def __init__(self, platform: str = 'tiktok'):
        super().__init__(platform)
        self.authenticator = SimpleTikTokAuthenticator()
        self.scraper = None
    
    def authenticate(self, account: ActorAccount, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """
        Authenticate with TikTok using provided credentials.
        
        Args:
            account: ActorAccount instance
            credentials: Dictionary containing username and password
            
        Returns:
            Dict containing authentication result
        """
        try:
            username = credentials.get('username') or account.platform_username
            password = credentials.get('password')
            
            if not password:
                password = account.decrypt_password()
            
            self.logger.info(f"Authenticating TikTok account: {username}")
            
            # Use the existing simple TikTok authenticator
            result = self.authenticator.simple_login(username, password)
            
            if result.get('success'):
                # Update account session data
                session_data = result.get('session_data', {})
                account.encrypt_session_data(session_data)
                account.last_login = timezone.now()
                account.session_expires_at = timezone.now() + timedelta(days=30)
                account.reset_login_attempts()
                account.save()
                
                self.logger.info(f"Successfully authenticated TikTok account: {username}")
                return {
                    'success': True,
                    'message': 'Successfully authenticated with TikTok',
                    'session_data': session_data,
                    'account_id': account.id
                }
            else:
                account.increment_login_attempts()
                error_msg = result.get('error', 'Authentication failed')
                self.logger.error(f"TikTok authentication failed for {username}: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg
                }
                
        except Exception as e:
            self.logger.error(f"TikTok authentication error: {str(e)}")
            return {
                'success': False,
                'error': f'Authentication error: {str(e)}'
            }
    
    def verify_session(self, account: ActorAccount) -> bool:
        """
        Verify if the TikTok session is still valid.
        
        Args:
            account: ActorAccount instance
            
        Returns:
            Boolean indicating if session is valid
        """
        try:
            if not account.is_session_valid():
                return False
            
            session_data = account.decrypt_session_data()
            if not session_data:
                return False
            
            # Additional TikTok-specific session validation can be added here
            # For now, rely on the account's built-in validation
            return True
            
        except Exception as e:
            self.logger.error(f"Session verification error: {str(e)}")
            return False
    
    def scrape_user_profile(self, account: ActorAccount, target_username: str, **kwargs) -> Dict[str, Any]:
        """
        Scrape TikTok user profile data.
        
        Args:
            account: ActorAccount instance
            target_username: TikTok username to scrape
            **kwargs: Additional parameters
            
        Returns:
            Dict containing scraped profile data
        """
        try:
            if not self.verify_session(account):
                raise Exception("Invalid session, please re-authenticate")
            
            # Initialize scraper if not already done
            if not self.scraper:
                self.scraper = ProductionTikTokScraper()
            
            # Get session data
            session_data = account.decrypt_session_data()
            
            # Scrape profile data
            profile_data = self.scraper.scrape_user_profile(
                target_username, 
                session_data=session_data,
                **kwargs
            )
            
            return self.normalize_data(profile_data, 'user')
            
        except Exception as e:
            self.logger.error(f"Profile scraping error: {str(e)}")
            return {'error': str(e), 'success': False}
    
    def scrape_user_content(self, account: ActorAccount, target_username: str, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape TikTok user's videos.
        
        Args:
            account: ActorAccount instance
            target_username: TikTok username to scrape
            limit: Maximum number of videos to scrape
            **kwargs: Additional parameters
            
        Returns:
            List of scraped video data
        """
        try:
            if not self.verify_session(account):
                raise Exception("Invalid session, please re-authenticate")
            
            if not self.scraper:
                self.scraper = ProductionTikTokScraper()
            
            session_data = account.decrypt_session_data()
            
            # Scrape user videos
            videos = self.scraper.scrape_user_videos(
                target_username,
                limit=limit,
                session_data=session_data,
                **kwargs
            )
            
            # Normalize each video
            normalized_videos = []
            for video in videos:
                normalized_video = self.normalize_data(video, 'video')
                normalized_videos.append(normalized_video)
            
            return normalized_videos
            
        except Exception as e:
            self.logger.error(f"User content scraping error: {str(e)}")
            return []
    
    def search_content(self, account: ActorAccount, keywords: List[str], limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for TikTok content using keywords.
        
        Args:
            account: ActorAccount instance
            keywords: List of keywords to search for
            limit: Maximum number of items to return
            **kwargs: Additional parameters (date_range, filters, etc.)
            
        Returns:
            List of search results
        """
        try:
            if not self.verify_session(account):
                raise Exception("Invalid session, please re-authenticate")
            
            if not self.scraper:
                self.scraper = ProductionTikTokScraper()
            
            session_data = account.decrypt_session_data()
            
            # Convert keywords list to search query
            search_query = ' '.join(keywords) if isinstance(keywords, list) else str(keywords)
            
            # Perform search
            search_results = self.scraper.search_content(
                search_query,
                limit=limit,
                session_data=session_data,
                **kwargs
            )
            
            # Normalize results
            normalized_results = []
            for result in search_results:
                normalized_result = self.normalize_data(result, 'video')
                normalized_results.append(normalized_result)
            
            return normalized_results
            
        except Exception as e:
            self.logger.error(f"Content search error: {str(e)}")
            return []
    
    def scrape_my_content(self, account: ActorAccount, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape authenticated user's own TikTok videos.
        
        Args:
            account: ActorAccount instance
            limit: Maximum number of videos to scrape
            **kwargs: Additional parameters
            
        Returns:
            List of user's own videos
        """
        try:
            return self.scrape_user_content(account, account.platform_username, limit, **kwargs)
        except Exception as e:
            self.logger.error(f"My content scraping error: {str(e)}")
            return []
    
    def scrape_feed(self, account: ActorAccount, limit: int = 50, **kwargs) -> List[Dict[str, Any]]:
        """
        Scrape user's TikTok feed.
        
        Args:
            account: ActorAccount instance
            limit: Maximum number of items to scrape
            **kwargs: Additional parameters
            
        Returns:
            List of feed items
        """
        try:
            if not self.verify_session(account):
                raise Exception("Invalid session, please re-authenticate")
            
            if not self.scraper:
                self.scraper = ProductionTikTokScraper()
            
            session_data = account.decrypt_session_data()
            
            # Scrape feed (this would need to be implemented in the scraper)
            # For now, return empty list as feed scraping is complex
            self.logger.warning("Feed scraping not yet implemented for TikTok")
            return []
            
        except Exception as e:
            self.logger.error(f"Feed scraping error: {str(e)}")
            return []
    
    def normalize_data(self, raw_data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """
        Normalize TikTok-specific data to common format.
        
        Args:
            raw_data: Raw TikTok data
            data_type: Type of data (video, user, etc.)
            
        Returns:
            Normalized data dictionary
        """
        normalized = super().normalize_data(raw_data, data_type)
        
        # Add TikTok-specific normalization
        if data_type == 'video':
            normalized.update({
                'title': raw_data.get('desc', ''),
                'author': raw_data.get('author', {}).get('uniqueId', ''),
                'author_display_name': raw_data.get('author', {}).get('nickname', ''),
                'likes': raw_data.get('stats', {}).get('diggCount', 0),
                'comments': raw_data.get('stats', {}).get('commentCount', 0),
                'shares': raw_data.get('stats', {}).get('shareCount', 0),
                'views': raw_data.get('stats', {}).get('playCount', 0),
                'video_url': raw_data.get('video', {}).get('playAddr', ''),
                'thumbnail_url': raw_data.get('video', {}).get('cover', ''),
                'duration': raw_data.get('video', {}).get('duration', 0),
                'created_at': raw_data.get('createTime', ''),
                'hashtags': [tag.get('title', '') for tag in raw_data.get('challenges', [])],
                'music': raw_data.get('music', {}).get('title', ''),
            })
        elif data_type == 'user':
            normalized.update({
                'username': raw_data.get('uniqueId', ''),
                'display_name': raw_data.get('nickname', ''),
                'bio': raw_data.get('signature', ''),
                'followers': raw_data.get('stats', {}).get('followerCount', 0),
                'following': raw_data.get('stats', {}).get('followingCount', 0),
                'likes': raw_data.get('stats', {}).get('heartCount', 0),
                'videos': raw_data.get('stats', {}).get('videoCount', 0),
                'avatar_url': raw_data.get('avatarMedium', ''),
                'verified': raw_data.get('verified', False),
            })
        
        return normalized
    
    def get_platform_specific_id(self, data: Dict[str, Any]) -> Optional[str]:
        """
        Extract TikTok-specific ID from data.
        
        Args:
            data: Data dictionary
            
        Returns:
            TikTok ID or None
        """
        return data.get('id') or data.get('aweme_id') or data.get('video_id')


# Register the TikTok engine
EngineRegistry.register('tiktok', TikTokEngine)
