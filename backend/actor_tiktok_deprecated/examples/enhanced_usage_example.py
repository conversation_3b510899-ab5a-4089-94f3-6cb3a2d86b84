#!/usr/bin/env python3
"""
Enhanced TikTok Actor Usage Examples

This script demonstrates how to use the enhanced TikTok login actor
with various configurations and use cases.
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Example configurations
PROXY_CONFIG = {
    'enabled': False,  # Set to True if you have proxies
    'proxies': [
        # Example proxy configuration
        # {
        #     'host': '127.0.0.1',
        #     'port': 8080,
        #     'username': 'proxy_user',
        #     'password': 'proxy_pass',
        #     'protocol': 'http',
        #     'country': 'US'
        # }
    ],
    'rotation_strategy': 'best_performance',
    'max_failures': 3,
    'health_check_interval': 300
}

RATE_LIMIT_CONFIG = {
    'requests_per_minute': 20,
    'requests_per_hour': 300,
    'delay_between_requests': (3, 7),
    'delay_between_profiles': (15, 25),
    'adaptive_delays': True
}

def example_enhanced_login():
    """
    Example: Enhanced login with advanced anti-detection
    """
    logger.info("=== Enhanced Login Example ===")
    
    try:
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        
        # Initialize authenticator
        authenticator = EnhancedTikTokAuthenticator(proxy_config=PROXY_CONFIG)
        
        # Perform login (replace with actual credentials)
        login_result = authenticator.login(
            username='your_tiktok_username',
            password='your_tiktok_password',
            use_2fa=False,  # Set to True if 2FA is enabled
            two_factor_code=None,  # Provide 2FA code if needed
            retry_count=0,
            account_id=None  # Provide account ID for session management
        )
        
        if login_result['success']:
            logger.info("✅ Login successful!")
            logger.info(f"Strategy used: {login_result.get('strategy', 'unknown')}")
            logger.info(f"Session data available: {'session_data' in login_result}")
        else:
            logger.error(f"❌ Login failed: {login_result.get('error')}")
            logger.error(f"Error type: {login_result.get('error_type', 'unknown')}")
            logger.error(f"Strategies attempted: {login_result.get('strategies_attempted', [])}")
        
        return login_result
        
    except Exception as e:
        logger.error(f"Login example failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def example_profile_scraping():
    """
    Example: Scrape user profile with enhanced scraper
    """
    logger.info("=== Profile Scraping Example ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        # Initialize scraper
        scraper = EnhancedTikTokScraper(
            proxy_config=PROXY_CONFIG,
            rate_limit_config=RATE_LIMIT_CONFIG
        )
        
        # Login and prepare scraper (replace with actual credentials)
        login_result = scraper.login_and_prepare(
            username='your_tiktok_username',
            password='your_tiktok_password',
            account_id=1  # Replace with actual account ID
        )
        
        if not login_result['success']:
            logger.error(f"❌ Scraper login failed: {login_result.get('error')}")
            return login_result
        
        logger.info("✅ Scraper prepared successfully")
        
        # Scrape a user profile (replace with actual username)
        target_username = 'target_tiktok_username'
        profile_result = scraper.scrape_user_profile(
            username=target_username,
            include_videos=True,
            video_limit=10  # Limit for example
        )
        
        if profile_result['success']:
            profile_data = profile_result['data']
            logger.info(f"✅ Profile scraped successfully: {target_username}")
            logger.info(f"Videos found: {len(profile_data.get('videos', []))}")
            logger.info(f"Profile info: {profile_data.get('profile_info', {})}")
        else:
            logger.error(f"❌ Profile scraping failed: {profile_result.get('error')}")
        
        # Cleanup
        scraper.cleanup()
        return profile_result
        
    except Exception as e:
        logger.error(f"Profile scraping example failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def example_video_scraping():
    """
    Example: Scrape specific video details
    """
    logger.info("=== Video Scraping Example ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        # Initialize scraper
        scraper = EnhancedTikTokScraper(
            proxy_config=PROXY_CONFIG,
            rate_limit_config=RATE_LIMIT_CONFIG
        )
        
        # Login and prepare scraper
        login_result = scraper.login_and_prepare(
            username='your_tiktok_username',
            password='your_tiktok_password',
            account_id=1
        )
        
        if not login_result['success']:
            logger.error(f"❌ Scraper login failed: {login_result.get('error')}")
            return login_result
        
        # Scrape specific video (replace with actual video details)
        video_result = scraper.scrape_video_details(
            username='video_owner_username',
            video_id='1234567890123456789'  # Replace with actual video ID
        )
        
        if video_result['success']:
            video_data = video_result['data']
            logger.info("✅ Video scraped successfully")
            logger.info(f"Video stats: {video_data.get('stats', {})}")
            logger.info(f"Comments found: {len(video_data.get('comments', []))}")
        else:
            logger.error(f"❌ Video scraping failed: {video_result.get('error')}")
        
        # Cleanup
        scraper.cleanup()
        return video_result
        
    except Exception as e:
        logger.error(f"Video scraping example failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def example_search_content():
    """
    Example: Search for content on TikTok
    """
    logger.info("=== Content Search Example ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        # Initialize scraper
        scraper = EnhancedTikTokScraper(
            proxy_config=PROXY_CONFIG,
            rate_limit_config=RATE_LIMIT_CONFIG
        )
        
        # Login and prepare scraper
        login_result = scraper.login_and_prepare(
            username='your_tiktok_username',
            password='your_tiktok_password',
            account_id=1
        )
        
        if not login_result['success']:
            logger.error(f"❌ Scraper login failed: {login_result.get('error')}")
            return login_result
        
        # Search for videos
        search_result = scraper.search_content(
            query='funny cats',  # Replace with your search query
            content_type='videos',
            limit=20
        )
        
        if search_result['success']:
            results = search_result['data']
            logger.info(f"✅ Search completed: found {len(results)} results")
            logger.info(f"Query: {search_result.get('query')}")
        else:
            logger.error(f"❌ Search failed: {search_result.get('error')}")
        
        # Cleanup
        scraper.cleanup()
        return search_result
        
    except Exception as e:
        logger.error(f"Search example failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def example_celery_tasks():
    """
    Example: Using Celery tasks for asynchronous processing
    """
    logger.info("=== Celery Tasks Example ===")
    
    try:
        from actor_tiktok.tasks import enhanced_actor_login_task, enhanced_scraping_task
        
        # Enhanced login task
        logger.info("Submitting enhanced login task...")
        login_task = enhanced_actor_login_task.delay(
            user_id=1,  # Replace with actual user ID
            tiktok_username='your_tiktok_username',
            tiktok_password='your_tiktok_password',
            use_2fa=False,
            proxy_config=PROXY_CONFIG
        )
        
        logger.info(f"Login task submitted: {login_task.id}")
        
        # Enhanced scraping task
        scraping_config = {
            'type': 'profile',
            'target': 'target_username',
            'options': {
                'include_videos': True,
                'video_limit': 25
            },
            'proxy_config': PROXY_CONFIG,
            'rate_limit_config': RATE_LIMIT_CONFIG
        }
        
        logger.info("Submitting enhanced scraping task...")
        scraping_task = enhanced_scraping_task.delay(
            user_id=1,  # Replace with actual user ID
            account_id=1,  # Replace with actual account ID
            scraping_config=scraping_config
        )
        
        logger.info(f"Scraping task submitted: {scraping_task.id}")
        
        return {
            'login_task_id': login_task.id,
            'scraping_task_id': scraping_task.id
        }
        
    except Exception as e:
        logger.error(f"Celery tasks example failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def example_proxy_management():
    """
    Example: Proxy management and health monitoring
    """
    logger.info("=== Proxy Management Example ===")
    
    try:
        from actor_tiktok.utils.proxy_manager import ProxyManager
        
        # Initialize proxy manager
        proxy_manager = ProxyManager(
            proxies=PROXY_CONFIG.get('proxies', []),
            max_failures=3,
            health_check_interval=300
        )
        
        if not proxy_manager.proxies:
            logger.info("No proxies configured - skipping proxy management example")
            return {'success': True, 'message': 'No proxies to manage'}
        
        # Perform health check
        logger.info("Performing proxy health check...")
        proxy_manager.health_check()
        
        # Get proxy statistics
        stats = proxy_manager.get_proxy_stats()
        logger.info(f"Proxy statistics: {stats}")
        
        # Get best proxy
        best_proxy = proxy_manager.get_best_proxy()
        if best_proxy:
            logger.info(f"Best proxy: {best_proxy.host}:{best_proxy.port} (success rate: {best_proxy.success_rate:.2f})")
        
        return {'success': True, 'stats': stats}
        
    except Exception as e:
        logger.error(f"Proxy management example failed: {str(e)}")
        return {'success': False, 'error': str(e)}

def main():
    """
    Main function to run all examples
    """
    logger.info("🚀 Starting Enhanced TikTok Actor Examples")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    
    examples = [
        ("Enhanced Login", example_enhanced_login),
        ("Profile Scraping", example_profile_scraping),
        ("Video Scraping", example_video_scraping),
        ("Content Search", example_search_content),
        ("Celery Tasks", example_celery_tasks),
        ("Proxy Management", example_proxy_management)
    ]
    
    results = {}
    
    for name, example_func in examples:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {name}")
        logger.info(f"{'='*50}")
        
        try:
            result = example_func()
            results[name] = result
            
            if result and result.get('success'):
                logger.info(f"✅ {name} completed successfully")
            else:
                logger.warning(f"⚠️ {name} completed with issues")
                
        except Exception as e:
            logger.error(f"❌ {name} failed with exception: {str(e)}")
            results[name] = {'success': False, 'error': str(e)}
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("SUMMARY")
    logger.info(f"{'='*50}")
    
    successful = sum(1 for r in results.values() if r and r.get('success'))
    total = len(results)
    
    logger.info(f"Examples completed: {successful}/{total}")
    
    for name, result in results.items():
        status = "✅" if result and result.get('success') else "❌"
        logger.info(f"{status} {name}")
    
    logger.info("🏁 Examples completed!")

if __name__ == "__main__":
    main()
