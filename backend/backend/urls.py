from django.urls import include, path
from rest_framework import routers
from django.contrib import admin
from django.conf import settings
from django.conf.urls.static import static
from blog import views
from users.views import UserViewSet

router = routers.DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'posts', views.PostViewSet)
router.register(r'comments', views.CommentViewSet)
router.register(r'categories', views.CategoryViewSet)

# URLs configuration
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
    # Djoser auth endpoints
    path('api/auth/', include('djoser.urls')),  # Basic auth endpoints
    path('api/auth/', include('djoser.urls.jwt')),  # JWT auth endpoints
    path('api/crawler/', include('crawler_tiktok.urls')),
    path('', include('actor_tiktok.urls')),
]

# Serve media files in development
from django.conf import settings
from django.conf.urls.static import static

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT) + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)