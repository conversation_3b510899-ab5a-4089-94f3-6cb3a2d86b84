#!/usr/bin/env python
"""
Test script to demonstrate how to access TikTok scraped data and metadata.
This shows that content text, likes, shares, comments, etc. are being captured.
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from crawler_tiktok.models import TikTokTask, ScrapedData
from crawler_tiktok.utils.data_processor import parse_tiktok_data

def demonstrate_data_access():
    print("=== TikTok Scraped Data Access Demonstration ===")
    print(f"Timestamp: {datetime.now()}\n")
    
    # 1. Show database statistics
    total_tasks = TikTokTask.objects.count()
    total_scraped = ScrapedData.objects.count()
    completed_tasks = TikTokTask.objects.filter(status='COMPLETED').count()
    
    print(f"📊 Database Statistics:")
    print(f"   Total Tasks: {total_tasks}")
    print(f"   Completed Tasks: {completed_tasks}")
    print(f"   Total Scraped Items: {total_scraped}")
    
    # 2. Show data by type
    for data_type in ['VIDEO', 'USER', 'SEARCH_RESULT']:
        count = ScrapedData.objects.filter(data_type=data_type).count()
        print(f"   {data_type}: {count} items")
    
    print("\n" + "="*60)
    
    # 3. Show recent completed tasks with their data
    recent_tasks = TikTokTask.objects.filter(status='COMPLETED').order_by('-completed_at')[:3]
    
    if not recent_tasks:
        print("❌ No completed tasks found.")
        print("\n💡 To see scraped data:")
        print("   1. Run a TikTok scraping task through the API")
        print("   2. Wait for it to complete")
        print("   3. Run this script again")
        return
    
    print(f"📋 Recent Completed Tasks ({len(recent_tasks)}):")
    
    for i, task in enumerate(recent_tasks, 1):
        print(f"\n{i}. Task #{task.id}: {task.job_name}")
        print(f"   Type: {task.task_type}")
        print(f"   Identifier: {task.identifier}")
        print(f"   Completed: {task.completed_at}")
        
        # Get scraped data for this task
        scraped_items = task.scraped_data.all()
        print(f"   Scraped Items: {scraped_items.count()}")
        
        # Show sample data with metadata
        for j, item in enumerate(scraped_items[:2], 1):  # Show first 2 items
            content = item.content
            print(f"\n   📄 Item {j} ({item.data_type}):")
            
            # Extract and display key metadata
            description = content.get('desc') or content.get('caption', 'N/A')
            author = content.get('author', 'N/A')
            stats = content.get('stats', {})
            
            print(f"      📝 Description: {description[:100]}{'...' if len(description) > 100 else ''}")
            print(f"      👤 Author: {author}")
            
            if stats:
                likes = stats.get('likes', 0) or 0
                comments = stats.get('comments', 0) or 0
                shares = stats.get('shares', 0) or 0
                print(f"      📊 Engagement:")
                print(f"         ❤️  Likes: {likes:,}")
                print(f"         💬 Comments: {comments:,}")
                if shares:
                    print(f"         🔄 Shares: {shares:,}")
            
            # Show additional metadata if available
            video_id = content.get('id')
            create_time = content.get('createTime')
            if video_id and video_id != 'N/A':
                print(f"      🆔 Video ID: {video_id}")
            if create_time and create_time != 'N/A':
                print(f"      📅 Created: {create_time}")
        
        if scraped_items.count() > 2:
            print(f"      ... and {scraped_items.count() - 2} more items")
    
    print("\n" + "="*60)
    
    # 4. Show API endpoint examples
    print("🔗 API Endpoints to Access This Data:")
    print("\n   📊 Summary of all data:")
    print("   GET /api/crawler-tiktok/scraped-data/summary/")
    
    print("\n   📋 All scraped data:")
    print("   GET /api/crawler-tiktok/scraped-data/")
    
    if recent_tasks:
        task_id = recent_tasks[0].id
        print(f"\n   🎯 Data for specific task (e.g., Task #{task_id}):")
        print(f"   GET /api/crawler-tiktok/scraped-data/by_task/?task_id={task_id}")
    
    print("\n   🔍 Filter by type:")
    print("   GET /api/crawler-tiktok/scraped-data/?data_type=SEARCH_RESULT")
    print("   GET /api/crawler-tiktok/scraped-data/?data_type=VIDEO")
    print("   GET /api/crawler-tiktok/scraped-data/?data_type=USER")
    
    print("\n" + "="*60)
    
    # 5. Show sample data structure
    if recent_tasks and recent_tasks[0].scraped_data.exists():
        sample_item = recent_tasks[0].scraped_data.first()
        print("📋 Sample Raw Data Structure:")
        print(json.dumps(sample_item.content, indent=2)[:500] + "...")
    
    print("\n✅ All scraped data includes rich metadata:")
    print("   • Content text/descriptions")
    print("   • Author information")
    print("   • Engagement metrics (likes, comments, shares)")
    print("   • Video IDs and timestamps")
    print("   • User profile data (for user scraping)")
    
    print("\n📖 For detailed documentation, see:")
    print("   crawler_tiktok/SCRAPED_DATA_GUIDE.md")

def demonstrate_data_extraction():
    """Show how the data extraction works with sample HTML."""
    print("\n" + "="*60)
    print("🧪 Data Extraction Test:")
    
    # Test with sample HTML to show what gets extracted
    sample_html = '''
    <div data-e2e="search-card-desc">Amazing TikTok video about technology trends in 2024</div>
    <div data-e2e="search-card-user-link">@techguru2024</div>
    <strong data-e2e="search-card-like-count">15.2K</strong>
    <strong data-e2e="search-card-comment-count">892</strong>
    '''
    
    result = parse_tiktok_data(sample_html, 'search')
    
    if result:
        print("\n✅ Extraction successful! Sample result:")
        print(json.dumps(result, indent=2))
    else:
        print("\n❌ No data extracted (this is expected with minimal HTML)")

if __name__ == "__main__":
    try:
        demonstrate_data_access()
        demonstrate_data_extraction()
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nMake sure you're running this from the backend directory:")
        print("cd /Users/<USER>/Documents/fullstax/backend && python test_scraped_data_access.py")