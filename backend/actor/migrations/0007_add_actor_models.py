# Generated by Django 5.2.4 on 2025-07-18 21:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor_tiktok', '0006_actortask_progress'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ActorSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('tiktok', 'TikTok'), ('instagram', 'Instagram'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('youtube', 'YouTube')], help_text='Platform for this session', max_length=20)),
                ('session_id', models.CharField(max_length=255, unique=True)),
                ('user_agent', models.TextField(help_text='User agent used for this session')),
                ('proxy_used', models.<PERSON>r<PERSON>ield(blank=True, help_text='Proxy used for this session', max_length=255, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='actorscrapeddata',
            name='account_username',
            field=models.CharField(blank=True, help_text='Username of the account used for scraping', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='actorscrapeddata',
            name='platform',
            field=models.CharField(choices=[('tiktok', 'TikTok'), ('instagram', 'Instagram'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('youtube', 'YouTube')], default='tiktok', help_text='Platform where data was scraped from', max_length=20),
        ),
        migrations.AddField(
            model_name='actorscrapeddata',
            name='platform_content_id',
            field=models.CharField(blank=True, help_text='Platform-specific ID of the content', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='actortask',
            name='platform',
            field=models.CharField(choices=[('tiktok', 'TikTok'), ('instagram', 'Instagram'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('youtube', 'YouTube')], default='tiktok', help_text='Platform for this task', max_length=20),
        ),
        migrations.AlterField(
            model_name='actorscrapeddata',
            name='data_type',
            field=models.CharField(choices=[('VIDEO', 'Video Data'), ('USER', 'User Data'), ('FOLLOWER', 'Follower Data'), ('FOLLOWING', 'Following Data'), ('LIKE', 'Liked Video Data'), ('FEED_ITEM', 'Feed Item Data'), ('HASHTAG_DATA', 'Hashtag Data'), ('ANALYTICS', 'Analytics Data'), ('POST', 'Post Data'), ('STORY', 'Story Data'), ('REEL', 'Reel Data'), ('TWEET', 'Tweet Data')], max_length=20),
        ),
        migrations.AlterField(
            model_name='actorscrapeddata',
            name='tiktok_id',
            field=models.CharField(blank=True, help_text='TikTok ID (for backward compatibility)', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='actortask',
            name='task_type',
            field=models.CharField(choices=[('MY_VIDEOS', 'My Videos'), ('MY_FOLLOWERS', 'My Followers'), ('MY_FOLLOWING', 'My Following'), ('MY_LIKES', 'My Liked Videos'), ('FEED_SCRAPE', 'Feed Scraping'), ('TARGETED_USER', 'Targeted User Analysis'), ('HASHTAG_ANALYSIS', 'Hashtag Analysis'), ('COMPETITOR_ANALYSIS', 'Competitor Analysis'), ('CONTENT_SEARCH', 'Content Search'), ('KEYWORD_SEARCH', 'Keyword Search'), ('PROFILE_ANALYSIS', 'Profile Analysis')], help_text='Type of actor task', max_length=20),
        ),
        migrations.AlterField(
            model_name='actortask',
            name='tiktok_account',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='actor_tiktok.tiktokuseraccount'),
        ),
        migrations.CreateModel(
            name='ActorAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('tiktok', 'TikTok'), ('instagram', 'Instagram'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('youtube', 'YouTube')], help_text='Social media platform', max_length=20)),
                ('platform_username', models.CharField(help_text='Username on the platform', max_length=255)),
                ('platform_user_id', models.CharField(blank=True, help_text='User ID on the platform', max_length=255, null=True)),
                ('email', models.EmailField(blank=True, help_text='Email for the account', max_length=254, null=True)),
                ('password', models.CharField(default='', help_text='Encrypted password for the account', max_length=255)),
                ('encrypted_session_data', models.TextField(help_text='Encrypted session data')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this account is active')),
                ('last_login', models.DateTimeField(blank=True, help_text='Last successful login', null=True)),
                ('session_expires_at', models.DateTimeField(blank=True, help_text='When the session expires', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('login_attempts', models.IntegerField(default=0, help_text='Number of login attempts')),
                ('last_attempt_at', models.DateTimeField(blank=True, help_text='Last login attempt', null=True)),
                ('is_blocked', models.BooleanField(default=False, help_text='Whether account is temporarily blocked')),
                ('blocked_until', models.DateTimeField(blank=True, help_text='When the block expires', null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='actor_accounts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Actor Account',
                'verbose_name_plural': 'Actor Accounts',
            },
        ),
        migrations.AddField(
            model_name='actorscrapeddata',
            name='actor_account',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scraped_data', to='actor_tiktok.actoraccount'),
        ),
        migrations.AddField(
            model_name='actortask',
            name='actor_account',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='actor_tiktok.actoraccount'),
        ),
        migrations.AddIndex(
            model_name='actorscrapeddata',
            index=models.Index(fields=['platform', 'platform_content_id'], name='actor_tikto_platfor_a7d9c9_idx'),
        ),
        migrations.AddIndex(
            model_name='actorscrapeddata',
            index=models.Index(fields=['actor_account', 'platform'], name='actor_tikto_actor_a_da2ead_idx'),
        ),
        migrations.AddField(
            model_name='actorsession',
            name='actor_account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='actor_tiktok.actoraccount'),
        ),
        migrations.AlterUniqueTogether(
            name='actoraccount',
            unique_together={('user', 'platform', 'platform_username')},
        ),
    ]
