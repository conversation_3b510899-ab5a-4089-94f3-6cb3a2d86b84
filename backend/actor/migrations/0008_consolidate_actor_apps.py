# Generated migration to consolidate actor_tiktok into actor app

from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('actor', '0007_add_actor_models'),
    ]

    operations = [
        # This migration consolidates the actor_tiktok app into the actor app
        # No actual database changes needed since the models are identical
        # This is just to mark the consolidation in migration history
        migrations.RunSQL(
            "-- Consolidating actor_tiktok app into actor app",
            reverse_sql="-- Reverse consolidation not supported"
        ),
    ]
