"""
Generic Actor Service

This service provides a unified interface for working with different social media platforms
through the Actor system. It handles account management, authentication, and task execution
across all supported platforms.
"""

import logging
from typing import Dict, List, Any, Optional
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>

from ..models import Actor<PERSON><PERSON>unt, ActorTask, ActorScrapedData, TikTokUserAccount
from ..engines import EngineRegistry
from ..engines.base_engine import BaseActorEngine

logger = logging.getLogger(__name__)


class ActorService:
    """
    Main service class for the Actor system.
    
    This service provides a unified interface for:
    - Managing accounts across platforms
    - Authenticating with different platforms
    - Creating and executing scraping tasks
    - Processing and storing scraped data
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def _encrypt_password(self, password: str) -> str:
        """Encrypt password for storage."""
        try:
            from cryptography.fernet import Fernet
            from django.conf import settings
            import base64

            # Use Django secret key to generate encryption key
            key = base64.urlsafe_b64encode(settings.SECRET_KEY[:32].encode())
            fernet = Fernet(key)
            return fernet.encrypt(password.encode()).decode()
        except Exception as e:
            self.logger.error(f"Error encrypting password: {str(e)}")
            return password  # Fallback to plain text (not recommended for production)

    def _decrypt_password(self, encrypted_password: str) -> str:
        """Decrypt password for use."""
        try:
            from cryptography.fernet import Fernet
            from django.conf import settings
            import base64

            # Use Django secret key to generate encryption key
            key = base64.urlsafe_b64encode(settings.SECRET_KEY[:32].encode())
            fernet = Fernet(key)
            return fernet.decrypt(encrypted_password.encode()).decode()
        except Exception as e:
            self.logger.error(f"Error decrypting password: {str(e)}")
            return encrypted_password  # Fallback to encrypted text

    def get_engine(self, platform: str) -> Optional[BaseActorEngine]:
        """Get the appropriate engine for a platform."""
        return EngineRegistry.get_engine(platform)
    
    def get_available_platforms(self) -> List[str]:
        """Get list of available platforms."""
        return EngineRegistry.get_available_platforms()
    
    def create_account(self, user: User, platform: str, username: str = None, password: str = None, email: str = None) -> Dict[str, Any]:
        """
        Create a new actor account for a platform.

        Args:
            user: Django User instance
            platform: Platform name (tiktok, instagram, etc.)
            username: Platform username (optional if email provided)
            password: Platform password
            email: Platform email (optional if username provided)

        Returns:
            Dict with success status and account data
        """
        try:
            # Validate input - need either username or email
            if not username and not email:
                return {
                    'success': False,
                    'error': 'Either username or email must be provided'
                }

            if not password:
                return {
                    'success': False,
                    'error': 'Password is required'
                }

            # Use email as username if username not provided
            platform_username = username or email

            # Check if account already exists
            existing_account = ActorAccount.objects.filter(
                user=user,
                platform=platform,
                platform_username=platform_username
            ).first()

            if existing_account:
                return {
                    'success': False,
                    'error': f'Account {platform_username} on {platform} already exists'
                }

            # Encrypt password
            encrypted_password = self._encrypt_password(password)

            # Create new account
            account = ActorAccount.objects.create(
                user=user,
                platform=platform,
                platform_username=platform_username,
                email=email,
                password=encrypted_password,
                is_active=True
            )
            
            # Encrypt and store password
            account.encrypt_password(password)
            account.save()
            
            self.logger.info(f"Created new {platform} account for user {user.id}: @{username}")
            
            return {
                'success': True,
                'message': f'Successfully created {platform} account',
                'account_id': account.id,
                'account': {
                    'id': account.id,
                    'platform': account.platform,
                    'username': account.platform_username,
                    'email': account.email,
                    'is_active': account.is_active,
                    'created_at': account.created_at.isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error creating account: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to create account: {str(e)}'
            }
    
    def authenticate_account(self, account_id: int, credentials: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Authenticate an actor account with its platform.
        
        Args:
            account_id: ActorAccount ID
            credentials: Optional credentials dict (if not provided, uses stored credentials)
            
        Returns:
            Dict with authentication result
        """
        try:
            account = ActorAccount.objects.get(id=account_id)
            engine = self.get_engine(account.platform)
            
            if not engine:
                return {
                    'success': False,
                    'error': f'No engine available for platform: {account.platform}'
                }
            
            # Use provided credentials or stored ones
            if not credentials:
                credentials = {
                    'username': account.platform_username,
                    'password': account.decrypt_password()
                }
            
            # Authenticate with the platform
            result = engine.authenticate(account, credentials)
            
            if result.get('success'):
                self.logger.info(f"Successfully authenticated {account.platform} account: @{account.platform_username}")
            else:
                self.logger.error(f"Authentication failed for {account.platform} account: @{account.platform_username}")
            
            return result
            
        except ActorAccount.DoesNotExist:
            return {
                'success': False,
                'error': 'Account not found'
            }
        except Exception as e:
            self.logger.error(f"Authentication error: {str(e)}")
            return {
                'success': False,
                'error': f'Authentication failed: {str(e)}'
            }
    
    def get_user_accounts(self, user: User, platform: str = None) -> List[Dict[str, Any]]:
        """
        Get all accounts for a user, optionally filtered by platform.
        
        Args:
            user: Django User instance
            platform: Optional platform filter
            
        Returns:
            List of account dictionaries
        """
        try:
            accounts_query = ActorAccount.objects.filter(user=user, is_active=True)
            
            if platform:
                accounts_query = accounts_query.filter(platform=platform)
            
            accounts = []
            for account in accounts_query:
                accounts.append({
                    'id': account.id,
                    'platform': account.platform,
                    'username': account.platform_username,
                    'email': account.email,
                    'is_active': account.is_active,
                    'last_login': account.last_login.isoformat() if account.last_login else None,
                    'session_valid': account.is_session_valid(),
                    'created_at': account.created_at.isoformat()
                })
            
            return accounts
            
        except Exception as e:
            self.logger.error(f"Error getting user accounts: {str(e)}")
            return []

    def update_account(self, user: User, account_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an actor account."""
        try:
            account = ActorAccount.objects.get(id=account_id, user=user)

            # Update allowed fields
            updatable_fields = ['platform_username', 'email', 'is_active']
            for field in updatable_fields:
                if field in update_data:
                    # Handle username field mapping
                    if field == 'platform_username' and 'username' in update_data:
                        setattr(account, 'platform_username', update_data['username'])
                    else:
                        setattr(account, field, update_data[field])

            # Handle password update separately for security
            if 'password' in update_data:
                # TODO: Implement proper password encryption
                account.encrypted_password = update_data['password']  # Placeholder

            account.save()

            return {
                'success': True,
                'message': 'Account updated successfully',
                'account': self._serialize_account(account)
            }

        except ActorAccount.DoesNotExist:
            return {
                'success': False,
                'error': 'Account not found'
            }
        except Exception as e:
            self.logger.error(f"Error updating account: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_account(self, user: User, account_id: int) -> Dict[str, Any]:
        """Delete an actor account."""
        try:
            account = ActorAccount.objects.get(id=account_id, user=user)

            # Check if account has active tasks
            active_tasks = ActorTask.objects.filter(
                account=account,
                status__in=['PENDING', 'RUNNING']
            ).count()

            if active_tasks > 0:
                return {
                    'success': False,
                    'error': f'Cannot delete account with {active_tasks} active tasks'
                }

            account_info = f"{account.platform}/@{account.platform_username}"
            account.delete()

            return {
                'success': True,
                'message': f'Account {account_info} deleted successfully'
            }

        except ActorAccount.DoesNotExist:
            return {
                'success': False,
                'error': 'Account not found'
            }
        except Exception as e:
            self.logger.error(f"Error deleting account: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_task(self, user: User, account_id: int, task_type: str, task_name: str, **kwargs) -> Dict[str, Any]:
        """
        Create a new scraping task.
        
        Args:
            user: Django User instance
            account_id: ActorAccount ID
            task_type: Type of task to create
            task_name: Name for the task
            **kwargs: Additional task parameters
            
        Returns:
            Dict with task creation result
        """
        try:
            account = ActorAccount.objects.get(id=account_id, user=user)
            
            # Create the task
            task = ActorTask.objects.create(
                user=user,
                actor_account=account,
                platform=account.platform,
                task_name=task_name,
                task_type=task_type,
                target_identifier=kwargs.get('target_identifier'),
                keywords=kwargs.get('keywords'),
                max_items=kwargs.get('max_items', 100),
                start_date=kwargs.get('start_date'),
                end_date=kwargs.get('end_date'),
                task_parameters=kwargs.get('task_parameters', {})
            )
            
            self.logger.info(f"Created new {task_type} task for {account.platform} account @{account.platform_username}")
            
            return {
                'success': True,
                'message': 'Task created successfully',
                'task_id': task.id,
                'task': {
                    'id': task.id,
                    'name': task.task_name,
                    'type': task.task_type,
                    'platform': task.platform,
                    'status': task.status,
                    'created_at': task.created_at.isoformat()
                }
            }
            
        except ActorAccount.DoesNotExist:
            return {
                'success': False,
                'error': 'Account not found or access denied'
            }
        except Exception as e:
            self.logger.error(f"Error creating task: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to create task: {str(e)}'
            }
    
    def execute_task(self, task_id: int) -> Dict[str, Any]:
        """
        Execute a scraping task.
        
        Args:
            task_id: ActorTask ID
            
        Returns:
            Dict with execution result
        """
        try:
            task = ActorTask.objects.get(id=task_id)
            account = task.actor_account
            engine = self.get_engine(account.platform)
            
            if not engine:
                return {
                    'success': False,
                    'error': f'No engine available for platform: {account.platform}'
                }
            
            # Verify session is valid
            if not engine.verify_session(account):
                return {
                    'success': False,
                    'error': 'Session invalid, please re-authenticate'
                }
            
            # Update task status
            task.status = 'RUNNING'
            task.started_at = timezone.now()
            task.save()
            
            # Execute based on task type
            results = []
            
            if task.task_type == 'CONTENT_SEARCH':
                keywords = task.keywords.split(',') if task.keywords else []
                results = engine.search_content(
                    account, 
                    keywords, 
                    limit=task.max_items,
                    start_date=task.start_date,
                    end_date=task.end_date
                )
            elif task.task_type == 'MY_VIDEOS':
                results = engine.scrape_my_content(account, limit=task.max_items)
            elif task.task_type == 'TARGETED_USER':
                results = engine.scrape_user_content(
                    account, 
                    task.target_identifier, 
                    limit=task.max_items
                )
            elif task.task_type == 'FEED_SCRAPE':
                results = engine.scrape_feed(account, limit=task.max_items)
            
            # Save scraped data with proper labeling
            saved_count = 0
            for result in results:
                if result and not result.get('error'):
                    # Determine data type from result
                    data_type = self._determine_data_type(result, task.task_type)

                    # Create scraped data with comprehensive labeling
                    scraped_data = ActorScrapedData.objects.create(
                        task=task,
                        data_type=data_type,
                        content=result,
                        platform=account.platform,
                        actor_account=account,
                        account_username=account.platform_username,
                        platform_content_id=engine.get_platform_specific_id(result),
                        # Add metadata for tracking
                        is_complete=self._validate_data_completeness(result, data_type),
                        quality_score=self._calculate_quality_score(result, data_type)
                    )
                    saved_count += 1

                    self.logger.debug(f"Saved {data_type} data from {account.platform} account @{account.platform_username}")

            # Log data labeling summary
            self.logger.info(f"Data labeling summary - Platform: {account.platform}, Account: @{account.platform_username}, Items: {saved_count}, Task: {task.task_type}")
            
            # Update task completion
            task.status = 'COMPLETED'
            task.completed_at = timezone.now()
            task.items_scraped = saved_count
            task.progress_percentage = 100.0
            task.save()
            
            self.logger.info(f"Task {task_id} completed successfully. Scraped {saved_count} items.")
            
            return {
                'success': True,
                'message': f'Task completed successfully. Scraped {saved_count} items.',
                'items_scraped': saved_count,
                'task_id': task_id
            }
            
        except ActorTask.DoesNotExist:
            return {
                'success': False,
                'error': 'Task not found'
            }
        except Exception as e:
            self.logger.error(f"Task execution error: {str(e)}")
            
            # Update task status to failed
            try:
                task = ActorTask.objects.get(id=task_id)
                task.status = 'FAILED'
                task.error_message = str(e)
                task.save()
            except:
                pass
            
            return {
                'success': False,
                'error': f'Task execution failed: {str(e)}'
            }
    
    def migrate_tiktok_account(self, tiktok_account_id: int) -> Dict[str, Any]:
        """
        Migrate a TikTokUserAccount to the new ActorAccount system.
        
        Args:
            tiktok_account_id: TikTokUserAccount ID
            
        Returns:
            Dict with migration result
        """
        try:
            tiktok_account = TikTokUserAccount.objects.get(id=tiktok_account_id)
            
            # Check if already migrated
            existing_actor_account = ActorAccount.objects.filter(
                user=tiktok_account.user,
                platform='tiktok',
                platform_username=tiktok_account.tiktok_username
            ).first()
            
            if existing_actor_account:
                return {
                    'success': True,
                    'message': 'Account already migrated',
                    'account_id': existing_actor_account.id
                }
            
            # Create new ActorAccount
            actor_account = ActorAccount.objects.create(
                user=tiktok_account.user,
                platform='tiktok',
                platform_username=tiktok_account.tiktok_username,
                platform_user_id=tiktok_account.tiktok_user_id,
                email=tiktok_account.email,
                password=tiktok_account.password,  # Already encrypted
                encrypted_session_data=tiktok_account.encrypted_session_data,
                is_active=tiktok_account.is_active,
                last_login=tiktok_account.last_login,
                session_expires_at=tiktok_account.session_expires_at,
                login_attempts=tiktok_account.login_attempts,
                last_attempt_at=tiktok_account.last_attempt_at,
                is_blocked=tiktok_account.is_blocked,
                blocked_until=tiktok_account.blocked_until
            )
            
            self.logger.info(f"Migrated TikTok account {tiktok_account.tiktok_username} to Actor system")
            
            return {
                'success': True,
                'message': 'Account migrated successfully',
                'account_id': actor_account.id
            }
            
        except TikTokUserAccount.DoesNotExist:
            return {
                'success': False,
                'error': 'TikTok account not found'
            }
        except Exception as e:
            self.logger.error(f"Migration error: {str(e)}")
            return {
                'success': False,
                'error': f'Migration failed: {str(e)}'
            }

    def _determine_data_type(self, result: Dict[str, Any], task_type: str) -> str:
        """
        Determine the data type based on the result content and task type.

        Args:
            result: Scraped data result
            task_type: Type of task that generated this data

        Returns:
            String representing the data type
        """
        # Map task types to data types
        task_to_data_type = {
            'MY_VIDEOS': 'VIDEO',
            'TARGETED_USER': 'VIDEO',
            'CONTENT_SEARCH': 'VIDEO',
            'KEYWORD_SEARCH': 'VIDEO',
            'FEED_SCRAPE': 'FEED_ITEM',
            'MY_FOLLOWERS': 'FOLLOWER',
            'MY_FOLLOWING': 'FOLLOWING',
            'MY_LIKES': 'LIKE',
            'HASHTAG_ANALYSIS': 'HASHTAG_DATA',
            'PROFILE_ANALYSIS': 'USER',
        }

        # Check if result contains specific indicators
        if 'video' in result or 'aweme_id' in result or 'video_id' in result:
            return 'VIDEO'
        elif 'user' in result or 'username' in result or 'follower_count' in result:
            return 'USER'
        elif 'hashtag' in result or 'challenge' in result:
            return 'HASHTAG_DATA'

        # Fall back to task type mapping
        return task_to_data_type.get(task_type, 'VIDEO')

    def _validate_data_completeness(self, result: Dict[str, Any], data_type: str) -> bool:
        """
        Validate if the scraped data is complete based on expected fields.

        Args:
            result: Scraped data result
            data_type: Type of data

        Returns:
            Boolean indicating if data is complete
        """
        required_fields = {
            'VIDEO': ['id', 'author', 'desc'],
            'USER': ['username', 'nickname'],
            'FOLLOWER': ['username'],
            'FOLLOWING': ['username'],
            'HASHTAG_DATA': ['title'],
            'FEED_ITEM': ['id'],
        }

        fields_to_check = required_fields.get(data_type, ['id'])

        # Check if all required fields are present and not empty
        for field in fields_to_check:
            if field not in result or not result[field]:
                return False

        return True

    def _calculate_quality_score(self, result: Dict[str, Any], data_type: str) -> float:
        """
        Calculate a quality score for the scraped data.

        Args:
            result: Scraped data result
            data_type: Type of data

        Returns:
            Float between 0.0 and 1.0 representing quality
        """
        score = 1.0

        # Define important fields for each data type
        important_fields = {
            'VIDEO': ['id', 'author', 'desc', 'stats', 'video', 'createTime'],
            'USER': ['username', 'nickname', 'signature', 'stats', 'avatarMedium'],
            'FOLLOWER': ['username', 'nickname'],
            'FOLLOWING': ['username', 'nickname'],
            'HASHTAG_DATA': ['title', 'desc', 'stats'],
        }

        fields_to_check = important_fields.get(data_type, ['id'])

        # Calculate score based on field presence
        present_fields = 0
        for field in fields_to_check:
            if field in result and result[field]:
                present_fields += 1
            else:
                score -= 0.1  # Reduce score for missing fields

        # Bonus for rich data
        if data_type == 'VIDEO':
            if result.get('stats', {}).get('playCount', 0) > 0:
                score += 0.1
            if result.get('video', {}).get('playAddr'):
                score += 0.1
        elif data_type == 'USER':
            if result.get('stats', {}).get('followerCount', 0) > 0:
                score += 0.1

        return max(0.0, min(1.0, score))  # Ensure score is between 0.0 and 1.0

    def get_user_tasks(self, user: User, platform: str = None, status: str = None, account_id: int = None) -> List[Dict[str, Any]]:
        """Get user's actor tasks with optional filters."""
        try:
            tasks = ActorTask.objects.filter(user=user)

            # Apply filters
            if platform:
                tasks = tasks.filter(platform=platform)
            if status:
                tasks = tasks.filter(status=status)
            if account_id:
                tasks = tasks.filter(account_id=account_id)

            # Order by creation date (newest first)
            tasks = tasks.order_by('-created_at')

            return [self._serialize_task(task) for task in tasks]

        except Exception as e:
            self.logger.error(f"Error getting user tasks: {str(e)}")
            return []

    def update_task(self, user: User, task_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an actor task."""
        try:
            task = ActorTask.objects.get(id=task_id, user=user)

            # Update allowed fields
            updatable_fields = [
                'task_name', 'keywords', 'max_items', 'target_identifier',
                'start_date', 'end_date', 'status'
            ]

            for field in updatable_fields:
                if field in update_data:
                    setattr(task, field, update_data[field])

            # Handle status updates with timestamps
            if 'status' in update_data:
                new_status = update_data['status']
                if new_status == 'RUNNING' and not task.started_at:
                    task.started_at = timezone.now()
                elif new_status in ['COMPLETED', 'FAILED', 'CANCELLED']:
                    task.completed_at = timezone.now()

            task.save()

            return {
                'success': True,
                'message': 'Task updated successfully',
                'task': self._serialize_task(task)
            }

        except ActorTask.DoesNotExist:
            return {
                'success': False,
                'error': 'Task not found'
            }
        except Exception as e:
            self.logger.error(f"Error updating task: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_task(self, user: User, task_id: int) -> Dict[str, Any]:
        """Delete an actor task."""
        try:
            task = ActorTask.objects.get(id=task_id, user=user)

            # Prevent deletion of running tasks
            if task.status == 'RUNNING':
                return {
                    'success': False,
                    'error': 'Cannot delete a running task. Please stop it first.'
                }

            task_name = task.task_name
            task.delete()

            return {
                'success': True,
                'message': f'Task "{task_name}" deleted successfully'
            }

        except ActorTask.DoesNotExist:
            return {
                'success': False,
                'error': 'Task not found'
            }
        except Exception as e:
            self.logger.error(f"Error deleting task: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_data_labeling_stats(self, user: User) -> Dict[str, Any]:
        """
        Get data labeling statistics for a user.

        Args:
            user: Django User instance

        Returns:
            Dict with success status and statistics
        """
        try:
            from django.db.models import Count, Avg

            # Get user's scraped data through task relationship
            user_data = ActorScrapedData.objects.filter(task__user=user)

            # Calculate statistics
            total_items = user_data.count()
            complete_items = user_data.filter(is_complete=True).count()
            avg_quality = user_data.aggregate(avg_quality=Avg('quality_score'))['avg_quality'] or 0

            # Platform breakdown
            platform_stats = user_data.values('platform').annotate(
                count=Count('id'),
                avg_quality=Avg('quality_score')
            )

            # Data type breakdown
            type_stats = user_data.values('data_type').annotate(
                count=Count('id'),
                avg_quality=Avg('quality_score')
            )

            return {
                'success': True,
                'stats': {
                    'total_items': total_items,
                    'complete_items': complete_items,
                    'completion_rate': (complete_items / total_items * 100) if total_items > 0 else 0,
                    'average_quality_score': round(avg_quality, 2),
                    'platform_breakdown': list(platform_stats),
                    'data_type_breakdown': list(type_stats)
                }
            }

        except Exception as e:
            logger.error(f"Error getting data labeling stats: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
