import json
import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional
from django.utils import timezone
from ..models import ActorScrapedData, ActorTask

logger = logging.getLogger(__name__)

class ActorDataProcessor:
    """
    Processes and stores scraped TikTok data for actor tasks
    """
    
    def __init__(self):
        self.supported_types = [
            'my_videos', 'my_followers', 'my_following', 'my_likes',
            'feed', 'user_profile', 'hashtag', 'competitor_analysis'
        ]
    
    def process_scraped_data(self, task_id: int, data: List[Dict], data_type: str) -> Dict[str, Any]:
        """
        Process and store scraped data
        
        Args:
            task_id: ActorTask ID
            data: List of scraped data items
            data_type: Type of scraped data
        
        Returns:
            dict: Processing results
        """
        try:
            if data_type not in self.supported_types:
                raise ValueError(f"Unsupported data type: {data_type}")
            
            # Get the task
            try:
                task = ActorTask.objects.get(id=task_id)
            except ActorTask.DoesNotExist:
                raise ValueError(f"Task with ID {task_id} not found")
            
            # Process data based on type
            processed_data = self._process_by_type(data, data_type)
            
            # Generate enhanced metadata
            enhanced_metadata = self._generate_enhanced_metadata(processed_data, data_type)
            
            # Create scraped data record
            scraped_data = ActorScrapedData.objects.create(
                task=task,
                content_type=data_type,
                raw_data=data,
                processed_data=processed_data,
                enhanced_metadata=enhanced_metadata,
                item_count=len(data),
                data_size=len(json.dumps(data)),
                scraped_at=timezone.now()
            )
            
            # Update task statistics
            self._update_task_stats(task, len(data))
            
            logger.info(f"Processed {len(data)} items for task {task_id} ({data_type})")
            
            return {
                'success': True,
                'scraped_data_id': scraped_data.id,
                'items_processed': len(data),
                'data_type': data_type,
                'enhanced_metadata': enhanced_metadata
            }
        
        except Exception as e:
            logger.error(f"Error processing scraped data: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'items_processed': 0
            }
    
    def _process_by_type(self, data: List[Dict], data_type: str) -> Dict[str, Any]:
        """
        Process data based on its type
        
        Args:
            data: Raw scraped data
            data_type: Type of data
        
        Returns:
            dict: Processed data
        """
        try:
            if data_type == 'my_videos':
                return self._process_video_data(data)
            elif data_type in ['my_followers', 'my_following']:
                return self._process_user_list_data(data)
            elif data_type == 'my_likes':
                return self._process_liked_videos_data(data)
            elif data_type == 'feed':
                return self._process_feed_data(data)
            elif data_type == 'user_profile':
                return self._process_user_profile_data(data)
            elif data_type == 'hashtag':
                return self._process_hashtag_data(data)
            elif data_type == 'competitor_analysis':
                return self._process_competitor_data(data)
            else:
                return {'items': data, 'summary': {}}
        
        except Exception as e:
            logger.error(f"Error processing {data_type} data: {str(e)}")
            return {'items': data, 'summary': {}, 'processing_error': str(e)}
    
    def _process_video_data(self, videos: List[Dict]) -> Dict[str, Any]:
        """
        Process video data with analytics
        
        Args:
            videos: List of video data
        
        Returns:
            dict: Processed video data with analytics
        """
        try:
            total_views = sum(video.get('view_count', 0) for video in videos)
            total_likes = sum(video.get('like_count', 0) for video in videos)
            total_comments = sum(video.get('comment_count', 0) for video in videos)
            total_shares = sum(video.get('share_count', 0) for video in videos)
            
            # Calculate engagement metrics
            avg_engagement = 0
            if total_views > 0:
                avg_engagement = ((total_likes + total_comments + total_shares) / total_views) * 100
            
            # Find top performing videos
            top_videos = sorted(
                videos,
                key=lambda x: x.get('view_count', 0),
                reverse=True
            )[:5]
            
            # Extract hashtags and mentions
            hashtags = set()
            mentions = set()
            
            for video in videos:
                description = video.get('description', '')
                hashtags.update(self._extract_hashtags(description))
                mentions.update(self._extract_mentions(description))
            
            return {
                'videos': videos,
                'analytics': {
                    'total_videos': len(videos),
                    'total_views': total_views,
                    'total_likes': total_likes,
                    'total_comments': total_comments,
                    'total_shares': total_shares,
                    'avg_views_per_video': total_views / len(videos) if videos else 0,
                    'avg_likes_per_video': total_likes / len(videos) if videos else 0,
                    'avg_engagement_rate': avg_engagement,
                    'top_performing_videos': top_videos,
                    'unique_hashtags': list(hashtags),
                    'unique_mentions': list(mentions),
                    'hashtag_count': len(hashtags),
                    'mention_count': len(mentions)
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing video data: {str(e)}")
            return {'videos': videos, 'analytics': {}, 'processing_error': str(e)}
    
    def _process_user_list_data(self, users: List[Dict]) -> Dict[str, Any]:
        """
        Process user list data (followers/following)
        
        Args:
            users: List of user data
        
        Returns:
            dict: Processed user data with analytics
        """
        try:
            # Calculate follower statistics
            total_followers = sum(user.get('follower_count', 0) for user in users)
            verified_users = [user for user in users if user.get('is_verified', False)]
            
            # Group by follower ranges
            follower_ranges = {
                'micro': 0,      # 1K - 100K
                'macro': 0,      # 100K - 1M
                'mega': 0,       # 1M+
                'nano': 0        # < 1K
            }
            
            for user in users:
                follower_count = user.get('follower_count', 0)
                if follower_count >= 1000000:
                    follower_ranges['mega'] += 1
                elif follower_count >= 100000:
                    follower_ranges['macro'] += 1
                elif follower_count >= 1000:
                    follower_ranges['micro'] += 1
                else:
                    follower_ranges['nano'] += 1
            
            # Find top users by follower count
            top_users = sorted(
                users,
                key=lambda x: x.get('follower_count', 0),
                reverse=True
            )[:10]
            
            return {
                'users': users,
                'analytics': {
                    'total_users': len(users),
                    'verified_users_count': len(verified_users),
                    'verification_rate': (len(verified_users) / len(users)) * 100 if users else 0,
                    'total_combined_followers': total_followers,
                    'avg_followers_per_user': total_followers / len(users) if users else 0,
                    'follower_distribution': follower_ranges,
                    'top_users_by_followers': top_users,
                    'verified_users': verified_users
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing user list data: {str(e)}")
            return {'users': users, 'analytics': {}, 'processing_error': str(e)}
    
    def _process_liked_videos_data(self, videos: List[Dict]) -> Dict[str, Any]:
        """
        Process liked videos data
        
        Args:
            videos: List of liked video data
        
        Returns:
            dict: Processed liked videos data
        """
        try:
            # Analyze liked content patterns
            authors = {}
            hashtags = set()
            
            for video in videos:
                # Count videos by author
                author = video.get('author_username', 'unknown')
                authors[author] = authors.get(author, 0) + 1
                
                # Extract hashtags
                description = video.get('description', '')
                hashtags.update(self._extract_hashtags(description))
            
            # Find most liked authors
            top_authors = sorted(
                authors.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            return {
                'liked_videos': videos,
                'analytics': {
                    'total_liked_videos': len(videos),
                    'unique_authors': len(authors),
                    'top_liked_authors': top_authors,
                    'hashtags_in_liked_content': list(hashtags),
                    'content_diversity_score': len(authors) / len(videos) if videos else 0
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing liked videos data: {str(e)}")
            return {'liked_videos': videos, 'analytics': {}, 'processing_error': str(e)}
    
    def _process_feed_data(self, videos: List[Dict]) -> Dict[str, Any]:
        """
        Process feed data
        
        Args:
            videos: List of feed video data
        
        Returns:
            dict: Processed feed data
        """
        try:
            # Analyze feed content
            authors = {}
            content_types = {'original': 0, 'duet': 0, 'stitch': 0}
            
            for video in videos:
                author = video.get('author_username', 'unknown')
                authors[author] = authors.get(author, 0) + 1
                
                # Analyze content type (basic detection)
                description = video.get('description', '').lower()
                if 'duet' in description:
                    content_types['duet'] += 1
                elif 'stitch' in description:
                    content_types['stitch'] += 1
                else:
                    content_types['original'] += 1
            
            return {
                'feed_videos': videos,
                'analytics': {
                    'total_feed_videos': len(videos),
                    'unique_authors_in_feed': len(authors),
                    'content_type_distribution': content_types,
                    'feed_diversity_score': len(authors) / len(videos) if videos else 0
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing feed data: {str(e)}")
            return {'feed_videos': videos, 'analytics': {}, 'processing_error': str(e)}
    
    def _process_user_profile_data(self, profiles: List[Dict]) -> Dict[str, Any]:
        """
        Process user profile data
        
        Args:
            profiles: List of user profile data
        
        Returns:
            dict: Processed profile data
        """
        try:
            if not profiles:
                return {'profiles': [], 'analytics': {}}
            
            # For single profile analysis
            if len(profiles) == 1:
                profile = profiles[0]
                return {
                    'profile': profile,
                    'analytics': {
                        'engagement_potential': self._calculate_engagement_potential(profile),
                        'influence_score': self._calculate_influence_score(profile),
                        'content_frequency': self._estimate_content_frequency(profile)
                    }
                }
            
            # For multiple profiles comparison
            total_followers = sum(p.get('follower_count', 0) for p in profiles)
            total_following = sum(p.get('following_count', 0) for p in profiles)
            verified_count = sum(1 for p in profiles if p.get('is_verified', False))
            
            return {
                'profiles': profiles,
                'analytics': {
                    'total_profiles': len(profiles),
                    'total_combined_followers': total_followers,
                    'total_combined_following': total_following,
                    'verified_profiles': verified_count,
                    'avg_followers': total_followers / len(profiles),
                    'avg_following': total_following / len(profiles)
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing profile data: {str(e)}")
            return {'profiles': profiles, 'analytics': {}, 'processing_error': str(e)}
    
    def _process_hashtag_data(self, videos: List[Dict]) -> Dict[str, Any]:
        """
        Process hashtag data
        
        Args:
            videos: List of hashtag video data
        
        Returns:
            dict: Processed hashtag data
        """
        try:
            hashtag = videos[0].get('hashtag', 'unknown') if videos else 'unknown'
            
            # Analyze hashtag performance
            total_views = sum(video.get('view_count', 0) for video in videos)
            total_engagement = sum(
                video.get('like_count', 0) + 
                video.get('comment_count', 0) + 
                video.get('share_count', 0)
                for video in videos
            )
            
            # Find top creators for this hashtag
            creators = {}
            for video in videos:
                author = video.get('author_username', 'unknown')
                if author not in creators:
                    creators[author] = {
                        'video_count': 0,
                        'total_views': 0,
                        'total_engagement': 0
                    }
                
                creators[author]['video_count'] += 1
                creators[author]['total_views'] += video.get('view_count', 0)
                creators[author]['total_engagement'] += (
                    video.get('like_count', 0) + 
                    video.get('comment_count', 0) + 
                    video.get('share_count', 0)
                )
            
            top_creators = sorted(
                creators.items(),
                key=lambda x: x[1]['total_views'],
                reverse=True
            )[:10]
            
            return {
                'hashtag': hashtag,
                'videos': videos,
                'analytics': {
                    'total_videos': len(videos),
                    'total_views': total_views,
                    'total_engagement': total_engagement,
                    'avg_views_per_video': total_views / len(videos) if videos else 0,
                    'avg_engagement_per_video': total_engagement / len(videos) if videos else 0,
                    'unique_creators': len(creators),
                    'top_creators': top_creators,
                    'hashtag_popularity_score': self._calculate_hashtag_popularity(videos)
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing hashtag data: {str(e)}")
            return {'hashtag': 'unknown', 'videos': videos, 'analytics': {}, 'processing_error': str(e)}
    
    def _process_competitor_data(self, data: List[Dict]) -> Dict[str, Any]:
        """
        Process competitor analysis data
        
        Args:
            data: List of competitor data
        
        Returns:
            dict: Processed competitor analysis
        """
        try:
            # Competitor analysis logic
            competitors = {}
            
            for item in data:
                if 'username' in item:
                    username = item['username']
                    if username not in competitors:
                        competitors[username] = {
                            'profile': item,
                            'metrics': {},
                            'content_analysis': {}
                        }
                    
                    # Calculate competitive metrics
                    competitors[username]['metrics'] = self._calculate_competitive_metrics(item)
            
            # Rank competitors
            ranked_competitors = sorted(
                competitors.items(),
                key=lambda x: x[1]['metrics'].get('influence_score', 0),
                reverse=True
            )
            
            return {
                'competitors': competitors,
                'analytics': {
                    'total_competitors': len(competitors),
                    'ranked_competitors': ranked_competitors,
                    'market_analysis': self._analyze_market_position(competitors)
                }
            }
        
        except Exception as e:
            logger.error(f"Error processing competitor data: {str(e)}")
            return {'competitors': {}, 'analytics': {}, 'processing_error': str(e)}
    
    def _generate_enhanced_metadata(self, processed_data: Dict, data_type: str) -> Dict[str, Any]:
        """
        Generate enhanced metadata for the scraped data
        
        Args:
            processed_data: Processed data
            data_type: Type of data
        
        Returns:
            dict: Enhanced metadata
        """
        try:
            metadata = {
                'data_type': data_type,
                'processing_timestamp': datetime.now().isoformat(),
                'data_quality_score': self._calculate_data_quality_score(processed_data),
                'completeness_score': self._calculate_completeness_score(processed_data),
                'freshness_score': self._calculate_freshness_score(processed_data),
                'relevance_score': self._calculate_relevance_score(processed_data, data_type),
                'data_hash': self._generate_data_hash(processed_data),
                'processing_version': '1.0',
                'analytics_available': 'analytics' in processed_data,
                'error_count': 1 if 'processing_error' in processed_data else 0,
                'data_size_mb': len(json.dumps(processed_data)) / (1024 * 1024),
                'item_count': self._count_items_in_processed_data(processed_data),
                'unique_identifiers': self._extract_unique_identifiers(processed_data),
                'content_categories': self._categorize_content(processed_data, data_type),
                'temporal_distribution': self._analyze_temporal_distribution(processed_data),
                'engagement_metrics': self._extract_engagement_metrics(processed_data)
            }
            
            return metadata
        
        except Exception as e:
            logger.error(f"Error generating enhanced metadata: {str(e)}")
            return {
                'data_type': data_type,
                'processing_timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _extract_hashtags(self, text: str) -> List[str]:
        """
        Extract hashtags from text
        
        Args:
            text: Text to extract hashtags from
        
        Returns:
            list: List of hashtags
        """
        import re
        return re.findall(r'#\w+', text.lower())
    
    def _extract_mentions(self, text: str) -> List[str]:
        """
        Extract mentions from text
        
        Args:
            text: Text to extract mentions from
        
        Returns:
            list: List of mentions
        """
        import re
        return re.findall(r'@\w+', text.lower())
    
    def _calculate_engagement_potential(self, profile: Dict) -> float:
        """
        Calculate engagement potential for a profile
        
        Args:
            profile: Profile data
        
        Returns:
            float: Engagement potential score (0-100)
        """
        try:
            followers = profile.get('follower_count', 0)
            following = profile.get('following_count', 0)
            videos = profile.get('video_count', 0)
            likes = profile.get('likes_count', 0)
            
            # Calculate ratios
            follower_following_ratio = followers / max(following, 1)
            content_engagement_ratio = likes / max(videos, 1)
            
            # Normalize and combine scores
            score = min(100, (
                (follower_following_ratio * 0.3) +
                (content_engagement_ratio / 1000 * 0.4) +
                (min(followers / 10000, 10) * 0.3)
            ) * 10)
            
            return round(score, 2)
        
        except:
            return 0.0
    
    def _calculate_influence_score(self, profile: Dict) -> float:
        """
        Calculate influence score for a profile
        
        Args:
            profile: Profile data
        
        Returns:
            float: Influence score (0-100)
        """
        try:
            followers = profile.get('follower_count', 0)
            is_verified = profile.get('is_verified', False)
            videos = profile.get('video_count', 0)
            
            # Base score from followers
            follower_score = min(50, followers / 100000 * 50)
            
            # Verification bonus
            verification_bonus = 20 if is_verified else 0
            
            # Content volume bonus
            content_bonus = min(30, videos / 100 * 30)
            
            score = follower_score + verification_bonus + content_bonus
            return round(min(100, score), 2)
        
        except:
            return 0.0
    
    def _estimate_content_frequency(self, profile: Dict) -> str:
        """
        Estimate content posting frequency
        
        Args:
            profile: Profile data
        
        Returns:
            str: Estimated frequency
        """
        try:
            videos = profile.get('video_count', 0)
            
            if videos > 1000:
                return 'very_high'  # Multiple posts per day
            elif videos > 500:
                return 'high'       # Daily
            elif videos > 100:
                return 'medium'     # Few times per week
            elif videos > 20:
                return 'low'        # Weekly
            else:
                return 'very_low'   # Occasional
        
        except:
            return 'unknown'
    
    def _calculate_hashtag_popularity(self, videos: List[Dict]) -> float:
        """
        Calculate hashtag popularity score
        
        Args:
            videos: List of videos with the hashtag
        
        Returns:
            float: Popularity score (0-100)
        """
        try:
            if not videos:
                return 0.0
            
            total_views = sum(video.get('view_count', 0) for video in videos)
            total_engagement = sum(
                video.get('like_count', 0) + 
                video.get('comment_count', 0) + 
                video.get('share_count', 0)
                for video in videos
            )
            
            # Calculate popularity based on views and engagement
            avg_views = total_views / len(videos)
            avg_engagement = total_engagement / len(videos)
            
            # Normalize scores
            view_score = min(50, avg_views / 100000 * 50)
            engagement_score = min(50, avg_engagement / 10000 * 50)
            
            return round(view_score + engagement_score, 2)
        
        except:
            return 0.0
    
    def _calculate_competitive_metrics(self, competitor: Dict) -> Dict[str, Any]:
        """
        Calculate competitive metrics for a competitor
        
        Args:
            competitor: Competitor data
        
        Returns:
            dict: Competitive metrics
        """
        try:
            return {
                'influence_score': self._calculate_influence_score(competitor),
                'engagement_potential': self._calculate_engagement_potential(competitor),
                'content_frequency': self._estimate_content_frequency(competitor),
                'follower_growth_potential': self._estimate_growth_potential(competitor),
                'market_share': self._calculate_market_share(competitor)
            }
        
        except Exception as e:
            logger.error(f"Error calculating competitive metrics: {str(e)}")
            return {}
    
    def _analyze_market_position(self, competitors: Dict) -> Dict[str, Any]:
        """
        Analyze market position among competitors
        
        Args:
            competitors: Dictionary of competitors
        
        Returns:
            dict: Market analysis
        """
        try:
            if not competitors:
                return {}
            
            total_followers = sum(
                comp['profile'].get('follower_count', 0) 
                for comp in competitors.values()
            )
            
            market_leaders = []
            for username, data in competitors.items():
                followers = data['profile'].get('follower_count', 0)
                market_share = (followers / total_followers * 100) if total_followers > 0 else 0
                
                market_leaders.append({
                    'username': username,
                    'followers': followers,
                    'market_share': round(market_share, 2)
                })
            
            market_leaders.sort(key=lambda x: x['followers'], reverse=True)
            
            return {
                'total_market_followers': total_followers,
                'market_leaders': market_leaders[:5],
                'market_concentration': self._calculate_market_concentration(market_leaders),
                'competitive_intensity': len(competitors)
            }
        
        except Exception as e:
            logger.error(f"Error analyzing market position: {str(e)}")
            return {}
    
    def _estimate_growth_potential(self, profile: Dict) -> str:
        """
        Estimate growth potential for a profile
        
        Args:
            profile: Profile data
        
        Returns:
            str: Growth potential (high/medium/low)
        """
        try:
            followers = profile.get('follower_count', 0)
            following = profile.get('following_count', 0)
            videos = profile.get('video_count', 0)
            
            # Calculate growth indicators
            engagement_ratio = following / max(followers, 1)
            content_ratio = videos / max(followers / 1000, 1)
            
            if engagement_ratio > 0.1 and content_ratio > 0.5:
                return 'high'
            elif engagement_ratio > 0.05 or content_ratio > 0.3:
                return 'medium'
            else:
                return 'low'
        
        except:
            return 'unknown'
    
    def _calculate_market_share(self, competitor: Dict) -> float:
        """
        Calculate market share for a competitor
        
        Args:
            competitor: Competitor data
        
        Returns:
            float: Market share percentage
        """
        # This would need market context, returning placeholder
        return 0.0
    
    def _calculate_market_concentration(self, market_leaders: List[Dict]) -> str:
        """
        Calculate market concentration
        
        Args:
            market_leaders: List of market leaders
        
        Returns:
            str: Concentration level
        """
        try:
            if len(market_leaders) < 3:
                return 'high'
            
            top_3_share = sum(leader['market_share'] for leader in market_leaders[:3])
            
            if top_3_share > 75:
                return 'high'
            elif top_3_share > 50:
                return 'medium'
            else:
                return 'low'
        
        except:
            return 'unknown'
    
    def _calculate_data_quality_score(self, data: Dict) -> float:
        """
        Calculate data quality score
        
        Args:
            data: Processed data
        
        Returns:
            float: Quality score (0-100)
        """
        try:
            # Basic quality indicators
            has_analytics = 'analytics' in data
            has_error = 'processing_error' in data
            
            score = 50  # Base score
            
            if has_analytics:
                score += 30
            
            if has_error:
                score -= 40
            
            # Check data completeness
            if self._has_complete_data(data):
                score += 20
            
            return max(0, min(100, score))
        
        except:
            return 0.0
    
    def _calculate_completeness_score(self, data: Dict) -> float:
        """
        Calculate data completeness score
        
        Args:
            data: Processed data
        
        Returns:
            float: Completeness score (0-100)
        """
        try:
            required_fields = ['analytics']
            present_fields = sum(1 for field in required_fields if field in data)
            
            return (present_fields / len(required_fields)) * 100
        
        except:
            return 0.0
    
    def _calculate_freshness_score(self, data: Dict) -> float:
        """
        Calculate data freshness score
        
        Args:
            data: Processed data
        
        Returns:
            float: Freshness score (0-100)
        """
        # For now, return high freshness as data is just scraped
        return 95.0
    
    def _calculate_relevance_score(self, data: Dict, data_type: str) -> float:
        """
        Calculate data relevance score
        
        Args:
            data: Processed data
            data_type: Type of data
        
        Returns:
            float: Relevance score (0-100)
        """
        try:
            # Base relevance on data type and content
            base_score = 80
            
            if 'analytics' in data:
                base_score += 15
            
            if self._count_items_in_processed_data(data) > 0:
                base_score += 5
            
            return min(100, base_score)
        
        except:
            return 50.0
    
    def _generate_data_hash(self, data: Dict) -> str:
        """
        Generate hash for data integrity
        
        Args:
            data: Data to hash
        
        Returns:
            str: Data hash
        """
        try:
            data_str = json.dumps(data, sort_keys=True)
            return hashlib.md5(data_str.encode()).hexdigest()
        except:
            return ''
    
    def _count_items_in_processed_data(self, data: Dict) -> int:
        """
        Count items in processed data
        
        Args:
            data: Processed data
        
        Returns:
            int: Item count
        """
        try:
            # Count based on data structure
            if 'videos' in data:
                return len(data['videos'])
            elif 'users' in data:
                return len(data['users'])
            elif 'liked_videos' in data:
                return len(data['liked_videos'])
            elif 'feed_videos' in data:
                return len(data['feed_videos'])
            elif 'profiles' in data:
                return len(data['profiles'])
            elif 'competitors' in data:
                return len(data['competitors'])
            else:
                return 0
        except:
            return 0
    
    def _extract_unique_identifiers(self, data: Dict) -> List[str]:
        """
        Extract unique identifiers from data
        
        Args:
            data: Processed data
        
        Returns:
            list: List of unique identifiers
        """
        try:
            identifiers = set()
            
            # Extract video IDs
            if 'videos' in data:
                for video in data['videos']:
                    if 'video_id' in video:
                        identifiers.add(f"video_{video['video_id']}")
            
            # Extract usernames
            if 'users' in data:
                for user in data['users']:
                    if 'username' in user:
                        identifiers.add(f"user_{user['username']}")
            
            return list(identifiers)
        except:
            return []
    
    def _categorize_content(self, data: Dict, data_type: str) -> List[str]:
        """
        Categorize content based on type and content
        
        Args:
            data: Processed data
            data_type: Type of data
        
        Returns:
            list: Content categories
        """
        categories = [data_type]
        
        try:
            if 'analytics' in data:
                categories.append('with_analytics')
            
            if data_type in ['my_videos', 'hashtag', 'feed']:
                categories.append('video_content')
            elif data_type in ['my_followers', 'my_following']:
                categories.append('user_content')
            elif data_type == 'user_profile':
                categories.append('profile_content')
            
            return categories
        except:
            return [data_type]
    
    def _analyze_temporal_distribution(self, data: Dict) -> Dict[str, Any]:
        """
        Analyze temporal distribution of data
        
        Args:
            data: Processed data
        
        Returns:
            dict: Temporal analysis
        """
        try:
            return {
                'scraped_at': datetime.now().isoformat(),
                'data_recency': 'fresh',
                'temporal_span': 'current'
            }
        except:
            return {}
    
    def _extract_engagement_metrics(self, data: Dict) -> Dict[str, Any]:
        """
        Extract engagement metrics from data
        
        Args:
            data: Processed data
        
        Returns:
            dict: Engagement metrics
        """
        try:
            if 'analytics' in data:
                analytics = data['analytics']
                return {
                    'total_engagement': analytics.get('total_likes', 0) + 
                                      analytics.get('total_comments', 0) + 
                                      analytics.get('total_shares', 0),
                    'avg_engagement_rate': analytics.get('avg_engagement_rate', 0),
                    'engagement_available': True
                }
            else:
                return {'engagement_available': False}
        except:
            return {'engagement_available': False}
    
    def _has_complete_data(self, data: Dict) -> bool:
        """
        Check if data is complete
        
        Args:
            data: Processed data
        
        Returns:
            bool: True if complete, False otherwise
        """
        try:
            return 'analytics' in data and not ('processing_error' in data)
        except:
            return False
    
    def _update_task_stats(self, task: ActorTask, items_count: int):
        """
        Update task statistics
        
        Args:
            task: ActorTask instance
            items_count: Number of items processed
        """
        try:
            # Update task with results
            task.items_scraped = items_count
            task.completed_at = timezone.now()
            task.save()
            
            logger.info(f"Updated task {task.id} with {items_count} items")
        
        except Exception as e:
            logger.error(f"Error updating task stats: {str(e)}")


# Convenience function for backward compatibility
def process_actor_data(task, data, data_type):
    """
    Convenience function to process actor data
    
    Args:
        task: ActorTask instance
        data: Raw scraped data
        data_type: Type of data
    
    Returns:
        dict: Processing results
    """
    processor = ActorDataProcessor()
    return processor.process_scraped_data(task.id, [data], data_type)