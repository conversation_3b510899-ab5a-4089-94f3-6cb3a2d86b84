# Enhanced Bot Detection System for TikTok Actor

This document describes the comprehensive bot detection and anti-detection system implemented for the TikTok Actor module.

## Overview

The enhanced bot detection system provides intelligent session management, account health monitoring, and sophisticated anti-detection measures to maintain reliable TikTok automation while avoiding detection.

## Key Features

### 1. Smart Session Management
- **Session Rotation**: Automatically rotates sessions based on health metrics
- **Account Health Scoring**: Comprehensive scoring system for account reliability
- **Cooldown Management**: Intelligent cooldown periods based on failure types
- **Session Quality Assessment**: Evaluates session data completeness and validity

### 2. Advanced Bot Detection Handling
- **Multi-layer Detection**: Monitors for bot detection indicators across multiple channels
- **Human Behavior Simulation**: Realistic typing, mouse movements, and browsing patterns
- **Progressive Retry Logic**: Exponential backoff with jitter for failed attempts
- **CAPTCHA Handling**: Enhanced CAPTCHA detection and resolution support

### 3. Configuration-Driven Approach
- **Centralized Configuration**: All parameters configurable via `bot_detection_config.py`
- **Easy Tuning**: Adjust thresholds, delays, and behaviors without code changes
- **Environment-Specific Settings**: Support for development and production configurations

## Architecture

### Core Components

1. **TikTokAuthenticator** (`utils/tiktok_auth.py`)
   - Enhanced login process with anti-detection measures
   - Human behavior simulation
   - Bot detection monitoring

2. **EnhancedSessionManager** (`utils/session_manager.py`)
   - Session rotation logic
   - Account health scoring
   - Cooldown management

3. **Configuration** (`config/bot_detection_config.py`)
   - Centralized parameter management
   - Threshold definitions
   - Behavior configurations

4. **Celery Tasks** (`tasks.py`)
   - Account selection logic
   - Health monitoring tasks
   - Session cleanup

### Data Flow

```
Task Request → Account Selection → Session Check → Login Attempt → Health Update
     ↓              ↓                ↓              ↓              ↓
User Request → Best Account → Rotation Check → Anti-Detection → Score Update
```

## Configuration

### Key Configuration Categories

#### Session Rotation Thresholds
```python
SESSION_ROTATION_THRESHOLDS = {
    'anti_bot_score': 0.3,        # Rotate if anti-bot score drops below 30%
    'session_quality': 0.4,       # Rotate if session quality drops below 40%
    'login_success_rate': 0.6,    # Rotate if login success rate drops below 60%
    'max_session_age_days': 7,    # Rotate sessions older than 7 days
}
```

#### Health Score Weights
```python
HEALTH_SCORE_WEIGHTS = {
    'session_quality': 0.3,       # 30% weight for session data quality
    'anti_bot_score': 0.25,       # 25% weight for anti-bot detection score
    'login_success_rate': 0.2,    # 20% weight for login success rate
    'account_age': 0.15,          # 15% weight for account age
    'recent_activity': 0.1,       # 10% weight for recent activity
}
```

#### Cooldown Periods
```python
COOLDOWN_PERIODS = {
    'bot_detection': timedelta(hours=6),    # 6 hours for bot detection
    'rate_limiting': timedelta(hours=2),    # 2 hours for rate limiting
    'captcha_timeout': timedelta(hours=1),  # 1 hour for CAPTCHA timeout
    'general_failure': timedelta(minutes=30), # 30 minutes for general failures
}
```

## Usage

### Basic Task Execution

```python
# Select best account for a task
from actor_tiktok.tasks import select_best_account_for_task

result = select_best_account_for_task.delay(user_id=123, task_type='scraping')
account_info = result.get()

if account_info['success']:
    account_id = account_info['selected_account_id']
    health_score = account_info['health_score']
    print(f"Selected account {account_id} with health score {health_score}")
```

### Health Monitoring

```python
# Monitor account health
from actor_tiktok.tasks import monitor_account_health

health_report = monitor_account_health.delay()
report = health_report.get()

print(f"Healthy accounts: {report['healthy_accounts']}")
print(f"Unhealthy accounts: {report['unhealthy_accounts']}")
```

### Manual Session Management

```python
# Use enhanced session manager directly
from actor_tiktok.utils.session_manager import EnhancedSessionManager

session_manager = EnhancedSessionManager()

# Check if rotation is needed
rotation_result = session_manager.rotate_session_if_needed(account_id)
if rotation_result['rotated']:
    print(f"Session rotated: {rotation_result['reason']}")

# Get account health score
health_score = session_manager.get_account_health_score(account_id)
print(f"Health score: {health_score['overall_score']:.2f}")
print(f"Risk level: {health_score['risk_level']}")
```

## Management Commands

### Test Bot Detection System

```bash
# Check health of all accounts
python manage.py test_bot_detection --health-check

# Test login for specific account
python manage.py test_bot_detection --test-login --account-id 123

# Simulate bot detection scenarios
python manage.py test_bot_detection --simulate-detection

# Verbose output
python manage.py test_bot_detection --health-check --verbose
```

## Monitoring and Alerts

### Health Metrics

- **Overall Score**: Weighted combination of all health factors (0.0 - 1.0)
- **Risk Levels**: 
  - Low (≥0.7): Healthy accounts, safe for use
  - Medium (0.4-0.7): Moderate risk, monitor closely
  - High (0.2-0.4): High risk, consider rotation
  - Critical (<0.2): Immediate attention required

### Key Performance Indicators

1. **Login Success Rate**: Percentage of successful logins
2. **Bot Detection Rate**: Frequency of bot detection triggers
3. **Session Quality**: Average session data completeness
4. **Account Availability**: Percentage of accounts not in cooldown

## Troubleshooting

### Common Issues

#### High Bot Detection Rate
- **Symptoms**: Frequent bot detection triggers, low anti-bot scores
- **Solutions**: 
  - Increase human behavior simulation
  - Rotate browser fingerprints more frequently
  - Extend delays between actions

#### Low Session Quality
- **Symptoms**: Missing cookies, incomplete session data
- **Solutions**:
  - Check browser configuration
  - Verify cookie persistence
  - Review session storage logic

#### Account Cooldowns
- **Symptoms**: Many accounts in cooldown state
- **Solutions**:
  - Review cooldown periods in configuration
  - Implement account rotation strategy
  - Monitor for rate limiting patterns

### Debug Mode

Enable debug mode in configuration for detailed logging:

```python
DEVELOPMENT_CONFIG = {
    'debug_mode': True,
    'log_detailed_errors': True,
    'save_screenshots_on_failure': True,
}
```

## Best Practices

### Account Management
1. **Diversify Accounts**: Use accounts with different creation dates and activity patterns
2. **Rotate Regularly**: Don't rely on single accounts for extended periods
3. **Monitor Health**: Regular health checks prevent issues before they occur
4. **Respect Limits**: Honor cooldown periods and rate limits

### Configuration Tuning
1. **Start Conservative**: Begin with longer delays and higher thresholds
2. **Monitor Metrics**: Track success rates and adjust accordingly
3. **Environment-Specific**: Use different settings for development and production
4. **Regular Review**: Periodically review and update configurations

### Error Handling
1. **Graceful Degradation**: Handle failures without breaking the entire system
2. **Retry Logic**: Implement intelligent retry mechanisms
3. **Logging**: Comprehensive logging for debugging and monitoring
4. **Alerting**: Set up alerts for critical issues

## Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Predictive models for bot detection
2. **Advanced Fingerprinting**: More sophisticated browser fingerprint rotation
3. **Behavioral Analytics**: Learning from successful vs. failed sessions
4. **Real-time Adaptation**: Dynamic parameter adjustment based on success rates

### Integration Opportunities
1. **External Monitoring**: Integration with monitoring services
2. **Analytics Dashboard**: Real-time health and performance metrics
3. **API Endpoints**: RESTful APIs for external system integration
4. **Webhook Support**: Event-driven notifications for critical events

## Support

For issues or questions regarding the bot detection system:

1. Check the logs for detailed error information
2. Use the management command for system diagnostics
3. Review configuration settings for proper values
4. Monitor account health metrics for patterns

## Security Considerations

- **Credential Protection**: Never log or expose account credentials
- **Session Security**: Encrypt session data at rest
- **Access Control**: Limit access to sensitive configuration parameters
- **Audit Trail**: Maintain logs of all system actions for security review