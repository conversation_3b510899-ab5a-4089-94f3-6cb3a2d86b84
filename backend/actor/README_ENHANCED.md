# Enhanced TikTok Login Actor

## Overview

The Enhanced TikTok Login Actor is a comprehensive system designed for reliable TikTok authentication and data scraping with advanced anti-detection measures, proxy support, and robust error handling.

## Key Features

### 🛡️ Advanced Anti-Detection
- **Undetected Chrome Driver**: Uses undetected-chromedriver to bypass bot detection
- **Browser Fingerprinting Protection**: Randomizes WebGL, Canvas, and hardware fingerprints
- **Human Behavior Simulation**: Mimics natural mouse movements, typing patterns, and delays
- **Stealth Mode**: Maximum stealth configuration for sensitive operations
- **User Agent Rotation**: Dynamic user agent switching with realistic profiles

### 🌐 Intelligent Proxy Support
- **Proxy Health Monitoring**: Automatic proxy testing and health checks
- **Smart Rotation**: Round-robin, random, or performance-based proxy selection
- **Failover Mechanisms**: Automatic proxy switching on failures
- **Geographic Distribution**: Support for location-based proxy selection
- **Performance Metrics**: Response time and success rate tracking

### 💾 Advanced Session Management
- **Session Persistence**: Encrypted session storage with automatic backup
- **Session Health Monitoring**: Real-time session quality assessment
- **Automatic Rotation**: Smart session rotation based on health metrics
- **Recovery Mechanisms**: Session restore from backups on failures
- **Multi-Account Support**: Concurrent session management for multiple accounts

### 🔄 Comprehensive Error Handling
- **Error Classification**: Intelligent error type detection and classification
- **Recovery Strategies**: Specific recovery methods for different error types
- **Adaptive Retries**: Smart retry logic with exponential backoff
- **Rate Limit Handling**: Automatic rate limit detection and recovery
- **Captcha Management**: Advanced captcha detection and handling

### 📊 Account Health Monitoring
- **Health Metrics**: Login success rate, scraping performance, error frequency
- **Risk Assessment**: Automated account risk evaluation
- **Proactive Alerts**: Early warning system for account issues
- **Automatic Actions**: Auto-rotation and pausing of problematic accounts
- **Performance Analytics**: Detailed performance tracking and reporting

## Architecture

### Core Components

1. **EnhancedTikTokAuthenticator**: Main authentication engine with multiple login strategies
2. **AntiDetectionManager**: Advanced stealth and anti-detection measures
3. **ProxyManager**: Intelligent proxy management and rotation
4. **EnhancedSessionManager**: Session persistence and health monitoring
5. **EnhancedTikTokScraper**: Comprehensive data extraction with rate limiting
6. **Account Health Monitor**: Real-time account performance tracking

### Login Strategies

The system employs multiple login strategies in order of preference:

1. **Undetected Chrome**: Primary strategy using undetected-chromedriver
2. **Stealth Mode**: Maximum stealth with advanced fingerprint protection
3. **Mobile View**: Mobile browser simulation for different detection patterns
4. **Alternative URLs**: Fallback using different TikTok login endpoints

## Configuration

### Basic Configuration

```python
# Enhanced authentication configuration
ENHANCED_CONFIG = {
    'use_undetected_chrome': True,
    'enable_proxy_support': True,
    'session_backup_enabled': True,
    'health_monitoring_enabled': True
}

# Proxy configuration
PROXY_CONFIG = {
    'enabled': True,
    'proxies': [
        {
            'host': '127.0.0.1',
            'port': 8080,
            'username': 'user',
            'password': 'pass',
            'protocol': 'http'
        }
    ],
    'rotation_strategy': 'best_performance',
    'max_failures': 3
}
```

### Rate Limiting Configuration

```python
RATE_LIMITING_CONFIG = {
    'requests_per_minute': 30,
    'requests_per_hour': 500,
    'delay_between_requests': (2, 5),
    'adaptive_delays': True
}
```

## Usage

### Enhanced Login Task

```python
from actor_tiktok.tasks import enhanced_actor_login_task

# Execute enhanced login
result = enhanced_actor_login_task.delay(
    user_id=1,
    tiktok_username='your_username',
    tiktok_password='your_password',
    use_2fa=False,
    proxy_config=PROXY_CONFIG
)
```

### Enhanced Scraping Task

```python
from actor_tiktok.tasks import enhanced_scraping_task

# Scrape user profile
scraping_config = {
    'type': 'profile',
    'target': 'target_username',
    'options': {
        'include_videos': True,
        'video_limit': 50
    },
    'proxy_config': PROXY_CONFIG,
    'rate_limit_config': RATE_LIMITING_CONFIG
}

result = enhanced_scraping_task.delay(
    user_id=1,
    account_id=1,
    scraping_config=scraping_config
)
```

### Direct API Usage

```python
from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper

# Initialize authenticator
authenticator = EnhancedTikTokAuthenticator(proxy_config=PROXY_CONFIG)

# Perform login
login_result = authenticator.login(
    username='your_username',
    password='your_password',
    account_id=1
)

if login_result['success']:
    # Initialize scraper
    scraper = EnhancedTikTokScraper(
        proxy_config=PROXY_CONFIG,
        rate_limit_config=RATE_LIMITING_CONFIG
    )
    
    # Prepare scraper with session
    scraper.login_and_prepare(
        username='your_username',
        password='your_password',
        account_id=1
    )
    
    # Scrape data
    profile_data = scraper.scrape_user_profile('target_username')
    
    # Cleanup
    scraper.cleanup()
```

## Best Practices

### 1. Account Management
- Use separate accounts for different scraping tasks
- Monitor account health regularly
- Implement account rotation for high-volume operations
- Keep backup accounts ready for failover

### 2. Rate Limiting
- Respect TikTok's rate limits to avoid detection
- Use adaptive delays based on response times
- Implement burst protection for sudden traffic spikes
- Monitor rate limit violations and adjust accordingly

### 3. Proxy Usage
- Use high-quality residential proxies when possible
- Rotate proxies regularly to avoid IP-based blocking
- Monitor proxy health and performance
- Have backup proxies ready for failover

### 4. Error Handling
- Implement comprehensive error logging
- Use appropriate retry strategies for different error types
- Monitor error patterns to identify systemic issues
- Have manual intervention procedures for critical errors

### 5. Security
- Encrypt all sensitive data including passwords and session data
- Use secure communication channels
- Implement access controls and authentication
- Regular security audits and updates

## Monitoring and Maintenance

### Health Monitoring
- Account performance metrics
- Login success rates
- Scraping efficiency
- Error frequency and types
- Proxy performance

### Maintenance Tasks
- Regular session cleanup
- Proxy health checks
- Account rotation
- Performance optimization
- Security updates

## Troubleshooting

### Common Issues

1. **Login Failures**
   - Check account credentials
   - Verify proxy connectivity
   - Review rate limiting settings
   - Check for account blocks

2. **Scraping Errors**
   - Verify session validity
   - Check rate limits
   - Review proxy performance
   - Validate target data

3. **Performance Issues**
   - Monitor resource usage
   - Optimize browser settings
   - Review proxy performance
   - Check network connectivity

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger('actor_tiktok').setLevel(logging.DEBUG)
```

## Dependencies

- selenium>=4.0.0
- undetected-chromedriver>=3.5.0
- fake-useragent>=2.0.0
- requests>=2.25.0
- cryptography>=3.0.0
- django>=4.0.0
- celery>=5.0.0

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please refer to the project documentation or contact the development team.
