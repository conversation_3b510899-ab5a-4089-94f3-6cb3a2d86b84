#!/usr/bin/env python3
"""
Simple TikTok Actor Test Script
Tests core login functionality without complex driver setup
"""

import os
import sys
import time
import json
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
import django
django.setup()

from actor_tiktok.utils.tiktok_auth import TikTokAuthenticator
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SimpleTikTokTest:
    def __init__(self):
        self.driver = None
        self.auth = None
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0
            }
        }
    
    def setup_simple_driver(self):
        """Setup a simple Chrome driver without complex anti-detection"""
        try:
            print("🚀 Setting up simple Chrome driver...")
            
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Create driver
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # Basic stealth
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Initialize authenticator
            self.auth = TikTokAuthenticator()
            
            print("✅ Simple driver setup completed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Driver setup failed: {str(e)}")
            return False
    
    def test_basic_navigation(self):
        """Test basic navigation to TikTok"""
        test_name = "basic_navigation"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Navigate to TikTok
            self.driver.get('https://www.tiktok.com')
            time.sleep(5)
            
            # Check if page loaded
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            success = 'tiktok.com' in current_url and len(page_title) > 0
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'current_url': current_url,
                'page_title': page_title
            }
            
            print(f"{'✅' if success else '❌'} Basic navigation: {'PASSED' if success else 'FAILED'}")
            print(f"   URL: {current_url}")
            print(f"   Title: {page_title}")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_login_page_access(self):
        """Test accessing the login page"""
        test_name = "login_page_access"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Navigate to login page
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            current_url = self.driver.current_url
            page_source = self.driver.page_source
            
            # Check for login elements
            login_indicators = [
                'login' in current_url.lower(),
                'email' in page_source.lower(),
                'password' in page_source.lower(),
                'sign in' in page_source.lower() or 'log in' in page_source.lower()
            ]
            
            success = any(login_indicators)
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'current_url': current_url,
                'login_indicators_found': sum(login_indicators)
            }
            
            print(f"{'✅' if success else '❌'} Login page access: {'PASSED' if success else 'FAILED'}")
            print(f"   URL: {current_url}")
            print(f"   Login indicators found: {sum(login_indicators)}/4")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_dynamic_selectors(self):
        """Test the dynamic selector system"""
        test_name = "dynamic_selectors"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Test getting dynamic selectors for different element types
            test_element_types = ['phone_email_username', 'email_username_tab', 'username_input', 'password_input', 'login_button']
            
            selectors = {}
            for element_type in test_element_types:
                try:
                    element_selectors = self.auth._get_dynamic_selectors(element_type)
                    selectors[element_type] = element_selectors
                except Exception as e:
                    selectors[element_type] = {'error': str(e)}
            
            # Check if selectors are properly structured
            required_keys = test_element_types
            
            selectors_valid = all(key in selectors and 'error' not in selectors[key] for key in required_keys)
            
            # Test finding elements (even if they don't exist)
            element_tests = {}
            for key in required_keys:
                try:
                    element = self.auth._find_element_with_dynamic_selectors(self.driver, key, timeout=2)
                    element_tests[key] = element is not None
                except Exception:
                    element_tests[key] = False
            
            success = selectors_valid
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'selectors_valid': selectors_valid,
                'element_tests': element_tests,
                'total_selectors': len(selectors)
            }
            
            print(f"{'✅' if success else '❌'} Dynamic selectors: {'PASSED' if success else 'FAILED'}")
            print(f"   Total selectors: {len(selectors)}")
            print(f"   Elements found: {sum(element_tests.values())}/{len(element_tests)}")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_javascript_error_handling(self):
        """Test JavaScript error detection"""
        test_name = "javascript_error_handling"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Test atob polyfill injection
            self.auth._handle_atob_error(self.driver)
            
            # Test atob function
            atob_test = self.driver.execute_script("""
                try {
                    var test = btoa('test');
                    var decoded = atob(test);
                    return decoded === 'test';
                } catch(e) {
                    return false;
                }
            """)
            
            # Test error checking function
            try:
                js_errors = self.auth._check_javascript_errors(self.driver)
                error_check_works = isinstance(js_errors, list)
            except Exception:
                error_check_works = False
            
            success = atob_test and error_check_works
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'atob_working': atob_test,
                'error_check_working': error_check_works
            }
            
            print(f"{'✅' if success else '❌'} JavaScript error handling: {'PASSED' if success else 'FAILED'}")
            print(f"   atob function: {'✅' if atob_test else '❌'}")
            print(f"   Error checking: {'✅' if error_check_works else '❌'}")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_popup_handling(self):
        """Test popup handling functionality"""
        test_name = "popup_handling"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Navigate to login page
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            # Test cookie consent handling
            try:
                cookie_result = self.auth._handle_cookie_consent(self.driver)
                cookie_handling_works = isinstance(cookie_result, bool)
            except Exception as e:
                cookie_result = False
                cookie_handling_works = False
            
            # Test app popup handling
            try:
                popup_result = self.auth._handle_app_popup(self.driver)
                popup_handling_works = isinstance(popup_result, bool)
            except Exception as e:
                popup_result = False
                popup_handling_works = False
            
            # Check for specific popup elements
            popup_elements_found = {
                'not_now_button': False,
                'continue_browser': False,
                'modal_overlay': False
            }
            
            try:
                # Check for "Not now" button with specific selector
                not_now_btn = self.driver.find_elements(By.CSS_SELECTOR, 'button[data-e2e="bottom-cta-cancel-btn"]')
                popup_elements_found['not_now_button'] = len(not_now_btn) > 0
            except Exception:
                pass
            
            try:
                # Check for continue in browser elements
                continue_elements = self.driver.find_elements(By.XPATH, '//*[contains(translate(text(), "ABCDEFGHIJKLMNOPQRSTUVWXYZ", "abcdefghijklmnopqrstuvwxyz"), "continue in browser")]')
                popup_elements_found['continue_browser'] = len(continue_elements) > 0
            except Exception:
                pass
            
            try:
                # Check for modal overlays
                modal_elements = self.driver.find_elements(By.CSS_SELECTOR, '.modal-overlay, .popup-overlay, [class*="modal"][class*="overlay"]')
                popup_elements_found['modal_overlay'] = len(modal_elements) > 0
            except Exception:
                pass
            
            success = cookie_handling_works and popup_handling_works
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'cookie_result': cookie_result,
                'popup_result': popup_result,
                'cookie_handling_works': cookie_handling_works,
                'popup_handling_works': popup_handling_works,
                'popup_elements_found': popup_elements_found
            }
            
            print(f"{'✅' if success else '❌'} Popup handling: {'PASSED' if success else 'FAILED'}")
            print(f"   Cookie consent: {'✅' if cookie_handling_works else '❌'} (result: {cookie_result})")
            print(f"   App popup: {'✅' if popup_handling_works else '❌'} (result: {popup_result})")
            print(f"   Popup elements found: {sum(popup_elements_found.values())}/3")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_8_step_login_process(self):
        """Test the complete 8-step login process"""
        test_name = "8_step_login_process"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Step 1: Access login page
            print("   Step 1: Accessing login page...")
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            step_results = {
                'step_1_access': 'login' in self.driver.current_url.lower(),
                'step_2_modal_handling': False,
                'step_3_method_selection': False,
                'step_4_email_username_selection': False,
                'step_5_form_detection': False,
                'step_6_username_field': False,
                'step_7_password_field': False,
                'step_8_login_button': False
            }
            
            # Step 2: Handle "Get full app experience" modal
            print("   Step 2: Checking for 'Get full app experience' modal...")
            try:
                modal_result = self.auth._handle_get_full_app_modal(self.driver)
                step_results['step_2_modal_handling'] = isinstance(modal_result, bool)
                print(f"      Modal handling: {'✅' if step_results['step_2_modal_handling'] else '❌'}")
            except Exception as e:
                print(f"      Modal handling failed: {str(e)}")
            
            # Step 3: Select "Use phone / email / username"
            print("   Step 3: Selecting login method...")
            try:
                # Test specific selector first
                specific_selectors = [
                    'div[data-e2e="channel-item"][tabindex="0"][role="link"][class="tiktok-17hparj-DivBoxContainer e1cgu1qo0"]',
                    'div[data-e2e="channel-item"][role="link"]',
                    'div[class*="tiktok-17hparj-DivBoxContainer"]',
                    'div[class*="e1cgu1qo0"]'
                ]
                
                phone_email_element = None
                selector_used = None
                
                # Try specific selectors first
                for selector in specific_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements and elements[0].is_displayed():
                            phone_email_element = elements[0]
                            selector_used = selector
                            print(f"      Found with specific selector: {selector[:50]}...")
                            break
                    except Exception:
                        continue
                
                # Fallback to dynamic selectors if specific ones don't work
                if not phone_email_element:
                    phone_email_element = self.auth._find_element_with_dynamic_selectors(self.driver, 'phone_email_username', timeout=5)
                    if phone_email_element:
                        selector_used = "dynamic_selectors"
                        print("      Found with dynamic selectors")
                
                step_results['step_3_method_selection'] = phone_email_element is not None
                print(f"      Method selection element: {'✅' if step_results['step_3_method_selection'] else '❌'}")
                if selector_used:
                    print(f"      Selector used: {selector_used[:50]}...")
            except Exception as e:
                print(f"      Method selection failed: {str(e)}")
            
            # Step 4: Handle email/username selection
            print("   Step 4: Handling email/username selection...")
            try:
                email_selection_result = self.auth._handle_email_username_selection(self.driver)
                step_results['step_4_email_username_selection'] = isinstance(email_selection_result, bool)
                print(f"      Email/username selection: {'✅' if step_results['step_4_email_username_selection'] else '❌'}")
            except Exception as e:
                print(f"      Email/username selection failed: {str(e)}")
            
            # Step 5: Check if form is visible
            print("   Step 5: Checking form visibility...")
            try:
                form_elements = self.driver.find_elements(By.CSS_SELECTOR, 'form, [class*="form"], [class*="login"]')
                step_results['step_5_form_detection'] = len(form_elements) > 0
                print(f"      Form detection: {'✅' if step_results['step_5_form_detection'] else '❌'}")
            except Exception as e:
                print(f"      Form detection failed: {str(e)}")
            
            # Step 6: Find username/email field with specific selectors
            print("   Step 6: Finding username/email field...")
            try:
                username_selectors = [
                    'input[type="text"][placeholder="Email or username"][autocomplete="webauthn"][name="username"][class="tiktok-11to27l-InputContainer etcs7ny1"]',
                    'input[placeholder="Email or username"][name="username"]',
                    'input[placeholder="Email or username"]',
                    'input[name="username"][type="text"]'
                ]
                
                username_found = False
                for selector in username_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements and elements[0].is_displayed():
                            username_found = True
                            print(f"      Found username field with: {selector[:50]}...")
                            break
                    except Exception:
                        continue
                
                step_results['step_6_username_field'] = username_found
                print(f"      Username field: {'✅' if username_found else '❌'}")
            except Exception as e:
                print(f"      Username field detection failed: {str(e)}")
            
            # Step 7: Find password field with specific selectors
            print("   Step 7: Finding password field...")
            try:
                password_selectors = [
                    'input[type="password"][placeholder="Password"][autocomplete="new-password"][class="tiktok-wv3bkt-InputContainer etcs7ny1"]',
                    'input[placeholder="Password"][type="password"]',
                    'input[type="password"][autocomplete="new-password"]',
                    'input[type="password"]'
                ]
                
                password_found = False
                for selector in password_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements and elements[0].is_displayed():
                            password_found = True
                            print(f"      Found password field with: {selector[:50]}...")
                            break
                    except Exception:
                        continue
                
                step_results['step_7_password_field'] = password_found
                print(f"      Password field: {'✅' if password_found else '❌'}")
            except Exception as e:
                print(f"      Password field detection failed: {str(e)}")
            
            # Step 8: Find login button with specific selectors
            print("   Step 8: Finding login button...")
            try:
                login_button_selectors = [
                    'button[type="submit"][data-e2e="login-button"][class="e1w6iovg0 tiktok-11sviba-Button-StyledButton ehk74z00"]',
                    'button[data-e2e="login-button"][type="submit"]',
                    'button[data-e2e="login-button"]',
                    'button[type="submit"]'
                ]
                
                login_button_found = False
                for selector in login_button_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements and elements[0].is_displayed():
                            login_button_found = True
                            print(f"      Found login button with: {selector[:50]}...")
                            break
                    except Exception:
                        continue
                
                step_results['step_8_login_button'] = login_button_found
                print(f"      Login button: {'✅' if login_button_found else '❌'}")
            except Exception as e:
                print(f"      Login button detection failed: {str(e)}")
            
            # Calculate success
            completed_steps = sum(step_results.values())
            total_steps = len(step_results)
            success_rate = (completed_steps / total_steps) * 100
            
            # Consider test successful if at least 6 out of 8 steps work
            success = completed_steps >= 6
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'step_results': step_results,
                'completed_steps': completed_steps,
                'total_steps': total_steps,
                'success_rate': success_rate
            }
            
            print(f"{'✅' if success else '❌'} 8-step login process: {'PASSED' if success else 'FAILED'}")
            print(f"   Completed steps: {completed_steps}/{total_steps} ({success_rate:.1f}%)")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def test_login_method_detection(self):
        """Test login method selection detection with popup handling"""
        test_name = "login_method_detection"
        print(f"\n🧪 Testing {test_name}...")
        
        try:
            # Navigate to login page
            self.driver.get('https://www.tiktok.com/login')
            time.sleep(5)
            
            # Handle popups first
            print("   Handling cookie consent...")
            self.auth._handle_cookie_consent(self.driver)
            time.sleep(2)
            
            print("   Handling app popup...")
            self.auth._handle_app_popup(self.driver)
            time.sleep(2)
            
            # Test login method selection
            try:
                result = self.auth._select_login_method(self.driver)
                method_selection_works = isinstance(result, dict)
            except Exception as e:
                result = {'error': str(e)}
                method_selection_works = False
            
            # Check for form elements after method selection
            form_elements = {
                'username_field': False,
                'password_field': False,
                'submit_button': False
            }
            
            try:
                username_field = self.auth._find_element_with_dynamic_selectors(self.driver, 'username_input', timeout=3)
                form_elements['username_field'] = username_field is not None
            except Exception:
                pass
            
            try:
                password_field = self.auth._find_element_with_dynamic_selectors(self.driver, 'password_input', timeout=3)
                form_elements['password_field'] = password_field is not None
            except Exception:
                pass
            
            try:
                submit_button = self.auth._find_element_with_dynamic_selectors(self.driver, 'login_button', timeout=3)
                form_elements['submit_button'] = submit_button is not None
            except Exception:
                pass
            
            success = method_selection_works and any(form_elements.values())
            
            self.test_results['tests'][test_name] = {
                'success': success,
                'method_selection_result': result,
                'form_elements': form_elements
            }
            
            print(f"{'✅' if success else '❌'} Login method detection: {'PASSED' if success else 'FAILED'}")
            print(f"   Method selection: {'✅' if method_selection_works else '❌'}")
            print(f"   Form elements found: {sum(form_elements.values())}/3")
            
            return success
            
        except Exception as e:
            print(f"❌ {test_name} failed: {str(e)}")
            self.test_results['tests'][test_name] = {'success': False, 'error': str(e)}
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🎯 Starting TikTok Actor Simple Tests")
        print("=" * 50)
        
        # Setup driver
        if not self.setup_simple_driver():
            print("❌ Cannot proceed without driver setup")
            return False
        
        # List of tests to run
        tests = [
            self.test_basic_navigation,
            self.test_login_page_access,
            self.test_dynamic_selectors,
            self.test_javascript_error_handling,
            self.test_popup_handling,
            self.test_8_step_login_process,
            self.test_login_method_detection
        ]
        
        # Run tests
        for test_func in tests:
            try:
                result = test_func()
                self.test_results['summary']['total_tests'] += 1
                if result:
                    self.test_results['summary']['passed'] += 1
                else:
                    self.test_results['summary']['failed'] += 1
            except Exception as e:
                print(f"❌ Test {test_func.__name__} threw exception: {str(e)}")
                self.test_results['summary']['total_tests'] += 1
                self.test_results['summary']['failed'] += 1
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print(f"Total Tests: {self.test_results['summary']['total_tests']}")
        print(f"Passed: {self.test_results['summary']['passed']} ✅")
        print(f"Failed: {self.test_results['summary']['failed']} ❌")
        
        success_rate = (self.test_results['summary']['passed'] / self.test_results['summary']['total_tests']) * 100
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Save results
        try:
            with open('tiktok_simple_test_results.json', 'w') as f:
                json.dump(self.test_results, f, indent=2)
            print("\n📄 Test results saved to: tiktok_simple_test_results.json")
        except Exception as e:
            print(f"⚠️ Could not save test results: {str(e)}")
        
        return success_rate >= 60  # Consider 60% success rate as acceptable
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if self.driver:
                self.driver.quit()
                print("🧹 Driver cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup error: {str(e)}")

def main():
    """Main test execution"""
    test_runner = SimpleTikTokTest()
    
    try:
        success = test_runner.run_all_tests()
        
        if success:
            print("\n🎉 Tests completed successfully!")
            print("The TikTok actor login system appears to be working correctly.")
        else:
            print("\n⚠️ Some tests failed.")
            print("Please review the test results for details.")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        return 1
    finally:
        test_runner.cleanup()

if __name__ == "__main__":
    exit(main())