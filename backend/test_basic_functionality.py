#!/usr/bin/env python3
"""
Basic Functionality Test for Enhanced TikTok Actor

This script tests the basic functionality without attempting real TikTok login,
focusing on system components and browser automation capabilities.
"""

import os
import sys
import django
import logging
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_browser_automation():
    """Test basic browser automation capabilities"""
    logger.info("=== Testing Browser Automation ===")
    
    try:
        from actor_tiktok.utils.anti_detection import AntiDetectionManager
        
        # Initialize anti-detection manager
        anti_detection = AntiDetectionManager()
        
        # Setup driver (headless for testing)
        logger.info("Setting up Chrome driver...")
        driver = anti_detection.setup_driver(headless=True, use_undetected=False)
        
        # Test basic navigation
        logger.info("Testing navigation to Google...")
        driver.get("https://www.google.com")
        
        # Check if page loaded
        title = driver.title
        logger.info(f"Page title: {title}")
        
        if "Google" in title:
            logger.info("✅ Basic browser automation working")
            result = True
        else:
            logger.warning("⚠️ Unexpected page title")
            result = False
        
        # Test some basic interactions
        try:
            # Find search box
            search_box = driver.find_element("name", "q")
            search_box.send_keys("TikTok")
            logger.info("✅ Element interaction working")
        except Exception as e:
            logger.warning(f"⚠️ Element interaction issue: {str(e)}")
        
        # Cleanup
        driver.quit()
        return result
        
    except Exception as e:
        logger.error(f"❌ Browser automation test failed: {str(e)}")
        return False

def test_enhanced_components():
    """Test enhanced components initialization"""
    logger.info("=== Testing Enhanced Components ===")
    
    try:
        # Test Enhanced Authenticator
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        
        authenticator = EnhancedTikTokAuthenticator()
        logger.info(f"✅ Enhanced Authenticator: {len(authenticator.login_strategies)} strategies")
        
        # Test Enhanced Scraper
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        scraper = EnhancedTikTokScraper()
        logger.info(f"✅ Enhanced Scraper: {len(scraper.selectors)} selectors")
        
        # Test Proxy Manager
        from actor_tiktok.utils.proxy_manager import ProxyManager
        
        proxy_manager = ProxyManager()
        stats = proxy_manager.get_proxy_stats()
        logger.info(f"✅ Proxy Manager: {stats['total_proxies']} proxies")
        
        # Test Session Manager
        from actor_tiktok.utils.session_manager import EnhancedSessionManager
        
        session_manager = EnhancedSessionManager()
        logger.info("✅ Enhanced Session Manager initialized")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced components test failed: {str(e)}")
        return False

def test_configuration_loading():
    """Test configuration loading"""
    logger.info("=== Testing Configuration Loading ===")
    
    try:
        from actor_tiktok.config.enhanced_config import (
            ENHANCED_ANTI_DETECTION,
            PROXY_CONFIG,
            RATE_LIMITING_CONFIG,
            SCRAPING_CONFIG
        )
        
        logger.info(f"✅ Anti-detection config: {len(ENHANCED_ANTI_DETECTION)} settings")
        logger.info(f"✅ Proxy config: {len(PROXY_CONFIG)} settings")
        logger.info(f"✅ Rate limiting config: {len(RATE_LIMITING_CONFIG)} settings")
        logger.info(f"✅ Scraping config: {len(SCRAPING_CONFIG)} settings")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration loading test failed: {str(e)}")
        return False

def test_database_connectivity():
    """Test database connectivity and models"""
    logger.info("=== Testing Database Connectivity ===")
    
    try:
        from django.contrib.auth.models import User
        from actor_tiktok.models import TikTokUserAccount, ActorScrapedData
        
        # Test model access
        user_count = User.objects.count()
        account_count = TikTokUserAccount.objects.count()
        data_count = ActorScrapedData.objects.count()
        
        logger.info(f"✅ Database accessible:")
        logger.info(f"  - Users: {user_count}")
        logger.info(f"  - TikTok Accounts: {account_count}")
        logger.info(f"  - Scraped Data: {data_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connectivity test failed: {str(e)}")
        return False

def test_error_handling():
    """Test error handling and recovery mechanisms"""
    logger.info("=== Testing Error Handling ===")
    
    try:
        from actor_tiktok.utils.enhanced_tiktok_auth import EnhancedTikTokAuthenticator
        
        authenticator = EnhancedTikTokAuthenticator()
        
        # Test error classification
        test_errors = [
            "Captcha verification required",
            "Rate limit exceeded", 
            "Suspicious activity detected",
            "Account temporarily locked",
            "Session expired"
        ]
        
        logger.info("✅ Error classification test:")
        for error in test_errors:
            error_type = authenticator._classify_error(error)
            logger.info(f"  - '{error[:30]}...' → {error_type}")
        
        # Test recovery strategies
        recovery_strategies = list(authenticator.recovery_strategies.keys())
        logger.info(f"✅ Recovery strategies available: {recovery_strategies}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error handling test failed: {str(e)}")
        return False

def test_rate_limiting():
    """Test rate limiting functionality"""
    logger.info("=== Testing Rate Limiting ===")
    
    try:
        from actor_tiktok.utils.enhanced_scraper import EnhancedTikTokScraper
        
        rate_config = {
            'requests_per_minute': 10,
            'requests_per_hour': 100,
            'delay_between_requests': (1, 3)
        }
        
        scraper = EnhancedTikTokScraper(rate_limit_config=rate_config)
        
        # Test rate limit check
        for i in range(3):
            rate_ok = scraper._check_rate_limit()
            logger.info(f"  Rate limit check {i+1}: {'PASS' if rate_ok else 'FAIL'}")
            
            if rate_ok:
                scraper._apply_rate_limiting()
        
        logger.info("✅ Rate limiting functionality working")
        return True
        
    except Exception as e:
        logger.error(f"❌ Rate limiting test failed: {str(e)}")
        return False

def test_celery_task_definitions():
    """Test Celery task definitions"""
    logger.info("=== Testing Celery Task Definitions ===")
    
    try:
        from actor_tiktok.tasks import enhanced_actor_login_task, enhanced_scraping_task
        
        logger.info(f"✅ Enhanced login task: {enhanced_actor_login_task.name}")
        logger.info(f"✅ Enhanced scraping task: {enhanced_scraping_task.name}")
        
        # Test task configuration
        logger.info(f"  - Login task retries: {enhanced_actor_login_task.max_retries}")
        logger.info(f"  - Scraping task retries: {enhanced_scraping_task.max_retries}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Celery task definitions test failed: {str(e)}")
        return False

def run_basic_functionality_tests():
    """Run all basic functionality tests"""
    logger.info("🚀 Starting Basic Functionality Tests")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    tests = [
        ("Browser Automation", test_browser_automation),
        ("Enhanced Components", test_enhanced_components),
        ("Configuration Loading", test_configuration_loading),
        ("Database Connectivity", test_database_connectivity),
        ("Error Handling", test_error_handling),
        ("Rate Limiting", test_rate_limiting),
        ("Celery Task Definitions", test_celery_task_definitions)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.warning(f"⚠️ {test_name} - FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - EXCEPTION: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("BASIC FUNCTIONALITY TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {(passed/total)*100:.1f}%")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    if passed == total:
        logger.info("🎉 ALL BASIC FUNCTIONALITY TESTS PASSED!")
        logger.info("The Enhanced TikTok Actor system is working correctly.")
    elif passed >= total * 0.8:
        logger.info("✅ Most functionality tests passed - system is mostly working.")
    else:
        logger.warning(f"⚠️ Only {passed}/{total} tests passed - some issues need attention.")
    
    logger.info("\n📋 Next Steps:")
    logger.info("1. For real TikTok testing, ensure you have valid credentials")
    logger.info("2. Consider using proxies for production scraping")
    logger.info("3. Test with actual TikTok pages when ready")
    logger.info("4. Monitor rate limits and adjust as needed")
    
    logger.info("🏁 Basic functionality tests completed!")
    return results

if __name__ == "__main__":
    results = run_basic_functionality_tests()
