#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from actor_tiktok.models import TikTokUserAccount
from actor_tiktok.tasks import actor_login_task
from django.utils import timezone

def test_login():
    """Test login for available accounts"""
    print("Testing TikTok login...")
    
    # Get an active, non-blocked account
    accounts = TikTokUserAccount.objects.filter(
        is_active=True,
        is_blocked=False
    ).order_by('login_attempts')
    
    if not accounts.exists():
        print("No available accounts to test login.")
        return
    
    account = accounts.first()
    print(f"Testing login for account: {account.tiktok_username}")
    print(f"Account ID: {account.id}")
    print(f"User: {account.user}")
    print(f"User ID: {account.user.id if account.user else 'None'}")
    print(f"Current login attempts: {account.login_attempts}")
    print(f"Last login: {account.last_login}")
    
    if not account.user:
        print("ERROR: Account has no associated user!")
        return
    
    # Test the login task
    try:
        print("Starting login task...")
        # Note: Using dummy password since we don't have the real password
        # In real usage, the password would come from the frontend
        result = actor_login_task.delay(
            user_id=account.user.id,
            tiktok_username=account.tiktok_username,
            tiktok_password="dummy_password_for_testing"
        )
        print(f"Login task started with ID: {result.id}")
        print("Check Celery worker logs for progress.")
        print("Note: This will fail because we're using a dummy password.")
        
        # Wait a bit and check result
        import time
        time.sleep(10)
        
        if result.ready():
            task_result = result.get()
            print(f"Login result: {task_result}")
        else:
            print("Login task is still running...")
            
    except Exception as e:
        print(f"Error starting login task: {e}")
    
    # Refresh account status
    account.refresh_from_db()
    print(f"\nUpdated account status:")
    print(f"  Login attempts: {account.login_attempts}")
    print(f"  Is blocked: {account.is_blocked}")
    print(f"  Last login: {account.last_login}")
    print(f"  Session valid: {account.is_session_valid()}")

if __name__ == '__main__':
    test_login()