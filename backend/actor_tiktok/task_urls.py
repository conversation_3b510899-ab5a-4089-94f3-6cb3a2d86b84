"""
URL patterns for Actor Task CRUD operations
"""

from django.urls import path
from . import task_crud

urlpatterns = [
    # Basic CRUD operations
    path('tasks/', task_crud.get_tasks, name='get_tasks'),  # GET: list, POST: create
    path('tasks/create/', task_crud.create_task, name='create_task'),  # POST: create
    path('tasks/<int:task_id>/', task_crud.get_task_by_id, name='get_task_by_id'),  # GET: detail
    path('tasks/<int:task_id>/update/', task_crud.update_task, name='update_task'),  # PUT/PATCH: update
    path('tasks/<int:task_id>/delete/', task_crud.delete_task, name='delete_task'),  # DELETE: delete
    
    # Bulk operations
    path('tasks/bulk-update/', task_crud.bulk_update_tasks, name='bulk_update_tasks'),  # POST: bulk update
    path('tasks/bulk-delete/', task_crud.bulk_delete_tasks, name='bulk_delete_tasks'),  # DELETE: bulk delete
    
    # Advanced operations
    path('tasks/<int:task_id>/duplicate/', task_crud.duplicate_task, name='duplicate_task'),  # POST: duplicate
    path('tasks/statistics/', task_crud.get_task_statistics, name='get_task_statistics'),  # GET: statistics
]
