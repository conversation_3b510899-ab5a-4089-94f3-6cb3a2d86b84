import django_filters
from django.db.models import Q
from .models import ActorTask, ActorS<PERSON>raped<PERSON>ata, TikTokUserAccount, TikTokSession

class ActorTaskFilter(django_filters.FilterSet):
    """Filter for ActorTask model"""
    
    # Task type filtering
    task_type = django_filters.ChoiceFilter(
        choices=ActorTask.TASK_TYPE_CHOICES,
        help_text="Filter by task type"
    )
    
    # Status filtering
    status = django_filters.ChoiceFilter(
        choices=ActorTask.STATUS_CHOICES,
        help_text="Filter by task status"
    )
    
    # Date range filtering
    created_after = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        help_text="Filter tasks created after this date"
    )
    
    created_before = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        help_text="Filter tasks created before this date"
    )
    
    completed_after = django_filters.DateTimeFilter(
        field_name='completed_at',
        lookup_expr='gte',
        help_text="Filter tasks completed after this date"
    )
    
    completed_before = django_filters.DateTimeFilter(
        field_name='completed_at',
        lookup_expr='lte',
        help_text="Filter tasks completed before this date"
    )
    
    # TikTok account filtering
    tiktok_account = django_filters.ModelChoiceFilter(
        queryset=TikTokUserAccount.objects.all(),
        help_text="Filter by TikTok account"
    )
    
    tiktok_username = django_filters.CharFilter(
        field_name='tiktok_account__tiktok_username',
        lookup_expr='icontains',
        help_text="Filter by TikTok username (partial match)"
    )
    
    # Items scraped range
    min_items_scraped = django_filters.NumberFilter(
        field_name='items_scraped',
        lookup_expr='gte',
        help_text="Filter tasks with at least this many items scraped"
    )
    
    max_items_scraped = django_filters.NumberFilter(
        field_name='items_scraped',
        lookup_expr='lte',
        help_text="Filter tasks with at most this many items scraped"
    )
    
    # Search in task parameters
    search_params = django_filters.CharFilter(
        method='filter_search_params',
        help_text="Search in task parameters (JSON field)"
    )
    
    # Error filtering
    has_error = django_filters.BooleanFilter(
        method='filter_has_error',
        help_text="Filter tasks that have errors"
    )
    
    # Active tasks (pending or running)
    is_active = django_filters.BooleanFilter(
        method='filter_is_active',
        help_text="Filter active (pending or running) tasks"
    )
    
    def filter_search_params(self, queryset, name, value):
        """Filter by searching in task parameters JSON field"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(task_parameters__icontains=value) |
            Q(description__icontains=value)
        )
    
    def filter_has_error(self, queryset, name, value):
        """Filter tasks that have errors"""
        if value is True:
            return queryset.exclude(error_message__isnull=True).exclude(error_message='')
        elif value is False:
            return queryset.filter(Q(error_message__isnull=True) | Q(error_message=''))
        return queryset
    
    def filter_is_active(self, queryset, name, value):
        """Filter active tasks (pending or running)"""
        if value is True:
            return queryset.filter(status__in=['PENDING', 'RUNNING'])
        elif value is False:
            return queryset.exclude(status__in=['PENDING', 'RUNNING'])
        return queryset
    
    class Meta:
        model = ActorTask
        fields = {}

class ActorScrapedDataFilter(django_filters.FilterSet):
    """Filter for ActorScrapedData model"""
    
    # Task filtering
    task = django_filters.ModelChoiceFilter(
        queryset=ActorTask.objects.all(),
        help_text="Filter by specific task"
    )
    
    task_type = django_filters.ChoiceFilter(
        field_name='task__task_type',
        choices=ActorTask.TASK_TYPE_CHOICES,
        help_text="Filter by task type"
    )
    
    task_status = django_filters.ChoiceFilter(
        field_name='task__status',
        choices=ActorTask.STATUS_CHOICES,
        help_text="Filter by task status"
    )
    
    # Date range filtering
    scraped_after = django_filters.DateTimeFilter(
        field_name='scraped_at',
        lookup_expr='gte',
        help_text="Filter data scraped after this date"
    )
    
    scraped_before = django_filters.DateTimeFilter(
        field_name='scraped_at',
        lookup_expr='lte',
        help_text="Filter data scraped before this date"
    )
    
    # TikTok account filtering
    tiktok_account = django_filters.ModelChoiceFilter(
        field_name='task__tiktok_account',
        queryset=TikTokUserAccount.objects.all(),
        help_text="Filter by TikTok account"
    )
    
    tiktok_username = django_filters.CharFilter(
        field_name='task__tiktok_account__tiktok_username',
        lookup_expr='icontains',
        help_text="Filter by TikTok username (partial match)"
    )
    
    # Content type filtering
    content_type = django_filters.CharFilter(
        method='filter_content_type',
        help_text="Filter by content type (video, user, comment, etc.)"
    )
    
    # Search in scraped data
    search_content = django_filters.CharFilter(
        method='filter_search_content',
        help_text="Search in scraped data content"
    )
    
    # Data size filtering
    min_data_size = django_filters.NumberFilter(
        method='filter_min_data_size',
        help_text="Filter by minimum data size (in characters)"
    )
    
    max_data_size = django_filters.NumberFilter(
        method='filter_max_data_size',
        help_text="Filter by maximum data size (in characters)"
    )
    
    # Enhanced metadata filtering
    has_enhanced_metadata = django_filters.BooleanFilter(
        method='filter_has_enhanced_metadata',
        help_text="Filter data that has enhanced metadata"
    )
    
    def filter_content_type(self, queryset, name, value):
        """Filter by content type in scraped data"""
        if not value:
            return queryset
        
        return queryset.filter(scraped_data__icontains=f'"type":"{value}"')
    
    def filter_search_content(self, queryset, name, value):
        """Search in scraped data content"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(scraped_data__icontains=value) |
            Q(task__description__icontains=value)
        )
    
    def filter_min_data_size(self, queryset, name, value):
        """Filter by minimum data size"""
        if value is None:
            return queryset
        
        from django.db.models import Length
        return queryset.annotate(
            data_length=Length('scraped_data')
        ).filter(data_length__gte=value)
    
    def filter_max_data_size(self, queryset, name, value):
        """Filter by maximum data size"""
        if value is None:
            return queryset
        
        from django.db.models import Length
        return queryset.annotate(
            data_length=Length('scraped_data')
        ).filter(data_length__lte=value)
    
    def filter_has_enhanced_metadata(self, queryset, name, value):
        """Filter data that has enhanced metadata"""
        if value is True:
            return queryset.filter(
                scraped_data__icontains='"enhanced_metadata"'
            )
        elif value is False:
            return queryset.exclude(
                scraped_data__icontains='"enhanced_metadata"'
            )
        return queryset
    
    class Meta:
        model = ActorScrapedData
        fields = []

class TikTokUserAccountFilter(django_filters.FilterSet):
    """Filter for TikTokUserAccount model"""
    
    # Account status filtering
    is_active = django_filters.BooleanFilter(
        help_text="Filter by active status"
    )
    
    is_blocked = django_filters.BooleanFilter(
        help_text="Filter by blocked status"
    )
    
    # Username filtering
    tiktok_username = django_filters.CharFilter(
        lookup_expr='icontains',
        help_text="Filter by TikTok username (partial match)"
    )
    
    # Date filtering
    created_after = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        help_text="Filter accounts created after this date"
    )
    
    created_before = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        help_text="Filter accounts created before this date"
    )
    
    # Session validity
    session_valid = django_filters.BooleanFilter(
        method='filter_session_valid',
        help_text="Filter by session validity"
    )
    
    # Login attempts filtering
    max_login_attempts = django_filters.NumberFilter(
        field_name='login_attempts',
        lookup_expr='lte',
        help_text="Filter accounts with login attempts less than or equal to this value"
    )
    
    def filter_session_valid(self, queryset, name, value):
        """Filter by session validity"""
        from django.utils import timezone
        
        if value is True:
            return queryset.filter(
                is_active=True,
                session_expires_at__gt=timezone.now()
            )
        elif value is False:
            return queryset.filter(
                Q(is_active=False) |
                Q(session_expires_at__lte=timezone.now())
            )
        return queryset
    
    class Meta:
        model = TikTokUserAccount
        fields = {
            'login_attempts': ['exact', 'gte', 'lte'],
        }

class TikTokSessionFilter(django_filters.FilterSet):
    """Filter for TikTokSession model"""
    
    # Account filtering
    tiktok_account = django_filters.ModelChoiceFilter(
        queryset=TikTokUserAccount.objects.all(),
        help_text="Filter by TikTok account"
    )
    
    # Health status filtering
    is_healthy = django_filters.BooleanFilter(
        help_text="Filter by session health status"
    )
    
    # Date filtering
    created_after = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte',
        help_text="Filter sessions created after this date"
    )
    
    created_before = django_filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte',
        help_text="Filter sessions created before this date"
    )
    
    # Anti-bot metrics filtering
    min_captcha_count = django_filters.NumberFilter(
        field_name='captcha_challenges',
        lookup_expr='gte',
        help_text="Filter sessions with at least this many captcha challenges"
    )
    
    max_captcha_count = django_filters.NumberFilter(
        field_name='captcha_challenges',
        lookup_expr='lte',
        help_text="Filter sessions with at most this many captcha challenges"
    )
    
    min_rate_limit_hits = django_filters.NumberFilter(
        field_name='rate_limit_hits',
        lookup_expr='gte',
        help_text="Filter sessions with at least this many rate limit hits"
    )
    
    max_rate_limit_hits = django_filters.NumberFilter(
        field_name='rate_limit_hits',
        lookup_expr='lte',
        help_text="Filter sessions with at most this many rate limit hits"
    )
    
    class Meta:
        model = TikTokSession
        fields = {
            'captcha_challenges': ['exact', 'gte', 'lte'],
            'rate_limit_hits': ['exact', 'gte', 'lte'],
        }