# Generated by Django 5.2.4 on 2025-07-15 21:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('actor_tiktok', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='tiktokuseraccount',
            name='email',
            field=models.EmailField(blank=True, help_text='Email for TikTok account', max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='tiktokuseraccount',
            name='password',
            field=models.CharField(default='', help_text='Encrypted password for TikTok account', max_length=255),
        ),
    ]
