"""
CRUD Operations for Actor Tasks

Complete Create, Read, Update, Delete operations for TikTok Actor Tasks
"""

from django.shortcuts import get_object_or_404
from django.contrib.auth.models import User
from django.db.models import Q, Count, Avg
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
import logging

from .models import ActorTask, TikTokUserAccount

logger = logging.getLogger(__name__)

# ================================
# CREATE OPERATIONS
# ================================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_task(request):
    """
    Create a new actor task
    
    POST /api/actor/tasks/
    Body: {
        "task_name": "My Task",
        "task_type": "CONTENT_SEARCH",
        "tiktok_account_id": 1,
        "target_identifier": "@username",
        "keywords": "viral, trending",
        "max_items": 50,
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "use_stealth_mode": true,
        "randomize_delays": true,
        "scrape_interval": 60
    }
    """
    try:
        # Extract data from request
        data = request.data
        task_name = data.get('task_name')
        task_type = data.get('task_type')
        tiktok_account_id = data.get('tiktok_account_id')
        
        # Validate required fields
        if not task_name or not task_type:
            return Response({
                'success': False,
                'error': 'task_name and task_type are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get or create TikTok account
        tiktok_account = None
        if tiktok_account_id:
            try:
                tiktok_account = TikTokUserAccount.objects.get(
                    id=tiktok_account_id,
                    user=request.user
                )
            except TikTokUserAccount.DoesNotExist:
                pass
        
        # Create default account if none provided
        if not tiktok_account:
            tiktok_account, created = TikTokUserAccount.objects.get_or_create(
                user=request.user,
                tiktok_username='default_user',
                defaults={
                    'password': '',
                    'is_active': True
                }
            )
        
        # Parse dates
        start_date = None
        end_date = None
        if data.get('start_date'):
            start_date = datetime.fromisoformat(data['start_date']).date()
        if data.get('end_date'):
            end_date = datetime.fromisoformat(data['end_date']).date()
        
        # Create task
        task = ActorTask.objects.create(
            user=request.user,
            tiktok_account=tiktok_account,
            task_name=task_name,
            task_type=task_type,
            target_identifier=data.get('target_identifier'),
            keywords=data.get('keywords'),
            max_items=data.get('max_items', 100),
            scrape_interval=data.get('scrape_interval', 60),
            use_stealth_mode=data.get('use_stealth_mode', True),
            randomize_delays=data.get('randomize_delays', True),
            start_date=start_date,
            end_date=end_date,
            task_parameters=data.get('task_parameters', {})
        )
        
        logger.info(f"✅ Created task {task.id}: {task_name} by user {request.user.username}")
        
        return Response({
            'success': True,
            'message': 'Task created successfully',
            'task': serialize_task(task)
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        logger.error(f"❌ Error creating task: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ================================
# READ OPERATIONS
# ================================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_tasks(request):
    """
    Get all tasks for the authenticated user
    
    GET /api/actor/tasks/
    Query params:
    - status: filter by status (PENDING, RUNNING, COMPLETED, etc.)
    - task_type: filter by task type
    - limit: number of results (default: 50)
    - offset: pagination offset (default: 0)
    - search: search in task names and keywords
    """
    try:
        # Get query parameters
        task_status = request.GET.get('status')
        task_type = request.GET.get('task_type')
        limit = int(request.GET.get('limit', 50))
        offset = int(request.GET.get('offset', 0))
        search = request.GET.get('search')
        
        # Build query
        queryset = ActorTask.objects.filter(user=request.user)
        
        # Apply filters
        if task_status:
            queryset = queryset.filter(status=task_status)
        if task_type:
            queryset = queryset.filter(task_type=task_type)
        if search:
            queryset = queryset.filter(
                Q(task_name__icontains=search) |
                Q(keywords__icontains=search) |
                Q(target_identifier__icontains=search)
            )
        
        # Get total count
        total_count = queryset.count()
        
        # Apply pagination
        tasks = queryset[offset:offset + limit]
        
        # Serialize tasks
        serialized_tasks = [serialize_task(task) for task in tasks]
        
        return Response({
            'success': True,
            'results': serialized_tasks,
            'total_count': total_count,
            'limit': limit,
            'offset': offset
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting tasks: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_task_by_id(request, task_id):
    """
    Get a specific task by ID
    
    GET /api/actor/tasks/{task_id}/
    """
    try:
        task = get_object_or_404(ActorTask, id=task_id, user=request.user)
        
        return Response({
            'success': True,
            'task': serialize_task(task, detailed=True)
        })
        
    except Exception as e:
        logger.error(f"❌ Error getting task {task_id}: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ================================
# UPDATE OPERATIONS
# ================================

@api_view(['PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def update_task(request, task_id):
    """
    Update a task
    
    PUT/PATCH /api/actor/tasks/{task_id}/
    Body: {
        "task_name": "Updated Task Name",
        "status": "RUNNING",
        "max_items": 200,
        "keywords": "new, keywords",
        ...
    }
    """
    try:
        task = get_object_or_404(ActorTask, id=task_id, user=request.user)
        data = request.data
        
        # Update allowed fields
        updatable_fields = [
            'task_name', 'target_identifier', 'keywords', 'max_items',
            'scrape_interval', 'use_stealth_mode', 'randomize_delays',
            'task_parameters', 'error_message'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(task, field, data[field])
        
        # Handle status updates with validation
        if 'status' in data:
            new_status = data['status']
            if new_status in dict(ActorTask.STATUS_CHOICES):
                task.status = new_status
                
                # Update timestamps based on status
                if new_status == 'RUNNING' and not task.started_at:
                    task.started_at = timezone.now()
                elif new_status in ['COMPLETED', 'FAILED', 'CANCELLED']:
                    task.completed_at = timezone.now()
        
        # Handle date updates
        if 'start_date' in data and data['start_date']:
            task.start_date = datetime.fromisoformat(data['start_date']).date()
        if 'end_date' in data and data['end_date']:
            task.end_date = datetime.fromisoformat(data['end_date']).date()
        
        # Handle progress updates
        if 'progress' in data:
            task.progress = max(0, min(100, int(data['progress'])))
        if 'items_scraped' in data:
            task.items_scraped = int(data['items_scraped'])
        if 'total_items_found' in data:
            task.total_items_found = int(data['total_items_found'])
        
        task.save()
        
        logger.info(f"✅ Updated task {task_id} by user {request.user.username}")
        
        return Response({
            'success': True,
            'message': 'Task updated successfully',
            'task': serialize_task(task)
        })
        
    except Exception as e:
        logger.error(f"❌ Error updating task {task_id}: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ================================
# DELETE OPERATIONS
# ================================

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_task(request, task_id):
    """
    Delete a task
    
    DELETE /api/actor/tasks/{task_id}/
    """
    try:
        task = get_object_or_404(ActorTask, id=task_id, user=request.user)
        
        # Store task info for logging
        task_name = task.task_name
        task_type = task.task_type
        
        # Delete the task
        task.delete()
        
        logger.info(f"✅ Deleted task {task_id} ({task_name}) by user {request.user.username}")
        
        return Response({
            'success': True,
            'message': f'Task "{task_name}" deleted successfully'
        })
        
    except Exception as e:
        logger.error(f"❌ Error deleting task {task_id}: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ================================
# UTILITY FUNCTIONS
# ================================

def serialize_task(task, detailed=False):
    """Serialize a task object to dictionary"""
    base_data = {
        'id': task.id,
        'task_name': task.task_name,
        'task_type': task.task_type,
        'status': task.status,
        'target_identifier': task.target_identifier,
        'keywords': task.keywords,
        'max_items': task.max_items,
        'progress': task.progress,
        'items_scraped': task.items_scraped,
        'total_items_found': task.total_items_found,
        'progress_percentage': task.progress_percentage,
        'created_at': task.created_at.isoformat() if task.created_at else None,
        'updated_at': task.updated_at.isoformat() if task.updated_at else None,
    }
    
    if detailed:
        base_data.update({
            'tiktok_account_id': task.tiktok_account.id,
            'tiktok_account_username': task.tiktok_account.tiktok_username,
            'scrape_interval': task.scrape_interval,
            'use_stealth_mode': task.use_stealth_mode,
            'randomize_delays': task.randomize_delays,
            'start_date': task.start_date.isoformat() if task.start_date else None,
            'end_date': task.end_date.isoformat() if task.end_date else None,
            'task_parameters': task.task_parameters,
            'celery_task_id': task.celery_task_id,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'error_message': task.error_message,
        })
    
    return base_data

# ================================
# BULK OPERATIONS
# ================================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def bulk_update_tasks(request):
    """
    Bulk update multiple tasks

    POST /api/actor/tasks/bulk-update/
    Body: {
        "task_ids": [1, 2, 3],
        "updates": {
            "status": "CANCELLED",
            "max_items": 200
        }
    }
    """
    try:
        data = request.data
        task_ids = data.get('task_ids', [])
        updates = data.get('updates', {})

        if not task_ids or not updates:
            return Response({
                'success': False,
                'error': 'task_ids and updates are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get tasks owned by user
        tasks = ActorTask.objects.filter(
            id__in=task_ids,
            user=request.user
        )

        updated_count = 0
        for task in tasks:
            # Apply updates
            for field, value in updates.items():
                if hasattr(task, field):
                    setattr(task, field, value)

            # Handle status-specific updates
            if 'status' in updates:
                new_status = updates['status']
                if new_status == 'RUNNING' and not task.started_at:
                    task.started_at = timezone.now()
                elif new_status in ['COMPLETED', 'FAILED', 'CANCELLED']:
                    task.completed_at = timezone.now()

            task.save()
            updated_count += 1

        logger.info(f"✅ Bulk updated {updated_count} tasks by user {request.user.username}")

        return Response({
            'success': True,
            'message': f'Successfully updated {updated_count} tasks',
            'updated_count': updated_count
        })

    except Exception as e:
        logger.error(f"❌ Error in bulk update: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def bulk_delete_tasks(request):
    """
    Bulk delete multiple tasks

    DELETE /api/actor/tasks/bulk-delete/
    Body: {
        "task_ids": [1, 2, 3]
    }
    """
    try:
        data = request.data
        task_ids = data.get('task_ids', [])

        if not task_ids:
            return Response({
                'success': False,
                'error': 'task_ids are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Delete tasks owned by user
        deleted_count, _ = ActorTask.objects.filter(
            id__in=task_ids,
            user=request.user
        ).delete()

        logger.info(f"✅ Bulk deleted {deleted_count} tasks by user {request.user.username}")

        return Response({
            'success': True,
            'message': f'Successfully deleted {deleted_count} tasks',
            'deleted_count': deleted_count
        })

    except Exception as e:
        logger.error(f"❌ Error in bulk delete: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ================================
# ADVANCED OPERATIONS
# ================================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def duplicate_task(request, task_id):
    """
    Duplicate an existing task

    POST /api/actor/tasks/{task_id}/duplicate/
    Body: {
        "task_name": "Copy of Original Task" (optional)
    }
    """
    try:
        original_task = get_object_or_404(ActorTask, id=task_id, user=request.user)

        # Create new task name
        new_name = request.data.get('task_name', f"Copy of {original_task.task_name}")

        # Duplicate the task
        new_task = ActorTask.objects.create(
            user=original_task.user,
            tiktok_account=original_task.tiktok_account,
            task_name=new_name,
            task_type=original_task.task_type,
            target_identifier=original_task.target_identifier,
            keywords=original_task.keywords,
            max_items=original_task.max_items,
            scrape_interval=original_task.scrape_interval,
            use_stealth_mode=original_task.use_stealth_mode,
            randomize_delays=original_task.randomize_delays,
            start_date=original_task.start_date,
            end_date=original_task.end_date,
            task_parameters=original_task.task_parameters.copy(),
            status='PENDING'  # Always start as pending
        )

        logger.info(f"✅ Duplicated task {task_id} to {new_task.id} by user {request.user.username}")

        return Response({
            'success': True,
            'message': 'Task duplicated successfully',
            'original_task_id': task_id,
            'new_task': serialize_task(new_task)
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"❌ Error duplicating task {task_id}: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_task_statistics(request):
    """
    Get comprehensive task statistics

    GET /api/actor/tasks/statistics/
    """
    try:
        user_tasks = ActorTask.objects.filter(user=request.user)

        # Basic counts
        total_tasks = user_tasks.count()
        status_counts = {}
        for status_code, status_name in ActorTask.STATUS_CHOICES:
            status_counts[status_code] = user_tasks.filter(status=status_code).count()

        # Type counts
        type_counts = {}
        for type_code, type_name in ActorTask.TASK_TYPE_CHOICES:
            type_counts[type_code] = user_tasks.filter(task_type=type_code).count()

        # Recent activity (last 7 days)
        week_ago = timezone.now() - timedelta(days=7)
        recent_tasks = user_tasks.filter(created_at__gte=week_ago).count()

        # Completion rate
        completed_tasks = status_counts.get('COMPLETED', 0)
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

        # Average items scraped
        avg_items = user_tasks.aggregate(Avg('items_scraped'))['items_scraped__avg'] or 0

        return Response({
            'success': True,
            'statistics': {
                'total_tasks': total_tasks,
                'status_counts': status_counts,
                'type_counts': type_counts,
                'recent_tasks_7_days': recent_tasks,
                'completion_rate': round(completion_rate, 2),
                'average_items_scraped': round(avg_items, 2),
                'most_used_task_type': max(type_counts, key=type_counts.get) if type_counts else None
            }
        })

    except Exception as e:
        logger.error(f"❌ Error getting task statistics: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
