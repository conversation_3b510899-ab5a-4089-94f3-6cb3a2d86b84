"""
Enhanced configuration for TikTok actor with advanced anti-detection and scraping settings
"""

from datetime import timedelta

# Enhanced Anti-Detection Configuration
ENHANCED_ANTI_DETECTION = {
    'use_undetected_chrome': True,
    'rotate_user_agents': True,
    'randomize_viewports': True,
    'simulate_human_behavior': True,
    'fingerprint_randomization': True,
    'canvas_fingerprint_protection': True,
    'webgl_fingerprint_protection': True,
    'geolocation_randomization': True,
    'timezone_randomization': True,
    'language_randomization': True,
    'stealth_mode_enabled': True,
    'behavioral_delays': {
        'min_delay': 1.0,
        'max_delay': 5.0,
        'typing_delay_range': (0.05, 0.15),
        'click_delay_range': (0.1, 0.3),
        'scroll_delay_range': (0.5, 2.0)
    }
}

# Proxy Configuration
PROXY_CONFIG = {
    'enabled': False,  # Set to True to enable proxy support
    'proxies': [
        # Example proxy configurations
        # {
        #     'host': '127.0.0.1',
        #     'port': 8080,
        #     'username': 'user',
        #     'password': 'pass',
        #     'protocol': 'http',
        #     'country': 'US',
        #     'city': 'New York'
        # }
    ],
    'rotation_strategy': 'round_robin',  # 'round_robin', 'random', 'best_performance'
    'max_failures': 3,
    'health_check_interval': 300,  # 5 minutes
    'timeout': 10,
    'retry_failed_proxies': True,
    'retry_interval': 1800  # 30 minutes
}

# Enhanced Session Management
ENHANCED_SESSION_CONFIG = {
    'session_timeout': 3600,  # 1 hour
    'max_session_age': 86400 * 7,  # 7 days
    'auto_backup_enabled': True,
    'backup_interval': 3600,  # 1 hour
    'backup_retention_days': 30,
    'session_rotation_enabled': True,
    'rotation_triggers': {
        'low_quality_score': 0.4,
        'high_failure_rate': 0.3,
        'session_age_hours': 24,
        'suspicious_activity_detected': True
    },
    'health_monitoring': {
        'enabled': True,
        'check_interval': 1800,  # 30 minutes
        'metrics_retention_days': 7
    }
}

# Rate Limiting Configuration
RATE_LIMITING_CONFIG = {
    'requests_per_minute': 30,
    'requests_per_hour': 500,
    'requests_per_day': 5000,
    'delay_between_requests': (2, 5),  # seconds
    'delay_between_profiles': (10, 20),  # seconds
    'delay_between_videos': (5, 10),  # seconds
    'adaptive_delays': True,
    'burst_protection': {
        'enabled': True,
        'max_burst_requests': 5,
        'burst_window_seconds': 30,
        'burst_cooldown_seconds': 60
    }
}

# Scraping Configuration
SCRAPING_CONFIG = {
    'max_videos_per_profile': 100,
    'max_comments_per_video': 200,
    'max_search_results': 500,
    'max_trending_videos': 200,
    'content_filters': {
        'min_video_duration': 5,  # seconds
        'max_video_duration': 600,  # 10 minutes
        'exclude_private_accounts': True,
        'exclude_deleted_content': True
    },
    'data_extraction': {
        'extract_video_metadata': True,
        'extract_user_metadata': True,
        'extract_engagement_metrics': True,
        'extract_hashtags': True,
        'extract_mentions': True,
        'extract_music_info': True,
        'extract_effects_info': True
    },
    'quality_checks': {
        'validate_data_completeness': True,
        'check_duplicate_content': True,
        'verify_data_integrity': True
    }
}

# Error Handling and Recovery
ERROR_HANDLING_CONFIG = {
    'max_retries': 3,
    'retry_delays': {
        'captcha': 60,  # 1 minute
        'rate_limit': 300,  # 5 minutes
        'suspicious_activity': 1800,  # 30 minutes
        'network_error': 30,  # 30 seconds
        'server_error': 120,  # 2 minutes
        'unknown_error': 180  # 3 minutes
    },
    'recovery_strategies': {
        'captcha_recovery_enabled': True,
        'proxy_rotation_on_block': True,
        'session_rotation_on_failure': True,
        'user_agent_rotation_on_detection': True,
        'fallback_to_mobile_view': True
    },
    'monitoring': {
        'log_all_errors': True,
        'alert_on_critical_errors': True,
        'track_error_patterns': True,
        'generate_error_reports': True
    }
}

# Account Health Monitoring
ACCOUNT_HEALTH_CONFIG = {
    'monitoring_enabled': True,
    'health_check_interval': 3600,  # 1 hour
    'metrics': {
        'login_success_rate': {
            'weight': 0.3,
            'threshold_warning': 0.7,
            'threshold_critical': 0.5
        },
        'scraping_success_rate': {
            'weight': 0.25,
            'threshold_warning': 0.8,
            'threshold_critical': 0.6
        },
        'captcha_encounter_rate': {
            'weight': 0.2,
            'threshold_warning': 0.1,
            'threshold_critical': 0.2
        },
        'rate_limit_frequency': {
            'weight': 0.15,
            'threshold_warning': 0.05,
            'threshold_critical': 0.1
        },
        'session_stability': {
            'weight': 0.1,
            'threshold_warning': 0.8,
            'threshold_critical': 0.6
        }
    },
    'actions': {
        'warning_threshold': 0.7,
        'critical_threshold': 0.5,
        'auto_rotate_on_critical': True,
        'auto_pause_on_critical': True,
        'notification_enabled': True
    }
}

# Performance Optimization
PERFORMANCE_CONFIG = {
    'browser_optimization': {
        'disable_images': False,  # Set to True for faster loading
        'disable_css': False,
        'disable_javascript': False,
        'disable_plugins': True,
        'disable_extensions': True,
        'enable_gpu_acceleration': False
    },
    'memory_management': {
        'max_browser_instances': 3,
        'browser_restart_interval': 7200,  # 2 hours
        'memory_threshold_mb': 1024,
        'garbage_collection_interval': 300  # 5 minutes
    },
    'caching': {
        'cache_session_data': True,
        'cache_user_profiles': True,
        'cache_video_metadata': True,
        'cache_ttl_seconds': 3600,  # 1 hour
        'max_cache_size_mb': 100
    }
}

# Security Configuration
SECURITY_CONFIG = {
    'encryption': {
        'encrypt_session_data': True,
        'encrypt_passwords': True,
        'encryption_algorithm': 'AES-256',
        'key_rotation_enabled': True,
        'key_rotation_interval_days': 30
    },
    'data_protection': {
        'anonymize_user_data': True,
        'hash_sensitive_fields': True,
        'secure_data_transmission': True,
        'data_retention_days': 90
    },
    'access_control': {
        'require_authentication': True,
        'session_timeout_minutes': 60,
        'max_concurrent_sessions': 5,
        'ip_whitelist_enabled': False
    }
}

# Monitoring and Logging
MONITORING_CONFIG = {
    'logging': {
        'log_level': 'INFO',
        'log_to_file': True,
        'log_file_rotation': True,
        'max_log_file_size_mb': 100,
        'log_retention_days': 30,
        'structured_logging': True
    },
    'metrics': {
        'collect_performance_metrics': True,
        'collect_error_metrics': True,
        'collect_usage_metrics': True,
        'metrics_retention_days': 30,
        'export_metrics': True
    },
    'alerts': {
        'email_alerts_enabled': False,
        'webhook_alerts_enabled': False,
        'alert_thresholds': {
            'error_rate': 0.1,
            'response_time_ms': 5000,
            'memory_usage_percent': 80,
            'disk_usage_percent': 90
        }
    }
}

# Feature Flags
FEATURE_FLAGS = {
    'enhanced_authentication': True,
    'proxy_support': True,
    'advanced_anti_detection': True,
    'session_backup_restore': True,
    'account_health_monitoring': True,
    'adaptive_rate_limiting': True,
    'error_recovery': True,
    'performance_optimization': True,
    'security_enhancements': True,
    'comprehensive_logging': True
}

# Default configurations for different use cases
USE_CASE_CONFIGS = {
    'stealth_mode': {
        'anti_detection': {**ENHANCED_ANTI_DETECTION, 'stealth_mode_enabled': True},
        'rate_limiting': {**RATE_LIMITING_CONFIG, 'requests_per_minute': 15},
        'proxy': {**PROXY_CONFIG, 'enabled': True}
    },
    'high_volume': {
        'rate_limiting': {**RATE_LIMITING_CONFIG, 'requests_per_minute': 60},
        'performance': {**PERFORMANCE_CONFIG, 'max_browser_instances': 5},
        'proxy': {**PROXY_CONFIG, 'enabled': True}
    },
    'research_mode': {
        'scraping': {**SCRAPING_CONFIG, 'max_videos_per_profile': 500},
        'data_extraction': {**SCRAPING_CONFIG['data_extraction'], 'extract_video_metadata': True},
        'quality_checks': {**SCRAPING_CONFIG['quality_checks'], 'validate_data_completeness': True}
    }
}
