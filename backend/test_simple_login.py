#!/usr/bin/env python3
"""
Simple TikTok Login Test

Test the simplified TikTok login approach with real credentials.
"""

import os
import sys
import django
import logging
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_login():
    """Test the simple TikTok login"""
    logger.info("=== Simple TikTok Login Test ===")
    
    try:
        from actor_tiktok.utils.simple_tiktok_auth import SimpleTikTokAuthenticator
        
        # Initialize simple authenticator
        authenticator = SimpleTikTokAuthenticator()
        
        # Test credentials
        username = "grafisone"
        password = "Puyol@102410"
        
        logger.info(f"Testing simple login for: {username}")
        logger.info("This is a focused approach without complex strategies")
        logger.info("")
        
        # Attempt login
        start_time = datetime.now()
        login_result = authenticator.login(username, password)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        logger.info(f"Login attempt completed in {duration:.1f} seconds")
        logger.info("")
        
        if login_result['success']:
            logger.info("🎉 LOGIN SUCCESSFUL!")
            logger.info("✅ Successfully logged into TikTok")
            
            session_info = login_result.get('session_info', {})
            if session_info:
                cookies = session_info.get('cookies', [])
                user_agent = session_info.get('user_agent', 'N/A')
                current_url = session_info.get('current_url', 'N/A')
                
                logger.info(f"✅ Session cookies: {len(cookies)} cookies obtained")
                logger.info(f"✅ User agent: {user_agent[:50]}...")
                logger.info(f"✅ Current URL: {current_url}")
            
            logger.info("")
            logger.info("🎯 Next Steps:")
            logger.info("1. You can now use this session for TikTok operations")
            logger.info("2. Search for 'prabowo' content")
            logger.info("3. Extract video data and engagement metrics")
            logger.info("4. The session is ready for production use")
            
            return True
            
        else:
            error_msg = login_result.get('error', 'Unknown error')
            current_url = login_result.get('current_url', 'N/A')
            
            logger.warning("❌ LOGIN FAILED")
            logger.warning(f"Error: {error_msg}")
            logger.info(f"Current URL: {current_url}")
            logger.info("")
            
            # Provide specific guidance based on error
            if 'maximum number of attempts' in error_msg.lower():
                logger.info("🔍 Analysis: TikTok Rate Limiting")
                logger.info("This means TikTok has detected multiple login attempts")
                logger.info("Solutions:")
                logger.info("  1. Wait 30-60 minutes before trying again")
                logger.info("  2. Try from a different IP address")
                logger.info("  3. Use a different browser/device")
                logger.info("  4. Clear browser data completely")
                
            elif 'incorrect' in error_msg.lower():
                logger.info("🔍 Analysis: Credential Issue")
                logger.info("Please verify:")
                logger.info("  1. Username/email is correct")
                logger.info("  2. Password is correct")
                logger.info("  3. Account is not locked or suspended")
                
            elif 'captcha' in error_msg.lower():
                logger.info("🔍 Analysis: Captcha Required")
                logger.info("TikTok requires human verification")
                logger.info("This typically happens with automated access")
                
            else:
                logger.info("🔍 Analysis: Unknown Error")
                logger.info("This may require manual investigation")
                logger.info("Check the browser window for visual clues")
            
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def run_simple_login_test():
    """Run the simple login test"""
    logger.info("🎯 Simple TikTok Login Test")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("="*50)
    
    logger.info("🎬 This test will:")
    logger.info("1. Use a simple, focused login approach")
    logger.info("2. Attempt to login with real credentials")
    logger.info("3. Provide clear feedback on results")
    logger.info("4. Give specific guidance for any issues")
    logger.info("")
    
    # Run the test
    result = test_simple_login()
    
    # Summary
    logger.info("="*50)
    logger.info("SIMPLE LOGIN TEST SUMMARY")
    logger.info("="*50)
    
    if result:
        logger.info("🎉 SUCCESS!")
        logger.info("The simple login approach worked!")
        logger.info("")
        logger.info("✅ Key Achievements:")
        logger.info("  - Successfully connected to TikTok")
        logger.info("  - Login form found and filled")
        logger.info("  - Session established")
        logger.info("  - Ready for content scraping")
        logger.info("")
        logger.info("🚀 Ready for Production:")
        logger.info("  - Use this session for 'prabowo' searches")
        logger.info("  - Extract video data and metrics")
        logger.info("  - Scale up with multiple accounts")
        
    else:
        logger.info("⚠️ LOGIN ISSUES ENCOUNTERED")
        logger.info("This is common with TikTok's protection measures")
        logger.info("")
        logger.info("🔧 Recommended Actions:")
        logger.info("  1. Wait before retrying (30-60 minutes)")
        logger.info("  2. Try from different network/IP")
        logger.info("  3. Verify credentials manually")
        logger.info("  4. Consider using proxies")
        logger.info("")
        logger.info("💡 Remember:")
        logger.info("  - TikTok has strong bot protection")
        logger.info("  - Multiple attempts can trigger rate limiting")
        logger.info("  - Success often requires patience and timing")
    
    logger.info("")
    logger.info("🏁 Simple login test completed!")
    
    return result

if __name__ == "__main__":
    run_simple_login_test()
