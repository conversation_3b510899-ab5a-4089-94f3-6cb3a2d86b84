#!/usr/bin/env python3

"""
Test script for Actor System with real TikTok account
Tests account creation and feed scraping functionality
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django
sys.path.append('/Users/<USER>/Documents/fullstax/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken

class ActorSystemTester:
    def __init__(self):
        self.base_url = 'http://localhost:8000/api'
        self.token = None
        self.user = None
        self.account_id = None
        
        print("🎭 Actor System Test Suite")
        print("=" * 50)
        print(f"🔗 Backend API: {self.base_url}")
        print(f"📅 Started: {datetime.now().isoformat()}")
        print()

    def setup_test_user(self):
        """Create or get test user and authentication token"""
        try:
            print("👤 Setting up test user...")
            
            # Create or get test user
            username = 'test_actor_user'
            email = '<EMAIL>'
            password = 'testpass123'
            
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': 'Test',
                    'last_name': 'Actor'
                }
            )
            
            if created:
                user.set_password(password)
                user.save()
                print(f"✅ Created new test user: {username}")
            else:
                print(f"✅ Using existing test user: {username}")
            
            # Create JWT token
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)

            self.user = user
            self.token = access_token
            
            print(f"🔑 Auth token: {self.token[:20]}...")
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup test user: {e}")
            return False

    def make_request(self, method, endpoint, data=None, params=None):
        """Make authenticated API request"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f'JWT {self.token}',
            'Content-Type': 'application/json'
        }
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=30)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data, timeout=30)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers, timeout=30)
            else:
                raise ValueError(f"Unsupported method: {method}")

            print(f"📊 {method} {endpoint} -> {response.status_code}")
            print(f"🔍 Response object: {response}")
            print(f"📄 Response text: {response.text}")
            return response

        except Exception as e:
            print(f"❌ Request failed: {e}")
            return None

    def test_create_tiktok_account(self):
        """Test creating TikTok account with real credentials"""
        print("📱 Testing TikTok account creation...")
        
        account_data = {
            'platform': 'tiktok',
            'username': 'grafisone',
            'password': 'Puyol@102410',
            'email': '<EMAIL>'
        }
        
        response = self.make_request('POST', '/actor/accounts/create/', account_data)

        print(f"🔍 Received response: {response}")
        print(f"🔍 Response type: {type(response)}")

        if response is None:
            print(f"❌ No response received")
            return False

        print(f"🔍 Checking status code: {response.status_code}")
        print(f"🔍 Status code type: {type(response.status_code)}")
        print(f"🔍 Status code == 200: {response.status_code == 200}")
        print(f"🔍 Status code == 400: {response.status_code == 400}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                self.account_id = result.get('account_id')
                print(f"✅ Account created successfully!")
                print(f"   Account ID: {self.account_id}")
                print(f"   Platform: {result.get('platform')}")
                print(f"   Username: {result.get('username')}")
                return True
            else:
                print(f"❌ Account creation failed: {result.get('error')}")
                return False
        elif response.status_code == 400:
            print(f"🔍 Handling 400 status code...")
            result = response.json()
            error_msg = result.get('error', '')
            if 'already exists' in error_msg:
                print(f"ℹ️ Account already exists, trying to get existing account...")
                # Try to get existing accounts and find the one we want
                accounts_response = self.make_request('GET', '/actor/accounts/list/')
                if accounts_response and accounts_response.status_code == 200:
                    accounts_result = accounts_response.json()
                    accounts = accounts_result.get('accounts', [])
                    for account in accounts:
                        if account.get('username') == 'grafisone' and account.get('platform') == 'tiktok':
                            self.account_id = account.get('id')
                            print(f"✅ Found existing account!")
                            print(f"   Account ID: {self.account_id}")
                            print(f"   Platform: {account.get('platform')}")
                            print(f"   Username: {account.get('username')}")
                            return True
                print(f"❌ Could not find existing account")
                return False
            else:
                print(f"❌ Account creation failed: {error_msg}")
                return False
        else:
            print(f"❌ API request failed: {response.status_code if response else 'No response'}")
            if response:
                print(f"   Response: {response.text}")
            return False

    def test_authenticate_account(self):
        """Test authenticating the TikTok account"""
        if not self.account_id:
            print("❌ No account ID available for authentication")
            return False
            
        print("🔐 Testing account authentication...")
        
        auth_data = {
            'account_id': self.account_id
        }
        
        response = self.make_request('POST', '/actor/accounts/authenticate/', auth_data)
        
        if response and response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Account authenticated successfully!")
                print(f"   Session valid: {result.get('session_valid')}")
                print(f"   Status: {result.get('status')}")
                return True
            else:
                print(f"❌ Authentication failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Authentication request failed: {response.status_code if response else 'No response'}")
            if response:
                print(f"   Response: {response.text}")
            return False

    def test_create_feed_scraping_task(self):
        """Test creating a feed scraping task"""
        if not self.account_id:
            print("❌ No account ID available for task creation")
            return False
            
        print("📋 Testing feed scraping task creation...")
        
        task_data = {
            'account_id': self.account_id,
            'task_type': 'MY_VIDEOS',  # Feed scraping
            'task_name': 'Test Feed Scraping - grafisone',
            'max_items': 10,
            'task_parameters': {
                'include_metadata': True,
                'quality_filter': 'high'
            }
        }
        
        response = self.make_request('POST', '/actor/tasks/create/', task_data)
        
        if response and response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ Task created successfully!")
                print(f"   Task ID: {task_id}")
                print(f"   Task Type: {result.get('task_type')}")
                print(f"   Status: {result.get('status')}")
                return task_id
            else:
                print(f"❌ Task creation failed: {result.get('error')}")
                return None
        else:
            print(f"❌ Task creation request failed: {response.status_code if response else 'No response'}")
            if response:
                print(f"   Response: {response.text}")
            return None

    def test_execute_task(self, task_id):
        """Test executing the scraping task"""
        if not task_id:
            print("❌ No task ID available for execution")
            return False
            
        print("🚀 Testing task execution...")
        
        execute_data = {
            'task_id': task_id
        }
        
        response = self.make_request('POST', '/actor/tasks/execute/', execute_data)
        
        if response and response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Task execution started successfully!")
                print(f"   Execution ID: {result.get('execution_id')}")
                print(f"   Status: {result.get('status')}")
                print(f"   Message: {result.get('message')}")
                return True
            else:
                print(f"❌ Task execution failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Task execution request failed: {response.status_code if response else 'No response'}")
            if response:
                print(f"   Response: {response.text}")
            return False

    def test_get_accounts(self):
        """Test getting user accounts"""
        print("📋 Testing account retrieval...")
        
        response = self.make_request('GET', '/actor/accounts/list/')
        
        if response and response.status_code == 200:
            result = response.json()
            accounts = result.get('accounts', [])
            print(f"✅ Retrieved {len(accounts)} accounts")
            for account in accounts:
                print(f"   - {account.get('platform')}: {account.get('username')} (ID: {account.get('id')})")
            return True
        else:
            print(f"❌ Account retrieval failed: {response.status_code if response else 'No response'}")
            if response:
                print(f"   Response: {response.text}")
            return False

    def test_get_tasks(self):
        """Test getting user tasks"""
        print("📋 Testing task retrieval...")
        
        response = self.make_request('GET', '/actor/tasks/list/')
        
        if response and response.status_code == 200:
            result = response.json()
            tasks = result.get('tasks', [])
            print(f"✅ Retrieved {len(tasks)} tasks")
            for task in tasks:
                print(f"   - {task.get('name')}: {task.get('status')} (ID: {task.get('id')})")
            return True
        else:
            print(f"❌ Task retrieval failed: {response.status_code if response else 'No response'}")
            if response:
                print(f"   Response: {response.text}")
            return False

    def run_full_test(self):
        """Run the complete test suite"""
        print("🧪 Starting full Actor system test...\n")
        
        # Setup
        if not self.setup_test_user():
            return False
        
        print()
        
        # Test account creation
        if not self.test_create_tiktok_account():
            return False
        
        print()
        
        # Test account authentication
        if not self.test_authenticate_account():
            print("⚠️ Authentication failed, but continuing with other tests...")
        
        print()
        
        # Test task creation
        task_id = self.test_create_feed_scraping_task()
        
        print()
        
        # Test task execution
        if task_id:
            self.test_execute_task(task_id)
        
        print()
        
        # Test data retrieval
        self.test_get_accounts()
        print()
        self.test_get_tasks()
        
        print()
        print("=" * 50)
        print("🎉 Actor System Test Complete!")
        print(f"📅 Finished: {datetime.now().isoformat()}")
        
        return True

if __name__ == '__main__':
    tester = ActorSystemTester()
    success = tester.run_full_test()
    sys.exit(0 if success else 1)
