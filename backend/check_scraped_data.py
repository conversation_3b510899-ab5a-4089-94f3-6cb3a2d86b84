#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from crawler_tiktok.models import ScrapedData, TikTokTask
import json

print("=== Database Content Analysis ===")
print(f"Total TikTok Tasks: {TikTokTask.objects.count()}")
print(f"Total Scraped Data: {ScrapedData.objects.count()}")

print("\n=== Recent Tasks ===")
tasks = TikTokTask.objects.all().order_by('-created_at')[:5]
for task in tasks:
    print(f"Task {task.id}: {task.status} - {task.task_type} - '{task.identifier}'")
    print(f"  Created: {task.created_at}")
    print(f"  Error: {task.error_message or 'None'}")
    
    # Check associated scraped data
    scraped_count = task.scraped_data.count()
    print(f"  Scraped data entries: {scraped_count}")
    
    if scraped_count > 0:
        sample_data = task.scraped_data.first()
        print(f"  Sample data type: {sample_data.data_type}")
        if isinstance(sample_data.content, dict):
            print(f"  Content keys: {list(sample_data.content.keys())}")
            # Print first few key-value pairs
            for key, value in list(sample_data.content.items())[:3]:
                if isinstance(value, (str, int, float, bool)):
                    print(f"    {key}: {value}")
                else:
                    print(f"    {key}: {type(value)} (length: {len(value) if hasattr(value, '__len__') else 'N/A'})")
        else:
            print(f"  Content type: {type(sample_data.content)}")
            print(f"  Content: {str(sample_data.content)[:200]}...")
    print("---")

print("\n=== Sample Scraped Data Content ===")
recent_data = ScrapedData.objects.all().order_by('-scraped_at')[:3]
for data in recent_data:
    print(f"\nData ID {data.id} ({data.data_type}):")
    if isinstance(data.content, dict):
        print(json.dumps(data.content, indent=2)[:500] + "..." if len(str(data.content)) > 500 else json.dumps(data.content, indent=2))
    else:
        print(f"Content: {data.content}")