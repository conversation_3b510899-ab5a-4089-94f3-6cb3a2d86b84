/**
 * Actor System API Client
 * 
 * This module provides API functions for the new Actor system that supports
 * multiple social media platforms (TikTok, Instagram, Facebook, Twitter, YouTube).
 */

import { api } from '../axios';

// Types for the Actor system
export interface Platform {
  value: string;
  label: string;
}

export interface ActorAccount {
  id: number;
  platform: string;
  username: string;
  email?: string;
  is_active: boolean;
  last_login?: string;
  session_valid: boolean;
  created_at: string;
}

export interface ActorTask {
  id: number;
  name: string;
  type: string;
  platform: string;
  status: string;
  created_at: string;
  target_identifier?: string;
  keywords?: string;
  max_items?: number;
  start_date?: string;
  end_date?: string;
}

export interface CreateAccountData {
  platform: string;
  username: string;
  password: string;
  email?: string;
}

export interface CreateTaskData {
  account_id: number;
  task_type: string;
  task_name: string;
  target_identifier?: string;
  keywords?: string;
  max_items?: number;
  start_date?: string;
  end_date?: string;
  task_parameters?: Record<string, any>;
}

// Platform Management
export const getAvailablePlatforms = async (): Promise<Platform[]> => {
  const response = await api.get('/actor/platforms/');
  return response.data.platforms.map((platform: string) => ({
    value: platform,
    label: platform.charAt(0).toUpperCase() + platform.slice(1)
  }));
};

// Account Management
export const createActorAccount = async (data: CreateAccountData) => {
  const response = await api.post('/actor/accounts/create/', data);
  return response.data;
};

export const authenticateActorAccount = async (accountId: number, credentials?: Record<string, any>) => {
  const response = await api.post('/actor/accounts/authenticate/', {
    account_id: accountId,
    credentials
  });
  return response.data;
};

export const getActorAccounts = async (platform?: string): Promise<ActorAccount[]> => {
  const params = platform ? { platform } : {};
  const response = await api.get('/actor/accounts/list/', { params });
  return response.data.accounts;
};

export const migrateTikTokAccount = async (tiktokAccountId: number) => {
  const response = await api.post('/actor/accounts/migrate-tiktok/', {
    tiktok_account_id: tiktokAccountId
  });
  return response.data;
};

// Task Management
export const createActorTask = async (data: CreateTaskData) => {
  const response = await api.post('/actor/tasks/create/', data);
  return response.data;
};

export const executeActorTask = async (taskId: number) => {
  const response = await api.post('/actor/tasks/execute/', {
    task_id: taskId
  });
  return response.data;
};

export const getActorTasks = async (filters: {
  platform?: string;
  account_id?: number;
  status?: string;
  task_type?: string;
} = {}): Promise<ActorTask[]> => {
  const response = await api.get('/actor/tasks/list/', { params: filters });
  return response.data.tasks;
};

export const updateActorTask = async (taskId: number, data: Partial<ActorTask>) => {
  const response = await api.put(`/actor/tasks/${taskId}/`, data);
  return response.data;
};

export const deleteActorTask = async (taskId: number) => {
  const response = await api.delete(`/actor/tasks/${taskId}/delete/`);
  return response.data;
};

// Search and Scraping
export const searchContentWithAccount = async (
  accountId: number,
  keywords: string[],
  options: {
    max_items?: number;
    start_date?: string;
    end_date?: string;
  } = {}
) => {
  const taskData: CreateTaskData = {
    account_id: accountId,
    task_type: 'CONTENT_SEARCH',
    task_name: `Search: ${keywords.join(', ')}`,
    keywords: keywords.join(','),
    max_items: options.max_items || 50,
    start_date: options.start_date,
    end_date: options.end_date
  };

  // Create and execute task
  const createResult = await createActorTask(taskData);
  if (createResult.success) {
    const executeResult = await executeActorTask(createResult.task_id);
    return executeResult;
  }
  return createResult;
};

export const scrapeUserContent = async (
  accountId: number,
  targetUsername: string,
  options: {
    max_items?: number;
  } = {}
) => {
  const taskData: CreateTaskData = {
    account_id: accountId,
    task_type: 'TARGETED_USER',
    task_name: `User Content: @${targetUsername}`,
    target_identifier: targetUsername,
    max_items: options.max_items || 50
  };

  // Create and execute task
  const createResult = await createActorTask(taskData);
  if (createResult.success) {
    const executeResult = await executeActorTask(createResult.task_id);
    return executeResult;
  }
  return createResult;
};

export const scrapeMyContent = async (
  accountId: number,
  options: {
    max_items?: number;
  } = {}
) => {
  const taskData: CreateTaskData = {
    account_id: accountId,
    task_type: 'MY_VIDEOS',
    task_name: 'My Content',
    max_items: options.max_items || 50
  };

  // Create and execute task
  const createResult = await createActorTask(taskData);
  if (createResult.success) {
    const executeResult = await executeActorTask(createResult.task_id);
    return executeResult;
  }
  return createResult;
};

// Utility functions
export const getPlatformIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    tiktok: '🎵',
    instagram: '📷',
    facebook: '👥',
    twitter: '🐦',
    youtube: '📺'
  };
  return icons[platform] || '🌐';
};

export const getPlatformColor = (platform: string): string => {
  const colors: Record<string, string> = {
    tiktok: 'bg-black text-white',
    instagram: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white',
    facebook: 'bg-blue-600 text-white',
    twitter: 'bg-blue-400 text-white',
    youtube: 'bg-red-600 text-white'
  };
  return colors[platform] || 'bg-gray-500 text-white';
};

export const getTaskTypeLabel = (taskType: string): string => {
  const labels: Record<string, string> = {
    'MY_VIDEOS': 'My Content',
    'MY_FOLLOWERS': 'My Followers',
    'MY_FOLLOWING': 'My Following',
    'MY_LIKES': 'My Liked Content',
    'FEED_SCRAPE': 'Feed Scraping',
    'TARGETED_USER': 'User Analysis',
    'HASHTAG_ANALYSIS': 'Hashtag Analysis',
    'COMPETITOR_ANALYSIS': 'Competitor Analysis',
    'CONTENT_SEARCH': 'Content Search',
    'KEYWORD_SEARCH': 'Keyword Search',
    'PROFILE_ANALYSIS': 'Profile Analysis'
  };
  return labels[taskType] || taskType;
};

export const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'RUNNING': 'bg-blue-100 text-blue-800',
    'COMPLETED': 'bg-green-100 text-green-800',
    'FAILED': 'bg-red-100 text-red-800',
    'CANCELLED': 'bg-gray-100 text-gray-800',
    'PAUSED': 'bg-orange-100 text-orange-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

// Additional CRUD operations (to be implemented)
export const updateActorAccount = async (accountId: number, data: Partial<ActorAccount>) => {
  const response = await api.put(`/actor/accounts/${accountId}/`, data);
  return response.data;
};

export const deleteActorAccount = async (accountId: number) => {
  const response = await api.delete(`/actor/accounts/${accountId}/delete/`);
  return response.data;
};

export const getActorScrapedData = async (filters: {
  platform?: string;
  account_id?: number;
  task_id?: number;
  data_type?: string;
  limit?: number;
} = {}) => {
  const response = await api.get('/actor/data/', { params: filters });
  return response.data;
};

export const getDataLabelingStats = async () => {
  const response = await api.get('/actor/data/stats/');
  return response.data;
};
