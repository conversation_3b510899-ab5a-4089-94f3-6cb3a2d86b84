'use client';

import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Shield, ShieldCheck, ShieldAlert, ShieldX, Trash2, RotateCcw, RefreshCw, Activity, Clock, TrendingUp } from "lucide-react";
import { getSessions, markSessionUnhealthy, resetSessionMetrics, deleteSession } from "@/lib/api/actor";
import type { TikTokSession } from "@/lib/types/actor";

export function SessionMonitor() {
    const [sessions, setSessions] = useState<TikTokSession[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchSessions = async () => {
        try {
            setLoading(true);
            setError(null);
            const data = await getSessions();
            setSessions(data);
        } catch (err) {
            setError('Failed to fetch sessions');
            console.error('Error fetching sessions:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleMarkUnhealthy = async (sessionId: number) => {
        try {
            await markSessionUnhealthy(sessionId);
            fetchSessions();
        } catch (error) {
            console.error('Failed to mark session unhealthy:', error);
        }
    };

    const handleResetMetrics = async (sessionId: number) => {
        try {
            await resetSessionMetrics(sessionId);
            fetchSessions();
        } catch (error) {
            console.error('Failed to reset session metrics:', error);
        }
    };

    const handleDeleteSession = async (sessionId: number) => {
        try {
            await deleteSession(sessionId);
            fetchSessions();
        } catch (error) {
            console.error('Failed to delete session:', error);
        }
    };

    useEffect(() => {
        fetchSessions();
    }, []);

    const getHealthIcon = (isHealthy: boolean, detectionScore: number) => {
        if (!isHealthy) {
            return <ShieldX className="h-4 w-4 text-red-500" />;
        }
        if (detectionScore > 0.7) {
            return <ShieldAlert className="h-4 w-4 text-yellow-500" />;
        }
        return <ShieldCheck className="h-4 w-4 text-green-500" />;
    };

    const getHealthColor = (isHealthy: boolean, detectionScore: number) => {
        if (!isHealthy) {
            return 'bg-red-100 text-red-800';
        }
        if (detectionScore > 0.7) {
            return 'bg-yellow-100 text-yellow-800';
        }
        return 'bg-green-100 text-green-800';
    };

    const getHealthStatus = (isHealthy: boolean, detectionScore: number) => {
        if (!isHealthy) return 'Unhealthy';
        if (detectionScore > 0.7) return 'Warning';
        return 'Healthy';
    };

    const calculateSuccessRate = (session: TikTokSession) => {
        const total = session.successful_requests + session.failed_requests;
        if (total === 0) return 0;
        return (session.successful_requests / total) * 100;
    };

    const formatLastActivity = (lastActivity: string | null) => {
        if (!lastActivity) return 'Never';
        
        const now = new Date();
        const activity = new Date(lastActivity);
        const diffMs = now.getTime() - activity.getTime();
        const diffMins = Math.floor(diffMs / (1000 * 60));
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
        return `${Math.floor(diffMins / 1440)}d ago`;
    };

    const isSessionExpired = (expiresAt: string | null) => {
        if (!expiresAt) return false;
        return new Date(expiresAt) < new Date();
    };

    if (loading) {
        return (
            <Card className="p-6">
                <div className="text-center">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p>Loading sessions...</p>
                </div>
            </Card>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }



    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <p className="text-sm text-gray-600">{sessions.length} sessions</p>
                    <Badge variant="outline">
                        {sessions.filter(s => s.is_healthy).length} healthy
                    </Badge>
                </div>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchSessions}
                    disabled={loading}
                >
                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
            </div>
            
            <div className="space-y-2 max-h-80 overflow-y-auto">
                {sessions.map((session) => (
                    <Card key={session.id} className="p-3">
                        <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                                {getHealthIcon(session.is_healthy, session.detection_score)}
                                <div>
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium text-sm">
                                            {session.account?.tiktok_username || 'Unknown Account'}
                                        </span>
                                        <Badge className={getHealthColor(session.is_healthy, session.detection_score)}>
                                            {getHealthStatus(session.is_healthy, session.detection_score)}
                                        </Badge>
                                        {isSessionExpired(session.expires_at) && (
                                            <Badge variant="destructive">Expired</Badge>
                                        )}
                                    </div>
                                    <p className="text-xs text-gray-500">
                                        Session: {session.session_id.substring(0, 8)}...
                                    </p>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-1">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleResetMetrics(session.id)}
                                    className="text-xs px-2 py-1 h-auto"
                                >
                                    Reset
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleMarkUnhealthy(session.id)}
                                    className="text-xs px-2 py-1 h-auto text-yellow-600"
                                >
                                    Mark Unhealthy
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteSession(session.id)}
                                    className="text-xs px-2 py-1 h-auto text-red-600"
                                >
                                    <Trash2 className="h-3 w-3" />
                                </Button>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-xs">
                            <div>
                                <div className="flex justify-between mb-1">
                                    <span className="text-gray-500">Success Rate</span>
                                    <span className="font-medium">
                                        {calculateSuccessRate(session).toFixed(1)}%
                                    </span>
                                </div>
                                <Progress 
                                    value={calculateSuccessRate(session)} 
                                    className="h-1"
                                />
                            </div>
                            
                            <div>
                                <div className="flex justify-between mb-1">
                                    <span className="text-gray-500">Detection Score</span>
                                    <span className="font-medium">
                                        {(session.detection_score * 100).toFixed(0)}%
                                    </span>
                                </div>
                                <Progress
                                    value={session.detection_score * 100}
                                    className="h-1"
                                    indicatorClassName={session.detection_score > 0.7 ? 'bg-red-500' : session.detection_score > 0.4 ? 'bg-yellow-500' : 'bg-green-500'}
                                />
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 gap-2 mt-2 text-xs text-gray-500">
                            <div className="text-center">
                                <div className="font-medium text-gray-900">{session.requests_made}</div>
                                <div>Requests</div>
                            </div>
                            <div className="text-center">
                                <div className="font-medium text-gray-900">{session.captcha_challenges}</div>
                                <div>Captchas</div>
                            </div>
                            <div className="text-center">
                                <div className="font-medium text-gray-900">{session.rate_limit_hits}</div>
                                <div>Rate Limits</div>
                            </div>
                            <div className="text-center">
                                <div className="font-medium text-gray-900 flex items-center justify-center gap-1">
                                    <Activity className="h-3 w-3" />
                                    {formatLastActivity(session.last_activity)}
                                </div>
                                <div>Last Active</div>
                            </div>
                        </div>
                        
                        {session.proxy_used && (
                            <div className="mt-2 pt-2 border-t text-xs text-gray-500">
                                <span className="font-medium">Proxy:</span> {session.proxy_used}
                            </div>
                        )}
                    </Card>
                ))}
                
                {sessions.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No active sessions</p>
                        <p className="text-sm">Sessions will appear here when tasks are running</p>
                    </div>
                )}
            </div>
        </div>
    );
}