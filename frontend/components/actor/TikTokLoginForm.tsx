'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Loader2, Eye, EyeOff, Shield, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { loginToTikTok, testSimpleLogin } from '@/lib/api/actor';

interface TikTokLoginFormProps {
    onLoginSuccess?: (result: any) => void;
    onLoginError?: (error: string) => void;
    showTestMode?: boolean;
}

export function TikTokLoginForm({ onLoginSuccess, onLoginError, showTestMode = true }: TikTokLoginFormProps) {
    const [formData, setFormData] = useState({
        tiktok_username: 'gra<PERSON>sone', // Pre-fill with working credentials
        tiktok_password: 'Puyol@102410',
        use_2fa: false,
        two_factor_code: '',
        remember_session: true,
        use_simple_login: true // New option for our working login
    });

    const [loading, setLoading] = useState(false);
    const [testLoading, setTestLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);
    const [testResult, setTestResult] = useState<any>(null);
    const [showPassword, setShowPassword] = useState(false);

    const handleInputChange = (field: string, value: string | boolean) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear messages when user starts typing
        if (error) {
            setError(null);
        }
        if (testResult) {
            setTestResult(null);
        }
    };

    const handleTestLogin = async () => {
        if (!formData.tiktok_username.trim() || !formData.tiktok_password.trim()) {
            setError('Username and password are required for testing');
            return;
        }

        setTestLoading(true);
        setError(null);
        setTestResult(null);

        try {
            const result = await testSimpleLogin({
                username: formData.tiktok_username.trim(),
                password: formData.tiktok_password
            });

            setTestResult(result);

            if (result.success) {
                const sessionMessage = result.session_reused
                    ? `🔄 Session reused! No re-authentication needed.`
                    : `✅ New session created with ${result.cookies_count || 0} cookies.`;

                setSuccess(`${sessionMessage} Ready for content scraping.`);
            } else {
                setError(`Test login failed: ${result.error}`);
            }
        } catch (err: any) {
            console.error('Test login failed:', err);
            const errorMessage = err.response?.data?.error || err.message || 'Test login failed';
            setError(errorMessage);
        } finally {
            setTestLoading(false);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.tiktok_username.trim() || !formData.tiktok_password.trim()) {
            setError('Username and password are required');
            return;
        }

        if (formData.use_2fa && !formData.two_factor_code.trim()) {
            setError('Two-factor authentication code is required');
            return;
        }

        setLoading(true);
        setError(null);
        setSuccess(null);
        setTestResult(null);

        try {
            let response;

            if (formData.use_simple_login) {
                // Use our working simple login
                response = await testSimpleLogin({
                    username: formData.tiktok_username.trim(),
                    password: formData.tiktok_password,
                    create_account: true // Create account record
                });

                if (response.success) {
                    // Store JWT tokens in localStorage
                    if (response.access_token) {
                        localStorage.setItem('access_token', response.access_token);
                    }
                    if (response.refresh_token) {
                        localStorage.setItem('refresh_token', response.refresh_token);
                    }
                    if (response.user) {
                        localStorage.setItem('user', JSON.stringify(response.user));
                    }

                    const sessionMessage = response.session_reused
                        ? `🔄 Using existing session! No re-authentication needed.`
                        : `✅ New session created! Session established with ${response.cookies_count || 0} cookies.`;

                    setSuccess(`${sessionMessage} Ready for keyword-based content scraping.`);

                    if (onLoginSuccess) {
                        onLoginSuccess(response);
                    }
                } else {
                    throw new Error(response.error || 'Simple login failed');
                }
            } else {
                // Use traditional login method
                response = await loginToTikTok({
                    tiktok_username: formData.tiktok_username.trim(),
                    tiktok_password: formData.tiktok_password,
                    use_2fa: formData.use_2fa,
                    two_factor_code: formData.two_factor_code.trim() || undefined,
                    remember_session: formData.remember_session
                });

                setSuccess(`${response.message} (Task ID: ${response.task_id})`);

                if (onLoginSuccess) {
                    onLoginSuccess(response);
                }
            }

        } catch (err: any) {
            console.error('Login failed:', err);
            let errorMessage = 'Login failed. Please try again.';

            if (err.response?.status === 400) {
                if (err.response?.data) {
                    const errorData = err.response.data;
                    if (typeof errorData === 'object') {
                        const errorMessages = Object.entries(errorData)
                            .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
                            .join('. ');
                        errorMessage = errorMessages;
                    } else {
                        errorMessage = errorData.toString();
                    }
                } else {
                    errorMessage = 'Invalid request. Please check your credentials.';
                }
            } else if (err.response?.status === 401) {
                errorMessage = 'Authentication required. Please log in to your account first.';
            } else if (err.response?.data) {
                const errorData = err.response.data;
                if (typeof errorData === 'object') {
                    const errorMessages = Object.entries(errorData)
                        .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
                        .join('. ');
                    errorMessage = errorMessages;
                } else {
                    errorMessage = errorData.toString();
                }
            } else {
                errorMessage = err.message || 'Login failed. Please try again.';
            }

            setError(errorMessage);

            if (onLoginError) {
                onLoginError(errorMessage);
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-md mx-auto">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Legacy TikTok Login
                    {formData.use_simple_login && (
                        <Badge variant="secondary" className="ml-2">
                            Simple Mode
                        </Badge>
                    )}
                </CardTitle>
                <CardDescription>
                    Login to your TikTok account using the legacy authentication system
                </CardDescription>
                {testResult && testResult.success && (
                    <div className="flex items-center gap-2 text-green-600 text-sm">
                        <CheckCircle className="h-4 w-4" />
                        Last test: Session established successfully
                    </div>
                )}
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="username">TikTok Username or Email</Label>
                        <Input
                            id="username"
                            type="text"
                            placeholder="@username or email"
                            value={formData.tiktok_username}
                            onChange={(e) => handleInputChange('tiktok_username', e.target.value)}
                            disabled={loading}
                            autoComplete="username"
                            required
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <div className="relative">
                            <Input
                                id="password"
                                type={showPassword ? 'text' : 'password'}
                                placeholder="Enter your password"
                                value={formData.tiktok_password}
                                onChange={(e) => handleInputChange('tiktok_password', e.target.value)}
                                disabled={loading}
                                autoComplete="current-password"
                                required
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                                disabled={loading}
                            >
                                {showPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                ) : (
                                    <Eye className="h-4 w-4" />
                                )}
                            </Button>
                        </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="use_simple_login"
                            checked={formData.use_simple_login}
                            onCheckedChange={(checked) => handleInputChange('use_simple_login', checked as boolean)}
                            disabled={loading || testLoading}
                        />
                        <Label htmlFor="use_simple_login" className="text-sm font-medium">
                            Use Simple Login (Recommended)
                        </Label>
                        <Badge variant="outline" className="text-xs">
                            ✅ Working
                        </Badge>
                    </div>

                    {!formData.use_simple_login && (
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="use_2fa"
                                checked={formData.use_2fa}
                                onCheckedChange={(checked) => handleInputChange('use_2fa', checked as boolean)}
                                disabled={loading || testLoading}
                            />
                            <Label htmlFor="use_2fa" className="text-sm">
                                Use Two-Factor Authentication
                            </Label>
                        </div>
                    )}
                    
                    {!formData.use_simple_login && formData.use_2fa && (
                        <div className="space-y-2">
                            <Label htmlFor="two_factor_code">2FA Code</Label>
                            <Input
                                id="two_factor_code"
                                type="text"
                                placeholder="Enter 6-digit code"
                                value={formData.two_factor_code}
                                onChange={(e) => handleInputChange('two_factor_code', e.target.value)}
                                disabled={loading || testLoading}
                                maxLength={6}
                                pattern="[0-9]{6}"
                            />
                        </div>
                    )}

                    {showTestMode && formData.use_simple_login && (
                        <div className="space-y-2">
                            <Button
                                type="button"
                                variant="outline"
                                className="w-full"
                                onClick={handleTestLogin}
                                disabled={loading || testLoading || !formData.tiktok_username.trim() || !formData.tiktok_password.trim()}
                            >
                                {testLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Testing Login...
                                    </>
                                ) : (
                                    <>
                                        <Clock className="mr-2 h-4 w-4" />
                                        Test Login & Session Setup
                                    </>
                                )}
                            </Button>
                        </div>
                    )}
                    
                    {!formData.use_simple_login && (
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="remember_session"
                                checked={formData.remember_session}
                                onCheckedChange={(checked) => handleInputChange('remember_session', checked as boolean)}
                                disabled={loading || testLoading}
                            />
                            <Label htmlFor="remember_session" className="text-sm">
                                Remember session
                            </Label>
                        </div>
                    )}
                    
                    {testResult && !testResult.success && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                                Test failed: {testResult.error}
                                {testResult.error?.includes('Maximum number of attempts') && (
                                    <div className="mt-2 text-sm">
                                        <strong>This is normal TikTok protection.</strong> Wait 30-60 minutes and try again.
                                    </div>
                                )}
                            </AlertDescription>
                        </Alert>
                    )}

                    {testResult && testResult.success && (
                        <Alert>
                            <CheckCircle className="h-4 w-4" />
                            <AlertDescription>
                                ✅ Test successful! Session established and ready for content scraping:
                                {testResult.sample_authors && (
                                    <div className="mt-1 text-sm">
                                        {testResult.sample_authors.slice(0, 3).join(', ')}
                                    </div>
                                )}
                            </AlertDescription>
                        </Alert>
                    )}

                    {error && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    {success && (
                        <Alert>
                            <CheckCircle className="h-4 w-4" />
                            <AlertDescription>{success}</AlertDescription>
                        </Alert>
                    )}
                    
                    <Button
                        type="submit"
                        className="w-full"
                        disabled={loading || testLoading}
                        variant={formData.use_simple_login ? "default" : "secondary"}
                    >
                        {loading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {formData.use_simple_login ? 'Logging in & Setting up...' : 'Logging in...'}
                            </>
                        ) : (
                            <>
                                <Shield className="mr-2 h-4 w-4" />
                                {formData.use_simple_login ? 'Login & Start Content Scraping' : 'Login to TikTok'}
                            </>
                        )}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}