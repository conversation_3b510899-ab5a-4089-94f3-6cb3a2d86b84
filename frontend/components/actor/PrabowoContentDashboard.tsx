'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
    Play, 
    Heart, 
    MessageCircle, 
    Share, 
    ExternalLink, 
    RefreshCw, 
    TrendingUp,
    Users,
    Video,
    BarChart3
} from 'lucide-react';
import { scrapePrabowoContent } from '@/lib/api/actor';

interface PrabowoVideo {
    video_id: string;
    url: string;
    author: string;
    description?: string;
    metrics: {
        likes?: string;
        comments?: string;
        shares?: string;
    };
    extracted_at: string;
}

interface PrabowoContentDashboardProps {
    username: string;
    password: string;
    autoRefresh?: boolean;
    refreshInterval?: number;
}

export function PrabowoContentDashboard({ 
    username, 
    password, 
    autoRefresh = false, 
    refreshInterval = 300000 // 5 minutes
}: PrabowoContentDashboardProps) {
    const [videos, setVideos] = useState<PrabowoVideo[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
    const [stats, setStats] = useState({
        totalVideos: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        uniqueAuthors: 0
    });

    const parseMetric = (metric: string): number => {
        if (!metric) return 0;
        const cleanMetric = metric.replace(/[^\d.KMB]/g, '');
        if (cleanMetric.includes('K')) {
            return Math.floor(parseFloat(cleanMetric.replace('K', '')) * 1000);
        } else if (cleanMetric.includes('M')) {
            return Math.floor(parseFloat(cleanMetric.replace('M', '')) * 1000000);
        } else if (cleanMetric.includes('B')) {
            return Math.floor(parseFloat(cleanMetric.replace('B', '')) * 1000000000);
        }
        return parseInt(cleanMetric) || 0;
    };

    const calculateStats = (videoList: PrabowoVideo[]) => {
        const totalLikes = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.likes || '0'), 0);
        const totalComments = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.comments || '0'), 0);
        const totalShares = videoList.reduce((sum, video) => sum + parseMetric(video.metrics.shares || '0'), 0);
        const uniqueAuthors = new Set(videoList.map(video => video.author)).size;

        setStats({
            totalVideos: videoList.length,
            totalLikes,
            totalComments,
            totalShares,
            uniqueAuthors
        });
    };

    const fetchPrabowoContent = async () => {
        setLoading(true);
        setError(null);

        try {
            const result = await scrapePrabowoContent({
                username,
                password,
                max_videos: 20
            });

            if (result.success) {
                setVideos(result.videos);
                calculateStats(result.videos);
                setLastUpdate(new Date());
            } else {
                setError(result.error || 'Failed to fetch Prabowo content');
            }
        } catch (err: any) {
            console.error('Failed to fetch Prabowo content:', err);
            setError(err.response?.data?.error || err.message || 'Failed to fetch content');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPrabowoContent();
    }, [username, password]);

    useEffect(() => {
        if (autoRefresh && refreshInterval > 0) {
            const interval = setInterval(fetchPrabowoContent, refreshInterval);
            return () => clearInterval(interval);
        }
    }, [autoRefresh, refreshInterval]);

    const formatNumber = (num: number): string => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    };

    const getAuthorDisplayName = (author: string): string => {
        return author.startsWith('@') ? author : `@${author}`;
    };

    const isOfficialAccount = (author: string): boolean => {
        return author.toLowerCase().includes('prabowo') || 
               author.toLowerCase().includes('subianto') ||
               author.toLowerCase().includes('official');
    };

    const isNewsAccount = (author: string): boolean => {
        const newsKeywords = ['bbc', 'tvri', 'republika', 'news', 'media', 'kompas', 'detik'];
        return newsKeywords.some(keyword => author.toLowerCase().includes(keyword));
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Prabowo Content Dashboard</h2>
                    <p className="text-muted-foreground">
                        Real-time monitoring of Prabowo-related TikTok content
                    </p>
                </div>
                <Button 
                    onClick={fetchPrabowoContent} 
                    disabled={loading}
                    variant="outline"
                >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                </Button>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Video className="h-8 w-8 text-blue-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Videos</p>
                                <p className="text-2xl font-bold">{stats.totalVideos}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Heart className="h-8 w-8 text-red-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Likes</p>
                                <p className="text-2xl font-bold">{formatNumber(stats.totalLikes)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <MessageCircle className="h-8 w-8 text-green-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Total Comments</p>
                                <p className="text-2xl font-bold">{formatNumber(stats.totalComments)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                        <div className="flex items-center">
                            <Users className="h-8 w-8 text-purple-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-muted-foreground">Content Sources</p>
                                <p className="text-2xl font-bold">{stats.uniqueAuthors}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Error Display */}
            {error && (
                <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Last Update Info */}
            {lastUpdate && (
                <div className="text-sm text-muted-foreground">
                    Last updated: {lastUpdate.toLocaleString()}
                </div>
            )}

            {/* Content Tabs */}
            <Tabs defaultValue="videos" className="w-full">
                <TabsList>
                    <TabsTrigger value="videos">Videos</TabsTrigger>
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>

                <TabsContent value="videos" className="space-y-4">
                    {loading && videos.length === 0 ? (
                        <div className="text-center py-8">
                            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
                            <p>Loading Prabowo content...</p>
                        </div>
                    ) : videos.length === 0 ? (
                        <div className="text-center py-8">
                            <Video className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                            <p>No Prabowo content found. Try refreshing or check your login credentials.</p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {videos.map((video, index) => (
                                <Card key={video.video_id || index} className="hover:shadow-lg transition-shadow">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center justify-between">
                                            <CardTitle className="text-sm font-medium">
                                                {getAuthorDisplayName(video.author)}
                                            </CardTitle>
                                            <div className="flex gap-1">
                                                {isOfficialAccount(video.author) && (
                                                    <Badge variant="default" className="text-xs">
                                                        Official
                                                    </Badge>
                                                )}
                                                {isNewsAccount(video.author) && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        News
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                        {video.description && (
                                            <CardDescription className="text-xs line-clamp-2">
                                                {video.description}
                                            </CardDescription>
                                        )}
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
                                            <div className="flex items-center gap-3">
                                                {video.metrics.likes && (
                                                    <div className="flex items-center gap-1">
                                                        <Heart className="h-3 w-3" />
                                                        <span>{video.metrics.likes}</span>
                                                    </div>
                                                )}
                                                {video.metrics.comments && (
                                                    <div className="flex items-center gap-1">
                                                        <MessageCircle className="h-3 w-3" />
                                                        <span>{video.metrics.comments}</span>
                                                    </div>
                                                )}
                                                {video.metrics.shares && (
                                                    <div className="flex items-center gap-1">
                                                        <Share className="h-3 w-3" />
                                                        <span>{video.metrics.shares}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <Button 
                                            variant="outline" 
                                            size="sm" 
                                            className="w-full"
                                            onClick={() => window.open(video.url, '_blank')}
                                        >
                                            <ExternalLink className="h-3 w-3 mr-2" />
                                            View on TikTok
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="analytics" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Engagement Overview
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex justify-between">
                                        <span>Average Likes per Video:</span>
                                        <span className="font-medium">
                                            {formatNumber(Math.floor(stats.totalLikes / Math.max(stats.totalVideos, 1)))}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Average Comments per Video:</span>
                                        <span className="font-medium">
                                            {formatNumber(Math.floor(stats.totalComments / Math.max(stats.totalVideos, 1)))}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Average Shares per Video:</span>
                                        <span className="font-medium">
                                            {formatNumber(Math.floor(stats.totalShares / Math.max(stats.totalVideos, 1)))}
                                        </span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Content Sources
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <span>Official Accounts:</span>
                                        <span className="font-medium">
                                            {videos.filter(v => isOfficialAccount(v.author)).length}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>News Outlets:</span>
                                        <span className="font-medium">
                                            {videos.filter(v => isNewsAccount(v.author)).length}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>Other Creators:</span>
                                        <span className="font-medium">
                                            {videos.filter(v => !isOfficialAccount(v.author) && !isNewsAccount(v.author)).length}
                                        </span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}
