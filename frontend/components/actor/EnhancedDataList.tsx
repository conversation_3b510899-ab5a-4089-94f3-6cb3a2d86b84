'use client';

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
    Database, Search, Filter, Download, Eye, User, Activity,
    CheckCircle, AlertCircle, RefreshCw, BarChart3, Target
} from "lucide-react";
import {
    getActorScrapedData,
    getDataLabelingStats,
    getPlatformIcon,
    getPlatformColor,
    type ActorAccount
} from "@/lib/api/actor-system";

interface ScrapedDataItem {
    id: number;
    data_type: string;
    platform: string;
    account_username: string;
    platform_content_id: string;
    content: any;
    scraped_at: string;
    is_complete: boolean;
    quality_score: number;
    task_info?: {
        id: number;
        name: string;
        type: string;
    };
    account_info?: {
        id: number;
        platform: string;
        username: string;
    };
}

interface DataStats {
    total_items: number;
    platforms_used: number;
    accounts_used: number;
    average_quality_score: number;
    complete_items: number;
    completion_rate: number;
    platform_breakdown: Record<string, number>;
    data_type_breakdown: Record<string, number>;
}

interface EnhancedDataListProps {
    accounts: ActorAccount[];
}

export function EnhancedDataList({ accounts }: EnhancedDataListProps) {
    const [data, setData] = useState<ScrapedDataItem[]>([]);
    const [stats, setStats] = useState<DataStats | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [platformFilter, setPlatformFilter] = useState('ALL');
    const [dataTypeFilter, setDataTypeFilter] = useState('ALL');
    const [accountFilter, setAccountFilter] = useState('ALL');
    const [qualityFilter, setQualityFilter] = useState('ALL');

    useEffect(() => {
        loadData();
        loadStats();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            setError(null);

            const filters = {
                platform: platformFilter !== 'ALL' ? platformFilter : undefined,
                account_id: accountFilter !== 'ALL' ? parseInt(accountFilter) : undefined,
                data_type: dataTypeFilter !== 'ALL' ? dataTypeFilter : undefined,
                limit: 100
            };

            const result = await getActorScrapedData(filters);
            
            if (result.success) {
                setData(result.data);
            } else {
                setError(result.error || 'Failed to load data');
            }
        } catch (err: any) {
            console.error('Error loading data:', err);
            setError(err.response?.data?.error || 'Failed to load data');
        } finally {
            setLoading(false);
        }
    };

    const loadStats = async () => {
        try {
            const result = await getDataLabelingStats();
            
            if (result.success) {
                setStats(result.stats);
            }
        } catch (err: any) {
            console.error('Error loading stats:', err);
        }
    };

    const getFilteredData = () => {
        return data.filter(item => {
            const matchesSearch = item.account_username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                item.data_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                item.platform.toLowerCase().includes(searchTerm.toLowerCase());
            
            const matchesPlatform = platformFilter === 'ALL' || item.platform === platformFilter;
            const matchesDataType = dataTypeFilter === 'ALL' || item.data_type === dataTypeFilter;
            const matchesAccount = accountFilter === 'ALL' || item.account_info?.id.toString() === accountFilter;
            
            let matchesQuality = true;
            if (qualityFilter === 'HIGH') matchesQuality = item.quality_score >= 0.8;
            else if (qualityFilter === 'MEDIUM') matchesQuality = item.quality_score >= 0.5 && item.quality_score < 0.8;
            else if (qualityFilter === 'LOW') matchesQuality = item.quality_score < 0.5;

            return matchesSearch && matchesPlatform && matchesDataType && matchesAccount && matchesQuality;
        });
    };

    const getQualityColor = (score: number) => {
        if (score >= 0.8) return 'bg-green-100 text-green-800';
        if (score >= 0.5) return 'bg-yellow-100 text-yellow-800';
        return 'bg-red-100 text-red-800';
    };

    const getQualityLabel = (score: number) => {
        if (score >= 0.8) return 'High';
        if (score >= 0.5) return 'Medium';
        return 'Low';
    };

    const filteredData = getFilteredData();

    return (
        <div className="space-y-6">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            {stats && (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
                    <Card className="p-4">
                        <div className="flex items-center gap-3">
                            <Database className="h-8 w-8 text-blue-500" />
                            <div>
                                <p className="text-sm text-gray-500">Total Items</p>
                                <p className="text-2xl font-bold">{stats.total_items}</p>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4">
                        <div className="flex items-center gap-3">
                            <Activity className="h-8 w-8 text-purple-500" />
                            <div>
                                <p className="text-sm text-gray-500">Platforms</p>
                                <p className="text-2xl font-bold">{stats.platforms_used}</p>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4">
                        <div className="flex items-center gap-3">
                            <User className="h-8 w-8 text-green-500" />
                            <div>
                                <p className="text-sm text-gray-500">Accounts</p>
                                <p className="text-2xl font-bold">{stats.accounts_used}</p>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4">
                        <div className="flex items-center gap-3">
                            <BarChart3 className="h-8 w-8 text-orange-500" />
                            <div>
                                <p className="text-sm text-gray-500">Avg Quality</p>
                                <p className="text-2xl font-bold">{stats.average_quality_score}</p>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4">
                        <div className="flex items-center gap-3">
                            <CheckCircle className="h-8 w-8 text-green-500" />
                            <div>
                                <p className="text-sm text-gray-500">Complete</p>
                                <p className="text-2xl font-bold">{stats.complete_items}</p>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4">
                        <div className="flex items-center gap-3">
                            <Target className="h-8 w-8 text-blue-500" />
                            <div>
                                <p className="text-sm text-gray-500">Success Rate</p>
                                <p className="text-2xl font-bold">{stats.completion_rate}%</p>
                            </div>
                        </div>
                    </Card>
                </div>
            )}

            {/* Controls */}
            <div className="flex justify-between items-center">
                <div className="flex gap-4 items-center">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search data..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10 w-64"
                        />
                    </div>
                    <Select value={platformFilter} onValueChange={setPlatformFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Platforms</SelectItem>
                            <SelectItem value="tiktok">TikTok</SelectItem>
                            <SelectItem value="instagram">Instagram</SelectItem>
                            <SelectItem value="facebook">Facebook</SelectItem>
                            <SelectItem value="twitter">Twitter</SelectItem>
                            <SelectItem value="youtube">YouTube</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={dataTypeFilter} onValueChange={setDataTypeFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Types</SelectItem>
                            <SelectItem value="VIDEO">Video</SelectItem>
                            <SelectItem value="USER">User</SelectItem>
                            <SelectItem value="POST">Post</SelectItem>
                            <SelectItem value="FOLLOWER">Follower</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={qualityFilter} onValueChange={setQualityFilter}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ALL">All Quality</SelectItem>
                            <SelectItem value="HIGH">High (≥80%)</SelectItem>
                            <SelectItem value="MEDIUM">Medium (50-79%)</SelectItem>
                            <SelectItem value="LOW">Low (<50%)</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex gap-2">
                    <Button onClick={loadData} variant="outline" size="sm" disabled={loading}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                </div>
            </div>

            {/* Data List */}
            {filteredData.length === 0 ? (
                <Card className="p-8 text-center">
                    <Database className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No data found</h3>
                    <p className="text-gray-600 mb-4">
                        {data.length === 0 
                            ? "No scraped data available yet. Run some tasks to collect data."
                            : "No data matches your current filters."
                        }
                    </p>
                </Card>
            ) : (
                <div className="grid gap-4">
                    {filteredData.map((item) => (
                        <Card key={item.id} className="p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    {/* Platform Icon */}
                                    <div className={`p-3 rounded-full ${getPlatformColor(item.platform)}`}>
                                        <span className="text-lg">{getPlatformIcon(item.platform)}</span>
                                    </div>
                                    
                                    {/* Data Info */}
                                    <div>
                                        <div className="flex items-center gap-2 mb-1">
                                            <h4 className="font-medium">{item.data_type}</h4>
                                            <Badge variant="outline">
                                                {item.platform.charAt(0).toUpperCase() + item.platform.slice(1)}
                                            </Badge>
                                            <Badge className={getQualityColor(item.quality_score)}>
                                                {getQualityLabel(item.quality_score)} Quality
                                            </Badge>
                                            {item.is_complete ? (
                                                <Badge className="bg-green-100 text-green-800">Complete</Badge>
                                            ) : (
                                                <Badge className="bg-yellow-100 text-yellow-800">Partial</Badge>
                                            )}
                                        </div>
                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                            <span>@{item.account_username}</span>
                                            <span>• {new Date(item.scraped_at).toLocaleDateString()}</span>
                                            {item.platform_content_id && (
                                                <span>• ID: {item.platform_content_id}</span>
                                            )}
                                            {item.task_info && (
                                                <span>• Task: {item.task_info.name}</span>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center gap-2">
                                    <Button size="sm" variant="outline">
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                    <Button size="sm" variant="outline">
                                        <Download className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}

            <div className="text-center text-sm text-gray-500">
                Showing {filteredData.length} of {data.length} items
            </div>
        </div>
    );
}
