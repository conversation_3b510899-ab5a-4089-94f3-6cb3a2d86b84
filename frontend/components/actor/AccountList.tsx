'use client';

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Edit, Trash2, User, Activity, LogIn, Users } from "lucide-react";
import { updateAccount, deleteAccount } from "@/lib/api/actor";
import { TikTokLoginForm } from "@/components/actor/TikTokLoginForm";
import type { TikTokAccount, AccountStatus } from "@/lib/types/actor";

interface AccountListProps {
    accounts: TikTokAccount[];
    onAccountsChange: () => void;
}

interface AccountFormData {
    tiktok_username: string;
    email: string;
    password: string;
    status: AccountStatus;
}

export function AccountList({ accounts, onAccountsChange }: AccountListProps) {
    const [isLoginOpen, setIsLoginOpen] = useState(false);
    const [isMultiLoginOpen, setIsMultiLoginOpen] = useState(false);
    const [editingAccount, setEditingAccount] = useState<TikTokAccount | null>(null);
    const [multiLoginLoading, setMultiLoginLoading] = useState(false);
    const [formData, setFormData] = useState<AccountFormData>({
        tiktok_username: '',
        email: '',
        password: '',
        status: 'ACTIVE'
    });
    const [loading, setLoading] = useState(false);

    const resetForm = () => {
        setFormData({
            tiktok_username: '',
            email: '',
            password: '',
            status: 'ACTIVE'
        });
    };



    const handleEdit = (account: TikTokAccount) => {
        setEditingAccount(account);
        setFormData({
            tiktok_username: account.tiktok_username,
            email: account.email || '',
            password: '', // Don't pre-fill password for security
            status: account.status
        });
    };

    const handleUpdate = async () => {
        if (!editingAccount) return;
        
        try {
            setLoading(true);
            await updateAccount(editingAccount.id, formData);
            setEditingAccount(null);
            resetForm();
            onAccountsChange();
        } catch (error) {
            console.error('Error updating account:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (accountId: number) => {
        if (!confirm('Are you sure you want to delete this account?')) return;
        
        try {
            await deleteAccount(accountId);
            onAccountsChange();
        } catch (error) {
            console.error('Error deleting account:', error);
        }
    };

    const handleLoginSuccess = (taskId: string) => {
        console.log('Login task initiated:', taskId);
        setIsLoginOpen(false);
        // Refresh accounts after a short delay to allow the login task to complete
        setTimeout(() => {
            onAccountsChange();
        }, 2000);
    };

    const handleLoginError = (error: string) => {
        console.error('Login error:', error);
        // Error is already handled by the TikTokLoginForm component
    };

    const handleMultiAccountLogin = async () => {
        try {
            setMultiLoginLoading(true);
            
            // Get all accounts with credentials
            const accountsWithCredentials = accounts
                .filter(account => account.status === 'ACTIVE')
                .map(account => ({
                    username: account.tiktok_username,
                    email: account.email || account.tiktok_username,
                    password: 'stored_password' // This would need to be handled securely
                }));
            
            if (accountsWithCredentials.length === 0) {
                alert('No active accounts found for multi-login');
                return;
            }
            
            const response = await fetch('/api/actor/multi-login/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}` // Adjust based on your auth system
                },
                body: JSON.stringify({
                    accounts: accountsWithCredentials,
                    headless: true
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('Multi-account login initiated:', result.task_id);
                alert(`Multi-account login started for ${result.total_accounts} accounts. Task ID: ${result.task_id}`);
                setIsMultiLoginOpen(false);
                
                // Refresh accounts after a delay
                setTimeout(() => {
                    onAccountsChange();
                }, 5000);
            } else {
                const error = await response.json();
                console.error('Multi-login error:', error);
                alert(`Multi-login failed: ${error.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Multi-login error:', error);
            alert('Failed to start multi-account login');
        } finally {
            setMultiLoginLoading(false);
        }
    };

    const getStatusColor = (status: AccountStatus) => {
        switch (status) {
            case 'ACTIVE':
                return 'bg-green-100 text-green-800';
            case 'SUSPENDED':
                return 'bg-yellow-100 text-yellow-800';
            case 'BANNED':
                return 'bg-red-100 text-red-800';
            case 'INACTIVE':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const EditAccountForm = () => {
        const updateFormData = (updates: Partial<AccountFormData>) => {
            setFormData(prev => ({ ...prev, ...updates }));
        };

        return (
            <div className="space-y-4">
                <div>
                    <Label htmlFor="username-edit">Username / Email</Label>
                    <Input
                        id="username-edit"
                        value={formData.tiktok_username}
                        onChange={(e) => updateFormData({ tiktok_username: e.target.value })}
                        placeholder="@<NAME_EMAIL>"
                        autoComplete="off"
                        required
                    />
                </div>
                
                <div>
                    <Label htmlFor="email-edit">Email (Optional)</Label>
                    <Input
                        id="email-edit"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData({ email: e.target.value })}
                        placeholder="<EMAIL>"
                        autoComplete="off"
                    />
                </div>
                
                <div>
                    <Label htmlFor="password-edit">Password</Label>
                    <Input
                        id="password-edit"
                        type="password"
                        value={formData.password}
                        onChange={(e) => updateFormData({ password: e.target.value })}
                        placeholder="Enter password"
                        autoComplete="new-password"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                        Leave empty to keep current password
                    </p>
                </div>
                
                <div>
                    <Label htmlFor="status-edit">Status</Label>
                    <Select value={formData.status} onValueChange={(value: AccountStatus) => updateFormData({ status: value })}>
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ACTIVE">Active</SelectItem>
                            <SelectItem value="INACTIVE">Inactive</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                
                <div className="flex justify-end gap-2">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                            setEditingAccount(null);
                            resetForm();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="button"
                        onClick={handleUpdate}
                        disabled={loading || !formData.tiktok_username}
                    >
                        {loading ? 'Saving...' : 'Update'}
                    </Button>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">{accounts.length} accounts</p>
                <div className="flex gap-2">
                    <Dialog open={isLoginOpen} onOpenChange={setIsLoginOpen}>
                        <DialogTrigger asChild>
                            <Button size="sm" variant="outline">
                                <LogIn className="h-4 w-4 mr-2" />
                                Legacy TikTok Login
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                            <DialogHeader>
                                <DialogTitle>Legacy TikTok Login</DialogTitle>
                            </DialogHeader>
                            <TikTokLoginForm
                                onLoginSuccess={handleLoginSuccess}
                                onLoginError={handleLoginError}
                            />
                        </DialogContent>
                    </Dialog>
                    
                    <Dialog open={isMultiLoginOpen} onOpenChange={setIsMultiLoginOpen}>
                        <DialogTrigger asChild>
                            <Button size="sm" variant="outline" disabled={accounts.filter(a => a.status === 'ACTIVE').length === 0}>
                                <Users className="h-4 w-4 mr-2" />
                                Multi-Login
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                            <DialogHeader>
                                <DialogTitle>Multi-Account Login</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <p className="text-sm text-gray-600">
                                    This will attempt to login to all active accounts ({accounts.filter(a => a.status === 'ACTIVE').length} accounts) simultaneously with automatic switching on bot detection.
                                </p>
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                    <p className="text-xs text-yellow-800">
                                        <strong>Note:</strong> This feature requires stored passwords. Make sure your accounts have been properly configured with credentials.
                                    </p>
                                </div>
                                <div className="flex justify-end gap-2">
                                    <Button
                                        variant="outline"
                                        onClick={() => setIsMultiLoginOpen(false)}
                                        disabled={multiLoginLoading}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        onClick={handleMultiAccountLogin}
                                        disabled={multiLoginLoading || accounts.filter(a => a.status === 'ACTIVE').length === 0}
                                    >
                                        {multiLoginLoading ? 'Starting...' : 'Start Multi-Login'}
                                    </Button>
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                    
                    <Link href="/actor/accounts/add">
                        <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Account
                        </Button>
                    </Link>
                </div>
            </div>
            
            <div className="space-y-2 max-h-96 overflow-y-auto">
                {accounts.map((account) => (
                    <Card key={account.id} className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                    {account.profile_image_url ? (
                                        <img
                                            src={account.profile_image_url}
                                            alt={account.tiktok_username}
                                            className="w-10 h-10 rounded-full object-cover"
                                        />
                                    ) : (
                                        <User className="h-5 w-5 text-gray-500" />
                                    )}
                                </div>
                                <div>
                                    <div className="flex items-center gap-2">
                                        <h4 className="font-medium">{account.tiktok_username}</h4>
                                        {account.is_verified && (
                                            <Badge variant="secondary" className="text-xs">
                                                Verified
                                            </Badge>
                                        )}
                                        <Badge className={getStatusColor(account.status)}>
                                            {account.status}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-4 text-xs text-gray-500">
                                        {account.follower_count && (
                                            <span>{account.follower_count.toLocaleString()} followers</span>
                                        )}
                                        {account.video_count && (
                                            <span>{account.video_count} videos</span>
                                        )}
                                        {account.last_login && (
                                            <span className="flex items-center gap-1">
                                                <Activity className="h-3 w-3" />
                                                {new Date(account.last_login).toLocaleDateString()}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                                <Dialog open={editingAccount?.id === account.id} onOpenChange={(open) => {
                                    if (!open) {
                                        setEditingAccount(null);
                                        resetForm();
                                    }
                                }}>
                                    <DialogTrigger asChild>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleEdit(account)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-md">
                                        <DialogHeader>
                                            <DialogTitle>Edit Account</DialogTitle>
                                        </DialogHeader>
                                        <EditAccountForm />
                                    </DialogContent>
                                </Dialog>
                                
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDelete(account.id)}
                                    className="text-red-600 hover:text-red-700"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </Card>
                ))}
                
                {accounts.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No accounts found</p>
                        <p className="text-sm">Add your first TikTok account to get started</p>
                    </div>
                )}
            </div>
        </div>
    );
}