'use client';

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Play, Square, RotateCcw, Trash2, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { createTask, retryTask, cancelTask, deleteTask, getAccounts } from "@/lib/api/actor";
import { api } from "@/lib/axios";
import type { ActorTask, ActorTaskType, TaskStatus, TikTokAccount } from "@/lib/types/actor";

interface TaskListProps {
    tasks: ActorTask[];
    onTasksChange: () => void;
}

interface TaskFormData {
    account_id: number;
    task_type: ActorTaskType;
    keywords: string;
    start_date: string;
    end_date: string;
    max_items: number;
}

export function TaskList({ tasks, onTasksChange }: TaskListProps) {
    const [isCreateOpen, setIsCreateOpen] = useState(false);
    const [formData, setFormData] = useState<TaskFormData>({
        account_id: 0,
        task_type: 'CONTENT_SEARCH',
        keywords: '',
        start_date: '',
        end_date: '',
        max_items: 100
    });
    const [loading, setLoading] = useState(false);
    const [accounts, setAccounts] = useState<TikTokAccount[]>([]);
    const [loadingAccounts, setLoadingAccounts] = useState(false);

    // Fetch accounts when component mounts
    useEffect(() => {
        const fetchAccounts = async () => {
            try {
                setLoadingAccounts(true);
                const accountsData = await getAccounts();
                setAccounts(accountsData);
            } catch (error) {
                console.error('Error fetching accounts:', error);
            } finally {
                setLoadingAccounts(false);
            }
        };

        fetchAccounts();
    }, []);

    const resetForm = () => {
        setFormData({
            account_id: 0,
            task_type: 'CONTENT_SEARCH',
            keywords: '',
            start_date: '',
            end_date: '',
            max_items: 100
        });
    };

    const refreshAccounts = async () => {
        try {
            setLoadingAccounts(true);
            // Get active sessions instead of all accounts
            const response = await api.get('/actor/active-sessions/');
            const data = response.data;

            if (data.success) {
                // Convert session data to account format for compatibility
                const activeAccounts = data.results.map((session: any) => ({
                    id: session.id,
                    username: session.username,
                    tiktok_username: session.tiktok_username,
                    status: 'ACTIVE',
                    is_active: true,
                    last_login: session.login_time,
                    session_expires_at: session.expires_at
                }));
                setAccounts(activeAccounts);
            } else {
                setAccounts([]);
            }
        } catch (error) {
            console.error('Error fetching active sessions:', error);
            setAccounts([]);
        } finally {
            setLoadingAccounts(false);
        }
    };

    const handleDialogOpen = (open: boolean) => {
        setIsCreateOpen(open);
        if (open) {
            refreshAccounts(); // Refresh accounts when dialog opens
        }
    };

    const handleCreate = async () => {
        try {
            setLoading(true);
            await createTask(formData);
            handleDialogOpen(false);
            resetForm();
            onTasksChange();
        } catch (error) {
            console.error('Error creating task:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleRetry = async (taskId: number) => {
        try {
            await retryTask(taskId);
            onTasksChange();
        } catch (error) {
            console.error('Error retrying task:', error);
        }
    };

    const handleCancel = async (taskId: number) => {
        try {
            await cancelTask(taskId);
            onTasksChange();
        } catch (error) {
            console.error('Error cancelling task:', error);
        }
    };

    const handleDelete = async (taskId: number) => {
        if (!confirm('Are you sure you want to delete this task?')) return;
        
        try {
            await deleteTask(taskId);
            onTasksChange();
        } catch (error) {
            console.error('Error deleting task:', error);
        }
    };

    const getStatusIcon = (status: TaskStatus) => {
        switch (status) {
            case 'PENDING':
                return <Clock className="h-4 w-4 text-yellow-500" />;
            case 'RUNNING':
                return <Play className="h-4 w-4 text-blue-500" />;
            case 'COMPLETED':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'FAILED':
                return <XCircle className="h-4 w-4 text-red-500" />;
            case 'CANCELLED':
                return <Square className="h-4 w-4 text-gray-500" />;
            default:
                return <AlertCircle className="h-4 w-4 text-gray-500" />;
        }
    };

    const getStatusColor = (status: TaskStatus) => {
        switch (status) {
            case 'PENDING':
                return 'bg-yellow-100 text-yellow-800';
            case 'RUNNING':
                return 'bg-blue-100 text-blue-800';
            case 'COMPLETED':
                return 'bg-green-100 text-green-800';
            case 'FAILED':
                return 'bg-red-100 text-red-800';
            case 'CANCELLED':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getTaskTypeLabel = (type: ActorTaskType) => {
        switch (type) {
            case 'MY_VIDEOS':
                return 'My Videos';
            case 'MY_FOLLOWERS':
                return 'My Followers';
            case 'MY_FOLLOWING':
                return 'My Following';
            case 'MY_LIKES':
                return 'My Likes';
            case 'FEED_SCRAPE':
                return 'Feed Scrape';
            case 'TARGETED_USER':
                return 'Targeted User';
            case 'HASHTAG_ANALYSIS':
                return 'Hashtag Analysis';
            case 'COMPETITOR_ANALYSIS':
                return 'Competitor Analysis';
            default:
                return type;
        }
    };

    const formatDuration = (startedAt: string | null, completedAt: string | null) => {
        if (!startedAt) return 'Not started';
        
        const start = new Date(startedAt);
        const end = completedAt ? new Date(completedAt) : new Date();
        const duration = Math.floor((end.getTime() - start.getTime()) / 1000);
        
        if (duration < 60) return `${duration}s`;
        if (duration < 3600) return `${Math.floor(duration / 60)}m ${duration % 60}s`;
        return `${Math.floor(duration / 3600)}h ${Math.floor((duration % 3600) / 60)}m`;
    };

    // Filter accounts to only show active ones
    const availableAccounts = accounts.filter(account => account.status === 'ACTIVE');

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <p className="text-sm text-gray-600">{tasks.length} tasks</p>
                <Dialog open={isCreateOpen} onOpenChange={handleDialogOpen}>
                    <DialogTrigger asChild>
                        <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Create Task
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>Create New Task</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="account">Account</Label>
                                <Select
                                    value={formData.account_id.toString()}
                                    onValueChange={(value) => setFormData({ ...formData, account_id: parseInt(value) })}
                                    disabled={loadingAccounts}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder={loadingAccounts ? "Loading accounts..." : availableAccounts.length === 0 ? "No accounts available" : "Select account"} />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableAccounts.map((account) => (
                                            <SelectItem key={account.id} value={account.id.toString()}>
                                                {account.tiktok_username || account.username} ({account.status})
                                            </SelectItem>
                                        ))}
                                        {availableAccounts.length === 0 && !loadingAccounts && (
                                            <SelectItem value="no-accounts" disabled>
                                                No active accounts found
                                            </SelectItem>
                                        )}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <Label htmlFor="task_type">Task Type</Label>
                                <Select
                                    value={formData.task_type}
                                    onValueChange={(value: ActorTaskType) => setFormData({ ...formData, task_type: value })}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="MY_VIDEOS">My Videos</SelectItem>
                                        <SelectItem value="MY_FOLLOWERS">My Followers</SelectItem>
                                        <SelectItem value="MY_FOLLOWING">My Following</SelectItem>
                                        <SelectItem value="MY_LIKES">My Liked Videos</SelectItem>
                                        <SelectItem value="FEED_SCRAPE">Feed Scraping</SelectItem>
                                        <SelectItem value="TARGETED_USER">Targeted User Analysis</SelectItem>
                                        <SelectItem value="HASHTAG_ANALYSIS">Hashtag Analysis</SelectItem>
                                        <SelectItem value="COMPETITOR_ANALYSIS">Competitor Analysis</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div>
                                <Label htmlFor="keywords">Search Keywords</Label>
                                <Input
                                    id="keywords"
                                    value={formData.keywords}
                                    onChange={(e) => setFormData({ ...formData, keywords: e.target.value })}
                                    placeholder="Enter keywords to search (e.g., prabowo, jokowi, viral)"
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="start_date">Start Date (Optional)</Label>
                                    <Input
                                        id="start_date"
                                        type="date"
                                        value={formData.start_date}
                                        onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="end_date">End Date (Optional)</Label>
                                    <Input
                                        id="end_date"
                                        type="date"
                                        value={formData.end_date}
                                        onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                                    />
                                </div>
                            </div>
                            
                            <div>
                                <Label htmlFor="max_items">Max Items</Label>
                                <Input
                                    id="max_items"
                                    type="number"
                                    value={formData.max_items}
                                    onChange={(e) => setFormData({ ...formData, max_items: parseInt(e.target.value) || 100 })}
                                    min="1"
                                    max="10000"
                                />
                            </div>
                            
                            <div className="flex justify-end gap-2">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        handleDialogOpen(false);
                                        resetForm();
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleCreate}
                                    disabled={loading || !formData.account_id || !formData.keywords || availableAccounts.length === 0}
                                >
                                    {loading ? 'Creating...' : 'Create Task'}
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
            
            <div className="space-y-2 max-h-96 overflow-y-auto">
                {tasks.map((task) => (
                    <Card key={task.id} className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex-1">
                                <div className="flex items-center gap-3 mb-2">
                                    {getStatusIcon(task.status)}
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <h4 className="font-medium">
                                                {getTaskTypeLabel(task.task_type)} - {task.parameters?.keywords || 'No keywords'}
                                            </h4>
                                            <Badge className={getStatusColor(task.status)}>
                                                {task.status}
                                            </Badge>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Account: {task.tiktok_username || 'Legacy Account'}
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-3 gap-4 text-xs text-gray-500">
                                    <div>
                                        <span className="font-medium">Progress:</span>
                                        <br />
                                        {task.items_scraped} / {task.max_items || '∞'}
                                    </div>
                                    <div>
                                        <span className="font-medium">Duration:</span>
                                        <br />
                                        {formatDuration(task.started_at, task.completed_at)}
                                    </div>
                                    <div>
                                        <span className="font-medium">Created:</span>
                                        <br />
                                        {new Date(task.created_at).toLocaleDateString()}
                                    </div>
                                </div>
                                
                                {task.error_message && (
                                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                                        {task.error_message}
                                    </div>
                                )}
                            </div>
                            
                            <div className="flex items-center gap-2 ml-4">
                                {task.status === 'FAILED' && (
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleRetry(task.id)}
                                    >
                                        <RotateCcw className="h-4 w-4" />
                                    </Button>
                                )}
                                
                                {task.status === 'RUNNING' && (
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleCancel(task.id)}
                                    >
                                        <Square className="h-4 w-4" />
                                    </Button>
                                )}
                                
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDelete(task.id)}
                                    className="text-red-600 hover:text-red-700"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </Card>
                ))}
                
                {tasks.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No tasks found</p>
                        <p className="text-sm">Create your first task to start scraping</p>
                    </div>
                )}
            </div>
        </div>
    );
}