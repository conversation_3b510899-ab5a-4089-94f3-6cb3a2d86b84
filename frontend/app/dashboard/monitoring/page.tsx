"use client";

import React from 'react';
import { useCrawler<PERSON><PERSON> } from '@/hooks/useCrawlerApi';
import StatsCard from '@/components/ui/StateCard';
import LineChart from '@/components/ui/LineChart';
// import DataTable from '@/components/ui/DataTable';
import { DataTable } from '@/components/ui/DataTable';
import { 
  ActivityIcon, 
  CheckCircleIcon, 
  ClockIcon, 
  XCircleIcon,
  ServerIcon,
  BarChartIcon
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { PageHeader } from "@/components/themes/page-header";

const MonitoringPage = () => {
  const { getDashboardStats, getActiveTasks, getPlatformStats, getTaskHistory, loading } = useCrawlerApi();
  const [dashboardStats, setDashboardStats] = React.useState<any>(null);
  const [activeTasks, setActiveTasks] = React.useState<any[]>([]);
  const [platformStats, setPlatformStats] = React.useState<any>(null);
  const [taskHistory, setTaskHistory] = React.useState<any[]>([]);

  React.useEffect(() => {
    const fetchData = async () => {
      const [statsRes, tasksRes, platformsRes, historyRes] = await Promise.all([
        getDashboardStats(),
        getActiveTasks(),
        getPlatformStats(),
        getTaskHistory()
      ]);
      setDashboardStats(statsRes.data);
      setActiveTasks(tasksRes.data);
      setPlatformStats(platformsRes.data);
      setTaskHistory(historyRes.data);
    };
    fetchData();
  }, []);

  // Sample data for the chart
  const taskData = [
    { date: '2023-06-01', tasks: 12 },
    { date: '2023-06-02', tasks: 15 },
    { date: '2023-06-03', tasks: 8 },
    { date: '2023-06-04', tasks: 20 },
    { date: '2023-06-05', tasks: 18 },
    { date: '2023-06-06', tasks: 25 },
    { date: '2023-06-07', tasks: 22 },
  ];

  return (
    <>
      <PageHeader
        breadcrumbs={[
          { href: "/", label: "Home" },
          { href: "/dashboard", label: "Dashboard" },
          { label: "Monitoring", current: true }
        ]}
      />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="space-y-6">
          <h1 className="text-3xl font-bold tracking-tight">Crawler Monitoring</h1>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[1, 2, 3, 4].map(i => (
                <Card key={i}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-6 w-24 mb-2" />
                    <Skeleton className="h-4 w-16" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : dashboardStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                  <div className="h-8 w-8 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center">
                    <ActivityIcon className="h-6 w-6" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.task_stats.total}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Tasks</CardTitle>
                  <div className="h-8 w-8 rounded-full bg-yellow-100 text-yellow-800 flex items-center justify-center">
                    <ClockIcon className="h-6 w-6" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.task_stats.running}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Completed</CardTitle>
                  <div className="h-8 w-8 rounded-full bg-green-100 text-green-800 flex items-center justify-center">
                    <CheckCircleIcon className="h-6 w-6" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.task_stats.completed}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Failed</CardTitle>
                  <div className="h-8 w-8 rounded-full bg-red-100 text-red-800 flex items-center justify-center">
                    <XCircleIcon className="h-6 w-6" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{dashboardStats.task_stats.failed}</div>
                </CardContent>
              </Card>
            </div>
          )}
          {dashboardStats && (
            <>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl shadow p-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Task Activity</h2>
                  <LineChart 
                    data={taskHistory} 
                    xKey="date" 
                    yKey="tasks" 
                    height={300}
                  />
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Storage Stats</h2>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-gray-600 dark:text-gray-300">Total Storage</span>
                        <span className="font-medium">{dashboardStats.storage_stats.total_size_mb} MB</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-indigo-600 h-2.5 rounded-full" 
                          style={{ width: '85%' }}
                        ></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-gray-600 dark:text-gray-300">Compression Ratio</span>
                        <span className="font-medium">
                          {dashboardStats.storage_stats.avg_ratio.toFixed(2)}:1
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-green-600 h-2.5 rounded-full" 
                          style={{ width: `${dashboardStats.storage_stats.avg_ratio * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">Platform Distribution</h3>
                    <div className="space-y-2">
                      {platformStats && Object.entries(platformStats.platforms).map(([platform, stats]: [string, any]) => (
                        <div key={platform} className="flex items-center">
                          <div className="w-24 text-gray-600 dark:text-gray-300">{platform}</div>
                          <div className="flex-1 ml-2">
                            <div className="flex justify-between text-sm mb-1">
                              <span>{stats.tasks} tasks</span>
                              <span>{Math.round(stats.avg_size / 1024)} KB avg</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-indigo-600 h-2 rounded-full" 
                                style={{ width: `${(stats.tasks / dashboardStats.task_stats.total) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow p-6 mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Active Tasks</h2>
                {activeTasks.length > 0 ? (
                  <DataTable
                    columns={[
                      { header: 'Task ID', accessorKey: 'id' },
                      { header: 'Project', accessorKey: 'schedule__project__name' },
                      { header: 'Started', accessorKey: 'started_at', cell: ({ getValue }) => new Date(getValue() as string).toLocaleTimeString() },
                      { header: 'Status', accessorKey: 'status', cell: ({ getValue }) => {
                        const value = getValue() as string;
                        return (
                          <Badge variant={value === 'running' ? 'secondary' : value === 'completed' ? 'default' : 'destructive'}>
                            {value}
                          </Badge>
                        );
                      }}
                    ]}
                    data={activeTasks}
                    searchKey="id"
                    pageSize={5}
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No active tasks at the moment
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default MonitoringPage;