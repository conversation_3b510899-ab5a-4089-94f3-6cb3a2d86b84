'use client';

import { useEffect, useState } from "react";
import { EnhancedDataList } from "@/components/actor/EnhancedDataList";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Database, User, Video, Users, Search, Eye, Download, Filter, Activity } from "lucide-react";
import { getScrapedData } from "@/lib/api/actor";
import { getActorAccounts, getActorScrapedData, getDataLabelingStats, type ActorAccount } from "@/lib/api/actor-system";
import type { ScrapedData } from "@/lib/types/actor";

type DataType = 'PROFILE' | 'VIDEO' | 'FOLLOWER' | 'FOLLOWING' | 'ALL';

export default function DataPage() {
    // Legacy system state
    const [allData, setAllData] = useState<ScrapedData[]>([]);
    const [filteredData, setFilteredData] = useState<ScrapedData[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);
    const [selectedItem, setSelectedItem] = useState<ScrapedData | null>(null);

    // New Actor system state
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [showNewSystem, setShowNewSystem] = useState(true);
    
    // Pagination
    const [currentPage, setCurrentPage] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [hasNext, setHasNext] = useState(false);
    const [hasPrevious, setHasPrevious] = useState(false);
    const pageSize = 20;
    
    // Filters
    const [dataTypeFilter, setDataTypeFilter] = useState<DataType>('ALL');
    const [searchQuery, setSearchQuery] = useState('');
    const [taskIdFilter, setTaskIdFilter] = useState('');

    const fetchData = async (page = 1) => {
        try {
            setLoading(true);
            setError(null);
            
            const params: any = {
                page,
                page_size: pageSize
            };
            
            if (dataTypeFilter !== 'ALL') {
                params.data_type = dataTypeFilter;
            }
            
            if (taskIdFilter) {
                params.task_id = parseInt(taskIdFilter);
            }
            
            const response = await getScrapedData(params);
            setAllData(response.results);
            setTotalCount(response.count);
            setHasNext(!!response.next);
            setHasPrevious(!!response.previous);
        } catch (err: any) {
            const errorMessage = err.response?.status === 404
                ? 'API endpoint not found - this might be a temporary issue'
                : 'Failed to fetch scraped data';
            setError(errorMessage);
            console.error('Error fetching scraped data:', {
                message: err.message,
                status: err.response?.status,
                url: err.config?.url,
                params: params,
                timestamp: new Date().toISOString()
            });
        } finally {
            setLoading(false);
        }
    };

    const loadActorData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Load accounts
            const accountsData = await getActorAccounts();
            setAccounts(accountsData);

            // Load scraped data with filters
            const filters: any = {};
            if (dataTypeFilter !== 'ALL') {
                filters.data_type = dataTypeFilter;
            }
            if (taskIdFilter) {
                filters.task_id = parseInt(taskIdFilter);
            }

            try {
                const scrapedDataResponse = await getActorScrapedData(filters);
                // Convert Actor system data to legacy format for compatibility
                const convertedData = scrapedDataResponse.results?.map((item: any) => ({
                    id: item.id,
                    tiktok_id: item.platform_content_id,
                    author_username: item.content?.author_username || 'Unknown',
                    description: item.content?.description || '',
                    video_url: item.content?.video_url || '',
                    scraped_at: item.scraped_at,
                    task: {
                        id: item.task_id,
                        target_username: item.content?.target_username || ''
                    },
                    platform: item.platform,
                    data_type: item.data_type,
                    quality_score: item.quality_score
                })) || [];

                setAllData(convertedData);
                setTotalCount(scrapedDataResponse.count || 0);
            } catch (dataError) {
                console.warn('Failed to load scraped data:', dataError);
                setAllData([]);
                setTotalCount(0);
            }

        } catch (err: any) {
            setError(err.response?.data?.error || 'Failed to fetch Actor data');
            console.error('Error fetching Actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    // Apply client-side search filter
    useEffect(() => {
        let filtered = allData;
        
        if (searchQuery) {
            filtered = filtered.filter(item =>
                item.author_username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.tiktok_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.task?.target_username?.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }
        
        setFilteredData(filtered);
    }, [allData, searchQuery]);

    useEffect(() => {
        if (showNewSystem) {
            loadActorData();
        } else {
            fetchData(currentPage);
        }
    }, [currentPage, dataTypeFilter, taskIdFilter, showNewSystem]);

    const getDataStats = () => {
        const total = totalCount;
        const profiles = allData.filter(item => item.data_type === 'PROFILE').length;
        const videos = allData.filter(item => item.data_type === 'VIDEO').length;
        const followers = allData.filter(item => item.data_type === 'FOLLOWER').length;
        const following = allData.filter(item => item.data_type === 'FOLLOWING').length;
        
        return { total, profiles, videos, followers, following };
    };

    const stats = getDataStats();

    const getDataTypeIcon = (type: string) => {
        switch (type) {
            case 'PROFILE':
                return <User className="h-4 w-4" />;
            case 'VIDEO':
                return <Video className="h-4 w-4" />;
            case 'FOLLOWER':
            case 'FOLLOWING':
                return <Users className="h-4 w-4" />;
            default:
                return <Database className="h-4 w-4" />;
        }
    };

    const getDataTypeColor = (type: string) => {
        switch (type) {
            case 'PROFILE':
                return 'bg-blue-100 text-blue-800';
            case 'VIDEO':
                return 'bg-purple-100 text-purple-800';
            case 'FOLLOWER':
                return 'bg-green-100 text-green-800';
            case 'FOLLOWING':
                return 'bg-orange-100 text-orange-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatContent = (content: any, type: string) => {
        if (!content) {
            return {
                title: 'No content',
                subtitle: type,
                details: 'No data available'
            };
        }
        
        try {
            const data = typeof content === 'string' ? JSON.parse(content) : content;
            
            switch (type) {
                case 'PROFILE':
                    return {
                        title: data.display_name || data.username || 'Unknown',
                        subtitle: `@${data.username || 'unknown'}`,
                        details: `${data.follower_count || 0} followers • ${data.video_count || 0} videos`
                    };
                case 'VIDEO':
                    return {
                        title: data.description || 'No description',
                        subtitle: `@${data.author || 'unknown'}`,
                        details: `${data.view_count || 0} views • ${data.like_count || 0} likes`
                    };
                case 'FOLLOWER':
                case 'FOLLOWING':
                    return {
                        title: data.display_name || data.username || 'Unknown',
                        subtitle: `@${data.username || 'unknown'}`,
                        details: `${data.follower_count || 0} followers`
                    };
                default:
                    return {
                        title: 'Data Item',
                        subtitle: type,
                        details: 'Raw data'
                    };
            }
        } catch {
            return {
                title: 'Invalid Data',
                subtitle: type,
                details: 'Unable to parse content'
            };
        }
    };

    const clearFilters = () => {
        setDataTypeFilter('ALL');
        setSearchQuery('');
        setTaskIdFilter('');
        setCurrentPage(1);
    };

    const exportData = () => {
        const dataStr = JSON.stringify(filteredData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `actor_scraped_data_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
    };

    if (loading && currentPage === 1) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[100px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold mb-2">
                        {showNewSystem ? 'My Data' : 'Legacy Data'}
                    </h1>
                    <p className="text-gray-600">
                        {showNewSystem
                            ? 'View and manage data collected from social media platforms'
                            : 'Legacy TikTok-specific scraped data'
                        }
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button
                        variant={showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(true)}
                    >
                        <Activity className="h-4 w-4 mr-2" />
                        Actor System
                    </Button>
                    <Button
                        variant={!showNewSystem ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowNewSystem(false)}
                    >
                        Legacy TikTok
                    </Button>
                    <Button onClick={exportData} disabled={filteredData.length === 0}>
                        <Download className="h-4 w-4 mr-2" />
                        Export Data
                    </Button>
                </div>
            </div>
            
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Database className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Items</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <User className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Profiles</p>
                            <p className="text-2xl font-bold">{stats.profiles}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Video className="h-8 w-8 text-purple-500" />
                        <div>
                            <p className="text-sm text-gray-500">Videos</p>
                            <p className="text-2xl font-bold">{stats.videos}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Users className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Followers</p>
                            <p className="text-2xl font-bold">{stats.followers}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Users className="h-8 w-8 text-orange-500" />
                        <div>
                            <p className="text-sm text-gray-500">Following</p>
                            <p className="text-2xl font-bold">{stats.following}</p>
                        </div>
                    </div>
                </Card>
            </div>


            {/* Data Content */}
            {showNewSystem ? (
                <EnhancedDataList accounts={accounts} />
            ) : (
                <>
                    {/* Legacy System Filters */}
                    <Card className="p-4">
                        <div className="flex flex-wrap items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Filter className="h-4 w-4 text-gray-500" />
                                <span className="text-sm font-medium">Legacy Filters:</span>
                            </div>

                            <div className="flex items-center gap-2">
                                <Search className="h-4 w-4 text-gray-500" />
                                <Input
                                    placeholder="Search data..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-48"
                                />
                            </div>

                            <Select value={dataTypeFilter} onValueChange={(value: DataType) => setDataTypeFilter(value)}>
                                <SelectTrigger className="w-32">
                                    <SelectValue placeholder="Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ALL">All Types</SelectItem>
                                    <SelectItem value="PROFILE">Profile</SelectItem>
                                    <SelectItem value="VIDEO">Video</SelectItem>
                                    <SelectItem value="FOLLOWER">Follower</SelectItem>
                                    <SelectItem value="FOLLOWING">Following</SelectItem>
                                </SelectContent>
                            </Select>

                            <Input
                                placeholder="Task ID..."
                                value={taskIdFilter}
                                onChange={(e) => setTaskIdFilter(e.target.value)}
                                className="w-24"
                                type="number"
                            />

                            <Button variant="outline" onClick={clearFilters} size="sm">
                                Clear Filters
                            </Button>

                            <div className="ml-auto">
                                <Badge variant="outline">
                                    {filteredData.length} of {totalCount} items
                                </Badge>
                            </div>
                        </div>
                    </Card>

                    {/* Legacy Data Grid */}
                    <Card className="p-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredData.map((item) => {
                        const formatted = formatContent(item.content, item.data_type);
                        return (
                            <Card key={item.id} className="p-4 hover:shadow-md transition-shadow">
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-2">
                                        {getDataTypeIcon(item.data_type)}
                                        <Badge className={getDataTypeColor(item.data_type)}>
                                            {item.data_type}
                                        </Badge>
                                    </div>
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <Button variant="outline" size="sm">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                            <DialogHeader>
                                                <DialogTitle>Data Details</DialogTitle>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div className="grid grid-cols-2 gap-4 text-sm">
                                                    <div>
                                                        <span className="font-medium">Type:</span> {item.data_type}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Task ID:</span> {item.task?.id || 'N/A'}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">TikTok ID:</span> {item.tiktok_id || 'N/A'}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Author:</span> {item.author_username || 'N/A'}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Quality Score:</span> {item.quality_score || 'N/A'}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Complete:</span> {item.is_complete ? 'Yes' : 'No'}
                                                    </div>
                                                </div>
                                                <div>
                                                    <span className="font-medium">Raw Content:</span>
                                                    <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-x-auto">
                                                        {JSON.stringify(item.content, null, 2)}
                                                    </pre>
                                                </div>
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                </div>
                                
                                <div className="space-y-2">
                                    <h4 className="font-medium text-sm line-clamp-2">{formatted.title}</h4>
                                    <p className="text-xs text-gray-600">{formatted.subtitle}</p>
                                    <p className="text-xs text-gray-500">{formatted.details}</p>
                                </div>
                                
                                <div className="mt-3 pt-3 border-t text-xs text-gray-500">
                                    <div className="flex justify-between">
                                        <span>Task: {item.task?.id || 'N/A'}</span>
                                        <span>{new Date(item.scraped_at).toLocaleDateString()}</span>
                                    </div>
                                </div>
                            </Card>
                        );
                    })}
                </div>
                
                {filteredData.length === 0 && (
                    <div className="text-center py-12 text-gray-500">
                        <Database className="h-16 w-16 mx-auto mb-4 opacity-50" />
                        <p className="text-lg">No data found</p>
                        <p className="text-sm">Try adjusting your filters or run some tasks to collect data</p>
                    </div>
                )}
                
                {/* Pagination */}
                {totalCount > pageSize && (
                    <div className="flex justify-between items-center mt-6 pt-6 border-t">
                        <div className="text-sm text-gray-600">
                            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} items
                        </div>
                        <div className="flex gap-2">
                            <Button
                                variant="outline"
                                onClick={() => setCurrentPage(currentPage - 1)}
                                disabled={!hasPrevious || loading}
                            >
                                Previous
                            </Button>
                            <Button
                                variant="outline"
                                onClick={() => setCurrentPage(currentPage + 1)}
                                disabled={!hasNext || loading}
                            >
                                Next
                            </Button>
                        </div>
                    </div>
                )}
                    </Card>
                </>
            )}
        </div>
    );
}