'use client';

import { useEffect, useState } from "react";
import { AccountList } from "@/components/actor/AccountList";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, UserX, Clock } from "lucide-react";
import { getAccounts } from "@/lib/api/actor";
import { useAuthGuard } from "@/hooks/useAuthGuard";
import type { TikTokAccount } from "@/lib/types/actor";

export default function AccountsPage() {
    const { user, isLoading: authLoading } = useAuthGuard();
    const [accounts, setAccounts] = useState<TikTokAccount[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    const fetchAccounts = async () => {
        try {
            setLoading(true);
            setError(null);
            const data = await getAccounts();
            setAccounts(data);
        } catch (err) {
            setError('Failed to fetch accounts');
            console.error('Error fetching accounts:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchAccounts();
    }, []);

    const getAccountStats = () => {
        const total = accounts.length;
        const active = accounts.filter(acc => acc.status === 'ACTIVE').length;
        const suspended = accounts.filter(acc => acc.status === 'SUSPENDED').length;
        const banned = accounts.filter(acc => acc.status === 'BANNED').length;
        const verified = accounts.filter(acc => acc.is_verified).length;
        
        return { total, active, suspended, banned, verified };
    };

    const stats = getAccountStats();

    // Show loading while authenticating or fetching data
    if (authLoading || loading) {
        return (
            <div className="space-y-4 p-8">
                <Skeleton className="h-[100px] w-full" />
                <Skeleton className="h-[400px] w-full" />
            </div>
        );
    }

    // If not authenticated, the useAuthGuard will redirect to login
    if (!user) {
        return null;
    }

    if (error) {
        return (
            <Alert variant="destructive" className="m-8">
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div>
                <h1 className="text-2xl font-bold mb-2">My Accounts</h1>
                <p className="text-gray-600">Manage your social media accounts across platforms</p>
            </div>
            
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Users className="h-8 w-8 text-blue-500" />
                        <div>
                            <p className="text-sm text-gray-500">Total Accounts</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <UserCheck className="h-8 w-8 text-green-500" />
                        <div>
                            <p className="text-sm text-gray-500">Active</p>
                            <p className="text-2xl font-bold">{stats.active}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Clock className="h-8 w-8 text-yellow-500" />
                        <div>
                            <p className="text-sm text-gray-500">Suspended</p>
                            <p className="text-2xl font-bold">{stats.suspended}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <UserX className="h-8 w-8 text-red-500" />
                        <div>
                            <p className="text-sm text-gray-500">Banned</p>
                            <p className="text-2xl font-bold">{stats.banned}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-3">
                        <Badge className="h-8 w-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">
                            ✓
                        </Badge>
                        <div>
                            <p className="text-sm text-gray-500">Verified</p>
                            <p className="text-2xl font-bold">{stats.verified}</p>
                        </div>
                    </div>
                </Card>
            </div>
            
            {/* Accounts List */}
            <Card className="p-6">
                <AccountList accounts={accounts} onAccountsChange={fetchAccounts} />
            </Card>
        </div>
    );
}