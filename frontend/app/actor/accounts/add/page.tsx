'use client';

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import { createAccount } from "@/lib/api/actor";
import type { AccountStatus } from "@/lib/types/actor";

interface AccountFormData {
    tiktok_username: string;
    password: string;
    status: AccountStatus;
}

export default function AddAccountPage() {
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState<AccountFormData>({
        tiktok_username: '',
        password: '',
        status: 'ACTIVE'
    });

    const updateFormData = (updates: Partial<AccountFormData>) => {
        setFormData(prev => ({ ...prev, ...updates }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!formData.tiktok_username || !formData.password) {
            alert('Please fill in all required fields');
            return;
        }

        try {
            setLoading(true);
            await createAccount(formData);
            router.push('/actor/accounts');
        } catch (error) {
            console.error('Error creating account:', error);
            alert('Failed to create account. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        router.push('/actor/accounts');
    };

    return (
        <div className="container mx-auto py-6">
            <div className="max-w-2xl mx-auto">
                <div className="mb-6">
                    <Button
                        variant="ghost"
                        onClick={handleCancel}
                        className="mb-4"
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Accounts
                    </Button>
                    
                    <h1 className="text-3xl font-bold">Add TikTok Account</h1>
                    <p className="text-gray-600 mt-2">
                        Add a new TikTok account to your collection for content management.
                    </p>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Account Details</CardTitle>
                        <CardDescription>
                            Enter the details for your TikTok account. All fields marked with * are required.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="username">TikTok Username *</Label>
                                <Input
                                    id="username"
                                    value={formData.tiktok_username}
                                    onChange={(e) => updateFormData({ tiktok_username: e.target.value })}
                                    placeholder="@username"
                                    autoComplete="off"
                                    required
                                />
                                <p className="text-xs text-gray-500">
                                    Enter your TikTok username (with @).
                                </p>
                            </div>
                            

                            <div className="space-y-2">
                                <Label htmlFor="password">Password *</Label>
                                <Input
                                    id="password"
                                    type="password"
                                    value={formData.password}
                                    onChange={(e) => updateFormData({ password: e.target.value })}
                                    placeholder="Enter password"
                                    autoComplete="new-password"
                                    required
                                />
                                <p className="text-xs text-gray-500">
                                    Your TikTok account password. This will be stored securely.
                                </p>
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="status">Status</Label>
                                <Select value={formData.status} onValueChange={(value: AccountStatus) => updateFormData({ status: value })}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="ACTIVE">Active</SelectItem>
                                        <SelectItem value="INACTIVE">Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p className="text-xs text-gray-500">
                                    Set the initial status for this account.
                                </p>
                            </div>
                            
                            <div className="flex justify-end gap-3 pt-6">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancel}
                                    disabled={loading}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={loading || !formData.tiktok_username || !formData.password}
                                >
                                    {loading ? 'Creating Account...' : 'Create Account'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}