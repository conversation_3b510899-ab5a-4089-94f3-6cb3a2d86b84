'use client';

import { useEffect, useState } from "react";
import { EnhancedTaskList } from "@/components/actor/EnhancedTaskList";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Play, Clock, CheckCircle, XCircle, Square, Search, Filter, Target, Activity, RefreshCw } from "lucide-react";
import { getActorAccounts, getActorTasks, type ActorAccount, type ActorTask } from "@/lib/api/actor-system";

export default function TasksPage() {
    // Actor system state
    const [tasks, setTasks] = useState<ActorTask[]>([]);
    const [accounts, setAccounts] = useState<ActorAccount[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);

    // Filters
    const [statusFilter, setStatusFilter] = useState<string>('ALL');
    const [typeFilter, setTypeFilter] = useState<string>('ALL');
    const [searchQuery, setSearchQuery] = useState('');

    const loadData = async () => {
        try {
            setLoading(true);
            setError(null);

            const accountsData = await getActorAccounts();
            setAccounts(accountsData);

            // Load Actor tasks
            try {
                const tasksData = await getActorTasks();
                setTasks(tasksData);
            } catch (taskError) {
                console.warn('Failed to load Actor tasks, using mock data:', taskError);
                // Fallback to mock data if API fails
                const mockTasks: ActorTask[] = accountsData.map((account, index) => ({
                    id: index + 1,
                    name: `Sample Task for @${account.username}`,
                    type: 'CONTENT_SEARCH',
                    platform: account.platform,
                    status: index % 3 === 0 ? 'COMPLETED' : index % 3 === 1 ? 'RUNNING' : 'PENDING',
                    created_at: new Date().toISOString(),
                    keywords: 'sample, keywords',
                    max_items: 50
                }));
                setTasks(mockTasks);
            }

        } catch (err: any) {
            setError(err.response?.data?.error || 'Failed to fetch Actor data');
            console.error('Error fetching Actor data:', err);
        } finally {
            setLoading(false);
        }
    };

    // Filter tasks based on current filters
    const getFilteredTasks = () => {
        let filtered = tasks;
        
        // Status filter
        if (statusFilter !== 'ALL') {
            filtered = filtered.filter(task => task.status === statusFilter);
        }
        
        // Type filter
        if (typeFilter !== 'ALL') {
            filtered = filtered.filter(task => task.type === typeFilter);
        }
        
        // Search filter
        if (searchQuery) {
            filtered = filtered.filter(task => 
                task.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                task.keywords?.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }
        
        return filtered;
    };

    const filteredTasks = getFilteredTasks();

    useEffect(() => {
        loadData();

        // Refresh data every 30 seconds
        const interval = setInterval(() => {
            loadData();
        }, 30000);

        return () => clearInterval(interval);
    }, []);

    const getTaskStats = () => {
        const total = tasks.length;
        const pending = tasks.filter(t => t.status === 'PENDING').length;
        const running = tasks.filter(t => t.status === 'RUNNING').length;
        const completed = tasks.filter(t => t.status === 'COMPLETED').length;
        const failed = tasks.filter(t => t.status === 'FAILED').length;
        const platforms = new Set(tasks.map(t => t.platform)).size;

        return { total, pending, running, completed, failed, platforms };
    };

    const stats = getTaskStats();

    if (loading) {
        return (
            <div className="container mx-auto p-6 space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Task Management</h1>
                        <p className="text-gray-600 mt-2">Manage and monitor your automation tasks</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    {[...Array(5)].map((_, i) => (
                        <Card key={i} className="p-4">
                            <Skeleton className="h-4 w-16 mb-2" />
                            <Skeleton className="h-8 w-12" />
                        </Card>
                    ))}
                </div>

                <Card className="p-6">
                    <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                            <div key={i} className="flex items-center space-x-4">
                                <Skeleton className="h-12 w-12 rounded-full" />
                                <div className="space-y-2 flex-1">
                                    <Skeleton className="h-4 w-1/4" />
                                    <Skeleton className="h-4 w-1/2" />
                                </div>
                                <Skeleton className="h-8 w-20" />
                            </div>
                        ))}
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className="container mx-auto p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">Task Management</h1>
                    <p className="text-gray-600 mt-2">Manage and monitor your automation tasks across all platforms</p>
                </div>
                <Button onClick={loadData} variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                </Button>
            </div>

            {/* Error Alert */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Target className="h-5 w-5 text-blue-600" />
                        <div>
                            <p className="text-sm text-gray-600">Total Tasks</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-yellow-600" />
                        <div>
                            <p className="text-sm text-gray-600">Pending</p>
                            <p className="text-2xl font-bold">{stats.pending}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Play className="h-5 w-5 text-blue-600" />
                        <div>
                            <p className="text-sm text-gray-600">Running</p>
                            <p className="text-2xl font-bold">{stats.running}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <div>
                            <p className="text-sm text-gray-600">Completed</p>
                            <p className="text-2xl font-bold">{stats.completed}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <XCircle className="h-5 w-5 text-red-600" />
                        <div>
                            <p className="text-sm text-gray-600">Failed</p>
                            <p className="text-2xl font-bold">{stats.failed}</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4">
                    <div className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-purple-600" />
                        <div>
                            <p className="text-sm text-gray-600">Platforms</p>
                            <p className="text-2xl font-bold">{stats.platforms}</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Enhanced Task List */}
            <EnhancedTaskList 
                tasks={filteredTasks} 
                onTasksChange={loadData}
            />
        </div>
    );
}
